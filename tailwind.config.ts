import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        stalbla: "#EBEEF3",
        stalbla700: "#2E3C51",
        eggskall400: "#F2E8DA",
        orange: "#FB8C00",
        mint: "#E3F2EB",
        header: "#FBF3E8",
        // Udir Green
        udirGreen: {
          100: "#DEF2E7",
          150: "#C9E5D7",
          200: "#B9E1CC",
          300: "#94CAAE",
          400: "#7DBF9D",
          500: "#3B7858",
          600: "#447266",
          700: "#254B38",
        },
        // Udir Eggshell
        udirEggshell: {
          100: "#F6EFE4",
          200: "#F2EBDA",
          300: "#DBC7AC",
          400: "#D6B689",
          500: "#BFA687",
          600: "#8C6631",
          700: "#5E4521",
        },
        // Udir Peach
        udirPeach: {
          100: "#F8E9DF",
          200: "#EED0C3",
          300: "#E2BAA7",
          400: "#D6A68B",
          500: "#C98E6F",
          600: "#87532F",
          700: "#63321C",
        },
        // Udir Light Azure
        udirLightAzure: {
          100: "#EAEAF5",
          200: "#E2E3F1",
          300: "#C7C9E3",
          400: "#9497CB",
          500: "#666883",
          600: "#494E96",
          700: "#2A2C56",
        },
        // Udir Steel Blue
        udirSteelBlue: {
          100: "#DCE5F3",
          200: "#BAC6D8",
          300: "#99ABC5",
          400: "#8B9CB5",
          500: "#51698F",
          600: "#4E6689",
          700: "#2E3C51",
        },
        // Udir Slate
        udirSlate: {
          50: "#ECEFF2",
          100: "#D9E5F1",
          200: "#B7CCDC",
          300: "#A0B1BF",
          400: "#6D889D",
          500: "#546B7D",
          600: "#2F4960",
          700: "#191D24",
        },
        // Udir Dark Blue
        udirDarkBlue: {
          DEFAULT: "#00468E",
        },
        udirGray: {
          100: "#F4F4F4",
          200: "#EAEAEA",
          300: "#D9D9D9",
          350: "#CFD8DF",
          400: "#BFBFBF",
          500: "#A6A6A6",
          600: "#8C8C8C",
          700: "#737373",
        },
        // Udir White
        udirWhite: {
          DEFAULT: "#FFFFFF",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  daisyui: {
    themes: [
      {
        udir: {
          secondary: "#7dbf9d",
          accent: "#eed0c3",
          neutral: "#0e0507",
          "base-100": "#FFFFFF",
          info: "#666666",
          success: "#36d399",
          warning: "#fbbd23",
          error: "#EA0B0B",
          primary: "#536C93",
        },
      },
    ],
  },
  plugins: [
    require("tailwindcss-animate"),
    require("daisyui"),
    require("@tailwindcss/typography"),
  ],
} satisfies Config;

export default config;
