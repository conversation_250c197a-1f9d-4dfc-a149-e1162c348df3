import { NextResponse } from "next/server";
import { generateSignalRAccessToken } from "@/lib/generateSignalRAccessToken";
import { v4 as uuidv4 } from "uuid";
import { SignalRMessageEnum } from "@/enums/SignalRMessageEnum";

const hubName = process.env.AZURE_SIGNALR_HUB_NAME || "pgshub";
const connectionString =
  process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "";
const endpoint = connectionString?.match(/Endpoint=(.*?);/)?.[1] ?? "";

export async function POST(request: Request) {
  try {
    
    const body = await request.json();
    const { candidateNumber, status, testPartId, schoolId } = body;

    if (!schoolId) {
      return NextResponse.json(
        { success: false, error: "SchoolId is required" },
        { status: 400 }
      );
    }

    // Bygg URL for å sende til spesifikk gruppe (schoolId)
    const url = `${endpoint}/api/v1/hubs/${hubName}/groups/${schoolId}`;
    // Send melding til SignalR gruppen
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${generateSignalRAccessToken({
          audience: url,
          lifetime: 60,
        })}`,
      },
      body: JSON.stringify({
        target: SignalRMessageEnum.CandidateStatusChanged,
        arguments: [
          {
            id: uuidv4(),
            candidateNumber,
            status: status,
            testPartId: testPartId,
          },
        ],
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("SignalR error:", errorText);
      throw new Error(`Failed to send SignalR message: ${response.status}`);
    }

    return NextResponse.json({
      success: true,
      message: "Access request sent successfully to school group",
    });
  } catch (error) {
    console.error("Error sending access request:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to send access request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
