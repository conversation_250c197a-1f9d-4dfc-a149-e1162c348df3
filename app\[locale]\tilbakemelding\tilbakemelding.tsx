"use client"

import Link from "next/link";
import React from "react";
import SendBtn from "./sendbtn";
import { usePathname } from "next/navigation";

interface IfeedBackProp {
  pathname?: string;
  setState?: React.Dispatch<React.SetStateAction<boolean>>;
}
const Tilbakemelding = ({ pathname, setState }: IfeedBackProp) => {

  return (
    <div className=" ">
      <h2 className="text-xl text-left font-semibold">Fortell oss hva du savner</h2>
      <p className="mt-4 ">
        Tilbakemeldingen din bidrar til å gjøre nettsidene bedre for alle. Ikke
        oppgi personlige opplysninger.
      </p>
      <span className="pt-0 mt-0">
        Tilbakemeldinger er anonyme og blir ikke besvart.
      </span>
      <span className="pt-0 mt-0 ml-1">
         Dersom du har behov for brukerstøtte ta kontakt{" "}
        <Link
          className="link"
          href="https://www.udir.no/om-udir/tjenestevei-skole-barnehage/"
        >
          tjenestevei
        </Link>
        .
      </span>

      <div className="flex flex-col mt-6 gap-4">
        <SendBtn pathname={pathname} setState={setState} />
      </div>
    </div>
  );
};

export default Tilbakemelding;
