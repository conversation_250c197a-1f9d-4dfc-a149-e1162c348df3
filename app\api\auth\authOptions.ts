import { NextAuthOptions, Profile, User } from "next-auth";
import DuendeIDS6<PERSON>rovider from "next-auth/providers/duende-identity-server6";
import { v4 as uuidv4 } from "uuid";

interface ExtendedProfile extends Profile {
  role?: string[] | string;
  userId: string;
}

//TODO: Update with correct role values

function checkArrayStartsWithAny(arr: string[]): boolean {
  const rolesToMatch = [
    "urn:udir:pgsa:administrator",
    "urn:udir:eksamen:sa",
    "urn:udir:eksamen:sa+",
    "urn:udir:eksamen:ko",
    "urn:udir:eksamen:fk",
    "urn:udir:eksamen:sf",
    "urn:udir:eksamen:vea",
    "urn:udir:eksamen:fm",
    "urn:udir:eksamen:ev",
  ];

  return arr.some((element) =>
    rolesToMatch.some((prefix) =>
      element.toLowerCase().startsWith(prefix.toLowerCase())
    )
  );
}

export const authOptions: NextAuthOptions = {
  providers: [
    DuendeIDS6Provider({
      clientId: process.env.NEXTAUTH_UIDP_CLIENT_ID || "",
      clientSecret: process.env.NEXTAUTH_UIDP_CLIENT_SECRET_PGS_ADMIN || "",
      //  wellKnown: `${process.env.NEXTAUTH_UIDP_URL}/.well-known/openid-configuration`,
      issuer: process.env.NEXTAUTH_UIDP_URL,
      checks: ["pkce", "state"],
      id: "UIDP",
      name: "UIDP",
      authorization: {
        params: {
          scope: process.env.NEXTAUTH_UIDP_CLIENT_SCOPE,
        },
      },
      idToken: true,
      userinfo: {
        async request(context) {
          return await context.client.userinfo(
            context.tokens.access_token ?? ""
          );
        },
      },
      async profile(profile) {
        return {
          id: profile.sub,
          name: `${profile.given_name} ${profile.family_name}`,
          uid: profile.uid,
          userId: profile["udir:pas2:brukerid"],
        };
      },
    }),
  ],
  pages: {
    signIn: "/auth/signin", // Tilpasset rute for innlogging
    error: "/auth/error",
  },
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 timer
  },
  jwt: {},

  callbacks: {
    async signIn({ user, profile }) {
      const pgsProfile = profile as ExtendedProfile;

      if (
        pgsProfile &&
        checkArrayStartsWithAny(
          Array.isArray(pgsProfile.role)
            ? pgsProfile.role
            : [pgsProfile.role ?? ""]
        )
      ) {
        return true;
      } else return false;
      /* if (
        pgsProfile &&
        (
          pgsProfile.role?.includes("urn:udir:pgsa:administrator") ||
          pgsProfile.role?.includes("urn:udir:eksamen:sa") ||
          pgsProfile.role?.includes("urn:udir:eksamen:sa+") ||
          pgsProfile.role?.includes("urn:udir:eksamen:ko") ||
          pgsProfile.role?.includes("urn:udir:eksamen:fk") ||
          pgsProfile.role?.includes("urn:udir:eksamen:sf") ||
          (pgsProfile.role?.includes("urn:udir:pgsa:kandidatgruppeansvarlig") &&
            pgsProfile.role?.includes("urn:udir:eksamen:ev")))
      ) {
        return true;
      } else {
        return false;
      }*/
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      // Allow callbacks to identity server Server
      else if (new URL(url).origin === process.env.NEXTAUTH_UIDP_URL)
        return url;
      return baseUrl;
    },
    async jwt({ token, user, account, profile }) {
      const pgsProfile = profile as ExtendedProfile;

      if (user) {
        const userGuid = uuidv4();

        token.user = {
          userGuid: userGuid,
          ...user,
        };
        token.idToken = account?.id_token;
        token.role = Array.isArray(pgsProfile.role)
          ? pgsProfile.role
          : [pgsProfile.role];
      }

      return token;
    },
    async session({ session, token }) {
      session.user = {
        // append the id token to the next-auth session
        idToken: token.idToken,
        userInfo: token.user,
        role: token.role,
      } as any;

      return session;
    },
  },
  events: {},
  debug: false,
  theme: {
    brandColor: "red",
    colorScheme: "light",
  },
};
