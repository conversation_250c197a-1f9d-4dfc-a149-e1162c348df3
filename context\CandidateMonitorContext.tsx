"use client";

import React, {
  createContext,
  useContext,
  useReducer,
  useCallback,
  ReactNode,
  useEffect,
  useMemo,
  useState,
} from "react";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { SignalRStatusUpdate } from "@/hooks/signalRProvider";
import { useRole } from "./RoleContext";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { IAccessRequestUpdate } from "@/hooks/accessRequestProvider";
import {
  createApiError,
  calculateCandidateStatuses,
  processAuthorizedUsers,
  processBlockedUsers,
  findLatestSession,
} from "./candidateMonitorUtils";
import { useCandidateData } from "./useCandidateData";

dayjs.extend(utc);
dayjs.extend(timezone);

// Types for state and actions
interface ErrorData {
  type: string;
  message: string;
}

interface CandidateState {
  examPaper: IExamPaperInternal[];
  visibleCandidates: string[];
  currentPageCandidates: string[];
  errors: ErrorData[];
  authorizedCandidates: Record<string, boolean>;
  hasAbsence: Record<string, boolean>;
  blockedUsers: Record<string, boolean>;
  redisResult: IRedisObject[];
  candidatesWithLevertStatus: Record<string, boolean>;
  candidatesWaitingForExamStart: Record<string, boolean>;
}

type CandidateAction =
  | { type: "SET_EXAM_PAPER"; payload: IExamPaperInternal[] }
  | { type: "SET_VISIBLE_CANDIDATES"; payload: string[] }
  | { type: "SET_CURRENT_PAGE_CANDIDATES"; payload: string[] }
  | { type: "ADD_ERROR"; payload: ErrorData }
  | { type: "REMOVE_ERROR"; payload: string }
  | { type: "CLEAR_ERRORS" }
  | { type: "SET_AUTHORIZED_CANDIDATES"; payload: Record<string, boolean> }
  | { type: "UPDATE_CANDIDATE_AUTHORIZATION"; payload: { candidateNumber: string; isAuthorized: boolean } }
  | { type: "SET_HAS_ABSENCE"; payload: Record<string, boolean> }
  | { type: "SET_BLOCKED_USERS"; payload: Record<string, boolean> }
  | { type: "UPDATE_BLOCKED_USER"; payload: { candidateNumber: string; isBlocked: boolean } }
  | { type: "SET_REDIS_RESULT"; payload: IRedisObject[] }
  | { type: "SET_LEVERT_STATUS"; payload: Record<string, boolean> }
  | { type: "SET_WAITING_FOR_EXAM"; payload: Record<string, boolean> }
  | { type: "UPDATE_CANDIDATE_STATUS"; payload: SignalRStatusUpdate }
  | { type: "RESET_STATE" };

interface CandidateContextType extends CandidateState {
  setExamPaper: (payload: IExamPaperInternal[]) => void;
  updateErrors: (
    actionType: string,
    isError: boolean,
    errorMessage?: string
  ) => void;
  removeAccess: (
    userSessionId: string,
    candidateNumber: string
  ) => Promise<void>;
  getLatestSession: (candidateNumber: string) => IRedisObject | undefined;
  removeBlockedUser: (userId: string, candidateNumber: string) => Promise<void>;
  updateAuthorizedCandidates: (newMap: Record<string, boolean>) => void;
  updateHasAbsence: (newMap: Record<string, boolean>) => void;
  updateBlockedUsers: (newMap: Record<string, boolean>) => void;
  updateRedisResult: (newResult: IRedisObject[]) => void;
  handleStatusUpdate: (update: SignalRStatusUpdate) => void;
  updateVisibleCandidates: (candidates: string[]) => void;
  setCurrentPageCandidates: (candidates: string[]) => void;
  getTotalCandidateCount: () => number;
  isLoading: boolean;
  refreshRedisData: () => Promise<IRedisObject[]>; // Removed fetchAll parameter
  updateAuthorizedWithAccessRequests: (
    accessRequests: IAccessRequestUpdate[]
  ) => void;
}

// Initial state
const initialState: CandidateState = {
  examPaper: [],
  visibleCandidates: [],
  currentPageCandidates: [],
  errors: [],
  authorizedCandidates: {},
  hasAbsence: {},
  blockedUsers: {},
  redisResult: [],
  candidatesWithLevertStatus: {},
  candidatesWaitingForExamStart: {},
};

// Reducer function
function candidateReducer(
  state: CandidateState,
  action: CandidateAction
): CandidateState {
  switch (action.type) {
    case "SET_EXAM_PAPER":
      return { ...state, examPaper: action.payload };
    case "SET_VISIBLE_CANDIDATES":
      return { ...state, visibleCandidates: action.payload };
    case "SET_CURRENT_PAGE_CANDIDATES":
      return { ...state, currentPageCandidates: action.payload };
    case "ADD_ERROR":
      return {
        ...state,
        errors: [...state.errors.filter(e => e.type !== action.payload.type), action.payload]
      };
    case "REMOVE_ERROR":
      return {
        ...state,
        errors: state.errors.filter(e => e.type !== action.payload)
      };
    case "CLEAR_ERRORS":
      return { ...state, errors: [] };
    case "SET_AUTHORIZED_CANDIDATES":
      return { ...state, authorizedCandidates: action.payload };
    case "UPDATE_CANDIDATE_AUTHORIZATION":
      return {
        ...state,
        authorizedCandidates: {
          ...state.authorizedCandidates,
          [action.payload.candidateNumber]: action.payload.isAuthorized
        }
      };
    case "SET_HAS_ABSENCE":
      return { ...state, hasAbsence: action.payload };
    case "SET_BLOCKED_USERS":
      return { ...state, blockedUsers: action.payload };
    case "UPDATE_BLOCKED_USER":
      return {
        ...state,
        blockedUsers: {
          ...state.blockedUsers,
          [action.payload.candidateNumber]: action.payload.isBlocked
        }
      };
    case "SET_REDIS_RESULT":
      return { ...state, redisResult: action.payload };
    case "SET_LEVERT_STATUS":
      return { ...state, candidatesWithLevertStatus: action.payload };
    case "SET_WAITING_FOR_EXAM":
      return { ...state, candidatesWaitingForExamStart: action.payload };
    case "UPDATE_CANDIDATE_STATUS":
      return handleStatusUpdateReducer(state, action.payload);
    case "RESET_STATE":
      return initialState;
    default:
      return state;
  }
}

// Helper function for status updates
function handleStatusUpdateReducer(
  state: CandidateState,
  update: SignalRStatusUpdate
): CandidateState {
  const { candidateNumber, statusPart1, statusPart2, testPartId } = update;
  if (!candidateNumber) return state;

  return {
    ...state,
    examPaper: state.examPaper.map((paper) => ({
      ...paper,
      candidates: paper.candidates.map((candidate) =>
        candidate.candidateNumber === candidateNumber
          ? {
              ...candidate,
              deliveryStatusPart1: statusPart1,
              deliveryStatusPart2: statusPart2,
              testPartId,
            }
          : candidate
      ),
    })),
  };
}

// Create the context
const CandidateContext = createContext<CandidateContextType | undefined>(
  undefined
);

// API utility functions are now imported from candidateMonitorUtils

interface CandidateProviderProps {
  children: ReactNode;
  serverTime: string; // UTC ISO string from server
}

export function CandidateProvider({ children, serverTime }: CandidateProviderProps) {
  const [state, dispatch] = useReducer(candidateReducer, initialState);
  const { selectedRole } = useRole();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Function to get current server time (no offset calculation needed)
  const getServerTime = useCallback(() => {
    return dayjs.utc(serverTime).tz("Europe/Oslo");
  }, [serverTime]);

  // Session data key generation is now handled by useCandidateData custom hook

  // Error handling helper
  const updateErrors = useCallback(
    (actionType: string, isError: boolean, errorMessage?: string) => {
      if (isError && errorMessage) {
        dispatch({
          type: "ADD_ERROR",
          payload: { type: actionType, message: errorMessage },
        });
      } else {
        dispatch({
          type: "REMOVE_ERROR",
          payload: actionType,
        });
      }
    },
    []
  );

  // Redis data handling
  const triggerFullCandidateRefresh = useCallback(() => {
    // Trigger a refresh of the Redis data
    setRefreshTrigger((prev) => prev + 1);
  }, []); // No dependencies needed as it only calls setRefreshTrigger

  // This function now primarily serves to trigger a refresh of the main SWR hook for session data.
  // The `fetchAll` parameter is no longer strictly necessary as the main SWR hook always fetches for all in examPaper.
  const refreshRedisData = useCallback(async (): Promise<IRedisObject[]> => {
    triggerFullCandidateRefresh();
    // The SWR hook will handle fetching and updating state.
    return Promise.resolve(state.redisResult || []);
  }, [triggerFullCandidateRefresh, state.redisResult]); // Added state.redisResult to dependencies if returning it.

  // Data fetching with custom hook
  const { isLoading } = useCandidateData({
    selectedSchoolId: selectedRole?.selectedSchoolId,
    examPaper: state.examPaper,
    refreshTrigger: refreshTrigger,
    onExamDataSuccess: (data: IExamPaperInternal[]) => {
      dispatch({ type: "SET_EXAM_PAPER", payload: data });

      if (data?.length > 0 && state.visibleCandidates.length === 0) {
        const allCandidates = data.flatMap((paper) =>
          paper.candidates.map((c) => c.candidateNumber)
        );
        dispatch({ type: "SET_VISIBLE_CANDIDATES", payload: allCandidates });
        dispatch({
          type: "SET_CURRENT_PAGE_CANDIDATES",
          payload: allCandidates,
        });
      }

      updateErrors("Kandidathenting", false);
    },
    onExamDataError: (error) => {
      updateErrors(
        "Kandidathenting",
        true,
        error instanceof Error
          ? `Klarte ikke å hente kandidater: ${error.message}`
          : "Klarte ikke å hente kandidater"
      );
    },
    onRedisDataSuccess: (data: IRedisObject[]) => {
      dispatch({ type: "SET_REDIS_RESULT", payload: data });
      setAuthorizedUsersFromRedisData(data);
      updateErrors("Autorisasjonsdata", false);
    },
    onRedisDataError: (error) => {
      updateErrors(
        "Autorisasjonsdata",
        true,
        `Klarte ikke å hente autorisasjonsstatus for kandidater: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    },
    onBlockedUsersSuccess: (data: { users: string[] }) => {
      if (data?.users) {
        const blockedUsersMap = processBlockedUsers(data.users, state.examPaper);
        dispatch({ type: "SET_BLOCKED_USERS", payload: blockedUsersMap });
      }
      updateErrors("Blokkerte brukere", false);
    },
    onBlockedUsersError: () => {
      updateErrors(
        "Blokkerte brukere",
        true,
        "Klarte ikke å hente status for blokkerte brukere"
      );
    },
  });

  // Helper functions
  const getLatestSession = useCallback(
    (candidateNumber: string): IRedisObject | undefined => {
      return findLatestSession(candidateNumber, state.redisResult);
    },
    [state.redisResult]
  );

  // Process Redis data to extract authorized users
  const setAuthorizedUsersFromRedisData = useCallback(
    (redisData: IRedisObject[]) => {
      const finalAuthorizedMap = processAuthorizedUsers(redisData, state.examPaper);
      dispatch({
        type: "SET_AUTHORIZED_CANDIDATES",
        payload: finalAuthorizedMap,
      });
    },
    [state.examPaper]
  );

  // SWR hooks removed - now handled by useCandidateData custom hook

  // Calculate derived states based on exam paper data
  useEffect(() => {
    if (!state.examPaper.length) return;

    const now = getServerTime();
    const { hasAbsence, hasLevert, waitingForExamStart } = calculateCandidateStatuses(
      state.examPaper,
      now
    );

    dispatch({ type: "SET_HAS_ABSENCE", payload: hasAbsence });
    dispatch({ type: "SET_LEVERT_STATUS", payload: hasLevert });
    dispatch({ type: "SET_WAITING_FOR_EXAM", payload: waitingForExamStart });
  }, [state.examPaper, getServerTime]);

  const removeAccess = useCallback(
    async (userSessionId: string, candidateNumber: string): Promise<void> => {
      const response = await fetch("/api/removeAccessRedis", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userSessionId, candidateNumber }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw createApiError("/api/removeAccessRedis", response.status, errorText || response.statusText);
      }

      dispatch({
        type: "UPDATE_CANDIDATE_AUTHORIZATION",
        payload: { candidateNumber, isAuthorized: false },
      });
    },
    []
  );

  const removeBlockedUser = useCallback(
    async (userId: string, candidateNumber: string): Promise<void> => {
      if (!selectedRole?.selectedSchoolId) {
        throw new Error("Ingen skole er valgt");
      }

      const url = `/api/blockedUsers/${selectedRole.selectedSchoolId}/${userId}`;
      const response = await fetch(url, { method: "DELETE" });

      if (!response.ok) {
        const errorText = await response.text();
        throw createApiError(url, response.status, errorText || response.statusText);
      }

      dispatch({
        type: "UPDATE_BLOCKED_USER",
        payload: { candidateNumber, isBlocked: false },
      });
    },
    [selectedRole?.selectedSchoolId]
  );

  // Action dispatchers
  const updateVisibleCandidates = useCallback((candidates: string[]): void => {
    dispatch({ type: "SET_VISIBLE_CANDIDATES", payload: candidates });
  }, []);

  const setCurrentPageCandidates = useCallback((candidates: string[]): void => {
    dispatch({ type: "SET_CURRENT_PAGE_CANDIDATES", payload: candidates });
  }, []);

  const updateAuthorizedCandidates = useCallback(
    (newMap: Record<string, boolean>): void => {
      dispatch({ type: "SET_AUTHORIZED_CANDIDATES", payload: newMap });
    },
    []
  );

  const updateHasAbsence = useCallback(
    (newMap: Record<string, boolean>): void => {
      dispatch({ type: "SET_HAS_ABSENCE", payload: newMap });
    },
    []
  );

  const updateBlockedUsers = useCallback(
    (newMap: Record<string, boolean>): void => {
      dispatch({ type: "SET_BLOCKED_USERS", payload: newMap });
    },
    []
  );

  const updateRedisResult = useCallback((newResult: IRedisObject[]): void => {
    dispatch({ type: "SET_REDIS_RESULT", payload: newResult });
  }, []);

  const handleStatusUpdate = useCallback(
    (update: SignalRStatusUpdate): void => {
      dispatch({ type: "UPDATE_CANDIDATE_STATUS", payload: update });
    },
    []
  );

  const getTotalCandidateCount = useCallback((): number => {
    return state.examPaper.reduce(
      (total, paper) => total + paper.candidates.length,
      0
    );
  }, [state.examPaper]);

  const updateAuthorizedWithAccessRequests = useCallback(
    (_accessRequests: IAccessRequestUpdate[]) => {
      // This function can be used to handle access requests if needed
      // For now, we just refresh the Redis data
      setAuthorizedUsersFromRedisData(state.redisResult);
    },
    [state.redisResult, setAuthorizedUsersFromRedisData]
  );

  // isLoading is now provided by useCandidateData custom hook

  // Context value
  const contextValue = useMemo(
    (): CandidateContextType => ({
      ...state,
      setExamPaper: (payload) => dispatch({ type: "SET_EXAM_PAPER", payload }),
      updateErrors,
      removeAccess,
      getLatestSession,
      removeBlockedUser,
      updateAuthorizedCandidates,
      updateHasAbsence,
      updateBlockedUsers,
      updateRedisResult,
      handleStatusUpdate,
      updateVisibleCandidates,
      setCurrentPageCandidates,
      getTotalCandidateCount,
      isLoading,
      refreshRedisData,
      updateAuthorizedWithAccessRequests,
    }),
    [
      state,
      updateErrors,
      removeAccess,
      getLatestSession,
      removeBlockedUser,
      updateAuthorizedCandidates,
      updateHasAbsence,
      updateBlockedUsers,
      updateRedisResult,
      handleStatusUpdate,
      updateVisibleCandidates,
      setCurrentPageCandidates,
      getTotalCandidateCount,
      isLoading,
      refreshRedisData,
      updateAuthorizedWithAccessRequests,
    ]
  );

  return (
    <CandidateContext.Provider value={contextValue}>
      {children}
    </CandidateContext.Provider>
  );
}

export function useCandidate(): CandidateContextType {
  const context = useContext(CandidateContext);
  if (!context) {
    throw new Error("useCandidate must be used within a CandidateProvider");
  }
  return context;
}
