import React, { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";

interface FileListProps {
  files: string[];
  maxDisplay?: number;
}

export function MissingFilesList({ files, maxDisplay = 10 }: FileListProps) {
  const [showAll, setShowAll] = useState(false);
  const displayFiles = showAll ? files : files.slice(0, maxDisplay);

  return (
    <div className="w-full max-w-md">
      <ScrollArea className="max-h-96 p-4">
        <ul className="space-y-2  list-inside list-disc">
          {displayFiles.map((file, index) => (
            <li key={index} className="text-sm ">
             {file}
            </li>
          ))}
        </ul>
        {files.length > maxDisplay && !showAll && (
          <button
            onClick={() => setShowAll(true)}
            className="mt-4 text-sm underline"
          >
            Vis alle ({files.length}) filer
          </button>
        )}
      </ScrollArea>
    </div>
  );
}
