"use client";

import * as React from "react";
import { DayPicker } from "react-day-picker";
import { nb } from "react-day-picker/locale";
import "react-day-picker/style.css";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return <DayPicker mode="single" locale={nb} className="p-4" {...props} />;
}
Calendar.displayName = "Calendar";

export { Calendar };
