"use client";
import React, { useEffect, useState } from "react";
import { ICandidate } from "@/interface/ICandidate";
import { toast } from "@/components/ui/use-toast";
import { AuditLogDataTable } from "@/components/tables/kandidatmonitor/activityLog/activityLogDataTable";
import { activityLogColumns } from "@/components/tables/kandidatmonitor/activityLog/activityLogColumns";
import { ActivityLogSkeleton } from "./ActivityLogSkeleton"; // Import skeleton
import dayjs from "dayjs";

interface CandidateInfoModalProp {
  candidate: ICandidate;
  subjectCode: string;
  examDate?: string;
}

export default function ActivityLogDialog({
  candidate,
  subjectCode,
  examDate,
}: CandidateInfoModalProp) {
  const [activityLogs, setActivityLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true); // Add loading state

  useEffect(() => {
    async function fetchActivityLog() {
      setIsLoading(true); // Set loading to true at the start
      const body = { userId: candidate.userId };
      try {
        const response = await fetch(
          `${window.location.origin}/api/getActivityLogV2`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(body),
          }
        );

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const result = await response.json();
        setActivityLogs(result);
      } catch (error) {
        console.log(error);
        toast({
          title: "Feil ved henting av aktivitetslogg",
          description: "Noe gikk galt, prøv igjen senere.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }
    fetchActivityLog();
  }, [candidate.userId]);

  if (isLoading) {
    return <ActivityLogSkeleton />;
  }

  return (
    <div className="mt-8">
      <h2 id="candidate-info-title" className="text-xl mb-5">
        Aktivitetslogg for {candidate.candidateName} (
        {candidate.candidateNumber}) - {subjectCode} -{" "}
        {dayjs(examDate).format("DD.MM.YYYY")}
      </h2>
      <div className="overflow-x-auto">
        <AuditLogDataTable columns={activityLogColumns} data={activityLogs} />
      </div>
      <div className="flex flex-col gap-8">
        <div role="region" aria-label="Kandidatens besvarelser"></div>
        <p></p>
      </div>
    </div>
  );
}
