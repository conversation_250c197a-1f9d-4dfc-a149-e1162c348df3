// components/ConnectionStatus.tsx
import {
  Tooltip<PERSON>rovider,
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Trigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useState } from "react";
import { useSignalRConnection } from "@/hooks/signalRProvider";

export function SignalRConnectionStatus() {
  const { isConnected, error, reconnect } = useSignalRConnection();
  const [isSpinning, setIsSpinning] = useState(false);

  const handleReconnect = async (e: React.MouseEvent) => {
    e.preventDefault();
    setIsSpinning(true);
    await reconnect();
    setTimeout(() => setIsSpinning(false), 1000);
  };

  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger>
            <div className="flex items-center gap-2">
              <div
                className={`w-2.5 h-2.5 rounded-full ${
                  error
                    ? "bg-red-500"
                    : isConnected
                    ? "bg-green-500"
                    : "bg-yellow-500 animate-pulse"
                }`}
              />
              <span className="text-sm text-muted-foreground">
                {error
                  ? "Sanntidsmeldinger feilet"
                  : isConnected
                  ? "Sanntidsmeldinger aktiv"
                  : "Kobler til..."}
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent className="bg-white p-2 shadow-lg rounded-lg">
            {error
              ? "Klikk på knappen for å prøve å gjenopprette tilkoblingen"
              : isConnected
              ? "Mottar statuser fra kandidater uten å måtte laste inn siden på nytt."
              : "Forsøker å etablere SignalR tilkobling..."}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {error && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleReconnect}
          disabled={isSpinning}
          className="h-7 text-xs px-2 flex items-center gap-1 border"
        >
          <div className="w-3 h-3 flex items-center justify-center">
            <RefreshCw
              className={`h-3 w-3 ${isSpinning ? "animate-spin" : ""}`}
            />
          </div>
          <span>Koble til</span>
        </Button>
      )}
    </div>
  );
}
