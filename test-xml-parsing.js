// Test script for XML parsing functionality
import { parseStringPromise } from "xml2js";

const sampleXml = `<PgsdEksamensplanerViewModel xmlns:pa="http://pas.udir.no/Pamelding" xmlns:ep="http://pas.udir.no/Eksamensplan" xmlns:ct="http://pas.udir.no/CommonTypes" xmlns:ko="http://pas.udir.no/Karakteroversikt" xmlns:kl="http://pas.udir.no/Klage">
  <Eksamensplaner>
    <PgsdEksamensplanViewModel>
      <Opplaeringsniva>VGS</Opplaeringsniva>
      <Oppgaveansvar>Sentral</Oppgaveansvar>
      <Eksamensperiode>
        <Kode>H-2025</Kode>
      </Eksamensperiode>
      <Eksamener>
        <PgsdEksamenViewModel>
          <Fagkode>LIM3102</Fagkode>
          <Eksamensdeler>
            <PgsdEksamensdelViewModel>
              <GjennomforingStart>2025-09-03T09:00:00</GjennomforingStart>
            </PgsdEksamensdelViewModel>
            <PgsdEksamensdelViewModel>
              <GjennomforingStart>2025-09-03T14:00:00</GjennomforingStart>
            </PgsdEksamensdelViewModel>
          </Eksamensdeler>
        </PgsdEksamenViewModel>
      </Eksamener>
    </PgsdEksamensplanViewModel>
  </Eksamensplaner>
</PgsdEksamensplanerViewModel>`;

async function testXmlParsing() {
  try {
    const result = await parseStringPromise(sampleXml, {
      explicitArray: true,
      ignoreAttrs: true,
      trim: true
    });
    
    console.log('Parsed XML structure:');
    console.log(JSON.stringify(result, null, 2));
    
    // Test navigation
    const root = result.PgsdEksamensplanerViewModel;
    const eksamensplan = root?.Eksamensplaner?.[0]?.PgsdEksamensplanViewModel?.[0];
    const eksamener = eksamensplan?.Eksamener?.[0]?.PgsdEksamenViewModel;
    
    console.log('\nExtracted data:');
    console.log('Eksamensperiode:', eksamensplan?.Eksamensperiode?.[0]?.Kode?.[0]);
    console.log('Første fagkode:', eksamener?.[0]?.Fagkode?.[0]);
    console.log('Antall eksamensdeler:', eksamener?.[0]?.Eksamensdeler?.[0]?.PgsdEksamensdelViewModel?.length);
    
  } catch (error) {
    console.error('XML parsing error:', error);
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testXmlParsing();
}
