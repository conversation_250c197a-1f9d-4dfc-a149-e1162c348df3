import { getClientIp } from "./getClientIp";
import { IActivityLogV2 } from "@/interface/IActivityLogV2";

export async function getUserLoggingInfo(
  userId: string,
  eksamensdel?: string,
  filnavn?: string,
  role?: string,
  administratorName?: string,
  candidateRegistrationId?: string,
  candidateNumber?: string,
  candidateFirstName?: string,
  candidateLastName?: string
): Promise<Partial<IActivityLogV2>> {
  const userLoggingInfo: Partial<IActivityLogV2> = {
    userId,
    Role: role || "",
    TestPartId: eksamensdel || "",
    FileName: filnavn || "",
    candidateRegistrationId: candidateRegistrationId || userId, // Fallback to userId if not provided
    candidateNumber: candidateNumber || "",
    candidateFirstName: candidateFirstName || "",
    candidateLastName: candidateLastName || "",
    ip: await getClientIp(),
  };

  return userLoggingInfo;
}
