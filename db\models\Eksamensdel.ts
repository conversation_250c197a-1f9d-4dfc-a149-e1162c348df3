import "reflect-metadata";
import { randomUUID } from "crypto";
import {
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  BeforeInsert,
} from "typeorm";

@Entity("Eksamensdel")
@Index(["FagkodeeksamensID", "EksamensdelType"], { unique: true })
export class Eksamensdel {
  @PrimaryColumn({ type: "nvarchar", length: 36 })
  EksamensdelID!: string;

  @Column({ type: "nvarchar", length: 100 })
  FagkodeeksamensID!: string;

  @Column({ type: "nvarchar", length: 50 })
  EksamensdelType!: string;

  @Column({ type: "datetime2", nullable: true })
  GjennomforingStart?: Date | null;

  @Column({ type: "datetime2", nullable: true })
  GjennomforingStopp?: Date | null;

  @Column({ type: "nvarchar", length: 100, nullable: true })
  Gjennomforingsystem?: string | null;

  @Column({ type: "ntext", nullable: true })
  Eksamensveiledning?: string | null;

  @Column({ type: "bit", default: false })
  ErPlagiatkontroll!: boolean;

  @CreateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  CreatedDate!: Date;

  @UpdateDateColumn({ type: "datetime2", default: () => "GETDATE()" })
  ModifiedDate!: Date;

  // Lazy import to avoid circular dependency
  @ManyToOne(
    "Fagkodeeksamen",
    (fagkodeeksamen: any) => fagkodeeksamen.eksamensdeler,
    { onDelete: "CASCADE" }
  )
  @JoinColumn({ name: "FagkodeeksamensID" })
  fagkodeeksamen!: any;

  @BeforeInsert()
  generateId() {
    if (!this.EksamensdelID) {
      this.EksamensdelID = randomUUID();
    }
  }
}
