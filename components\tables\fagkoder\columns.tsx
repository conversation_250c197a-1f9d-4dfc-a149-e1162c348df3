"use client";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { FaLink } from "react-icons/fa";
import { IFagkoder } from "@/interface/IFagkoder";


export const columnsFagkode: ColumnDef<IFagkoder>[] = [
  {
    accessorKey: "Fagkode",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Fagkode{" "}
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="pil opp ned ikon"
          />
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div tabIndex={0} className="">
        {row.original.Fagkode}
      </div>
    ),
  },
  {
    accessorKey: "Fagnavn",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Fagnavn
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div tabIndex={0} className="">
        {row.original.Fagnavn}
      </div>
    ),
  },
  {
    accessorKey: "Eksamensdato",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
         Eksamensdato
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div className="">{row.original.Eksamensdato}</div>
    ),
  },
  {
    accessorKey: "Todelt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Todelt: Start del 2
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div className="capitalize">{row.original.formattedTodelt}</div>
    ),
  },
  {
    accessorKey: "Lareplan",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Læreplan
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div tabIndex={0} className="">
        {row.original.Lareplan}
      </div>
    ),
  },
  
  {
    accessorKey: "SisteEksamen",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Siste eksamen
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div tabIndex={0} className="">
        {row.original.SisteEksamen}
      </div>
    ),
  },
  {
    accessorKey: "LenkeEksamensplan",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Lenke eksamensplan
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div tabIndex={0} className="">
        <a href={row.original.LenkeEksamensplan} target="_blank" rel="noopener noreferrer">
         <FaLink role="img" aria-label="Link"/>
        </a>
      </div>
    ),
  },
];
