import { IRoleObject } from "@/interface/IRoleObject";
import { ISchools } from "@/interface/ISchools";
import { ISession } from "@/interface/ISession";

// Definerer props for NavBar-komponenten
export interface NavBarProps {
  session: ISession | null;
  userRoles: string[];
}

// Definerer strukturen for roller hentet fra JSON
interface IRoleFromJson {
  role: string;
  orgName: string;
  orgNumber: string;
}

//TODO: Update with correct role values
// Mapper roller til navn og nivå
//TODO: PGSAF-3588
export const roleMapping: Record<string, { name: string; level: number }> = {
  "urn:udir:pgsa:administrator": {
    name: "Systemadministrator",
    level: 7,
  },
  "urn:udir:eksamen:sa+": { name: "Skoleadministrator+", level: 4 },
  "urn:udir:eksamen:sa": { name: "Skoleadministrator", level: 3 },
  "urn:udir:eksamen:sf": { name: "Statsforvalter", level: 5 },
  "urn:udir:eksamen:fm": { name: "Statsforvalter", level: 5 },
  "urn:udir:eksamen:ev": { name: "Eksamensvakt", level: 2 },
  "urn:udir:eksamen:ko": { name: "Kommune", level: 5 },
  "urn:udir:eksamen:fk": { name: "Fylkeskommune", level: 5 },
  "urn:udir:eksamen:vea": { name: "Vigo eksamensadministrator", level: 6 },
};

// Henter rollenavn og organisasjonsinformasjon fra JSON
export function getRoleNamesFromJson(
  role: string,
  entities: (IPasxSchool | IAuthority | IMunicipality)[]
): IRoleFromJson {
  const parts = role.split(":");
  const orgNumber = parts.pop() || "";
  const roleName = parts.join(":");

  const orgName =
    entities.find((e) => e.organisasjonsnummer === orgNumber)?.navn || "";

  return { role, orgName, orgNumber };
}

// Henter relevante roller fra JSON
function getRolesFromJson(
  userRoles: string[],
  entities: (IPasxSchool | IAuthority | IMunicipality)[]
): IRoleFromJson[] {
  const relevantPrefixes = [
    "urn:udir:eksamen:sf",
    "urn:udir:eksamen:ko",
    "urn:udir:eksamen:fk",
    "urn:udir:eksamen:fm",
  ];

  return userRoles
    .filter((role) =>
      relevantPrefixes.some((prefix) => role.startsWith(prefix))
    )
    .map((role) => getRoleNamesFromJson(role, entities));
}

// Henter rollenavn basert på URN
export const getRoleName = (urn: string): string =>
  roleMapping[urn]?.name || "Ukjent rolle";

// Finner matchende roller for skoler
function getMatchedSchoolRoles(
  session: ISession | null,
  schools: ISchools[],
  userRoles: string[]
): { schoolId: string; schoolName: string; matchedRoles: string[] }[] {
  const sessionUserRoles = (
    Array.isArray(session?.user?.role)
      ? session.user.role
      : [session?.user?.role]
  )
    .filter((role): role is string => typeof role === "string")
    .filter(
      (role) =>
        ![
          "urn:udir:eksamen:sf",
          "urn:udir:eksamen:ko",
          "urn:udir:eksamen:fk",
          "urn:udir:eksamen:fm",
        ].some((prefix) => role.startsWith(prefix))
    );

  return schools
    .map((school) => ({
      schoolId: school.schoolId,
      schoolName: school.schoolName,
      matchedRoles: school.roles.filter((role) =>
        sessionUserRoles.some((item) => item.startsWith(role.split(":")[0]))
      ),
    }))
    .filter((school) => school.matchedRoles.length > 0);
}

// Hovedfunksjon for å hente rolleobjekter
export function getRoleObject(
  session: ISession | null,
  schools: ISchools[],
  userRoles: string[],
  pasxSchools: IPasxSchool[],
  enhetsservice: IAuthority[],
  skoleansvarlig: IMunicipality[]
): IRoleObject[] {
  let returnObject: IRoleObject[] = [];

  // Samler alle enheter
  const allEntities = [...pasxSchools, ...enhetsservice, ...skoleansvarlig];
  const rolesFromJson = getRolesFromJson(userRoles, allEntities);

  // Legger til roller fra JSON
  if (rolesFromJson) {
    returnObject.push(
      ...rolesFromJson.map(({ orgName, orgNumber, role }) => ({
        displayRoleName: getRoleName(role.replace(/:\d+$/, "")),
        orgId: orgNumber,
        orgName: orgName,
        role,
        selectedSchoolName:
          role.startsWith("urn:udir:eksamen:sa") ||
          role.startsWith("urn:udir:eksamen:sa+")
            ? orgName
            : "",
        selectedSchoolId:
          role.startsWith("urn:udir:eksamen:sa") ||
          role.startsWith("urn:udir:eksamen:sa+")
            ? orgNumber
            : "",
      }))
    );
  }

  // Legger til matchende skolerolle
  const schoolRoles = getMatchedSchoolRoles(session, schools, userRoles);

  schoolRoles.forEach(({ schoolId, schoolName, matchedRoles }) =>
    matchedRoles.forEach((role) =>
      returnObject.push({
        displayRoleName: getRoleName(role.replace(/:\d+$/, "")),
        orgId: schoolId,
        orgName: schoolName,
        role,
        selectedSchoolName:
          role.startsWith("urn:udir:eksamen:sa") ||
          role.startsWith("urn:udir:eksamen:sa+")
            ? schoolName
            : "",
        selectedSchoolId:
          role.startsWith("urn:udir:eksamen:sa") ||
          role.startsWith("urn:udir:eksamen:sa+")
            ? schoolId
            : "",
      })
    )
  );

  // Henter roller fra sesjonen
  const roles = (
    Array.isArray(session?.user.role)
      ? session?.user.role
      : [session?.user.role]
  ).filter(Boolean) as string[];

  // Filtrerer ut spesifikke roller
  //TODO: PGSAF-3588
  const filteredRoles = roles.filter(
    (role) =>
      ![
        "urn:udir:eksamen:sa",
        "urn:udir:eksamen:ev",
        "urn:udir:eksamen:fk",
        "urn:udir:eksamen:ko",
        "urn:udir:eksamen:sf",
        "urn:udir:eksamen:fm",
      ].some((prefix) => role.startsWith(prefix))
  );

  // Legger til filtrerte roller
  returnObject.push(
    ...filteredRoles.map((role) => ({
      displayRoleName: getRoleName(role),
      orgId: "",
      orgName: "",
      role,
      selectedSchoolName: "",
      selectedSchoolId: "",
    }))
  );

  /* // Sjekker for spesielle roller
  const hasSpecialRoles = roles.some((r) =>
    r.startsWith("urn:udir:eksamen:ev")
  );

  // Fjerner eksamensvakt-roller hvis brukeren ikke har spesielle roller
  if (!hasSpecialRoles) {
    returnObject = returnObject.filter(
      (r) => !r.role.startsWith("urn:udir:eksamen:ev")
    );
  } */
  const sorted = sortRoles(returnObject);
  return sorted;
}

// Sorterer roller basert på nivå og navn
export function sortRoles(roles: IRoleObject[]): IRoleObject[] {
  return roles.sort((a, b) => {
    const getLevel = (role: string) =>
      roleMapping[
        Object.keys(roleMapping).find((key) => role.startsWith(key)) || ""
      ]?.level || 0;

    const levelDiff = getLevel(b.role) - getLevel(a.role);
    if (levelDiff !== 0) return levelDiff;

    const nameCompare = a.displayRoleName.localeCompare(b.displayRoleName);
    return nameCompare !== 0 ? nameCompare : a.orgName.localeCompare(b.orgName);
  });
}
