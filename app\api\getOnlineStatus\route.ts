"use server";

import { NextResponse } from "next/server";
import { getAppInsightsServer } from "@/lib/appInsightsServer";
import { IOnlineServerResponse } from "@/interface/IOnlineServerResponse";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";

const telemetryClient = getAppInsightsServer();

export async function POST(request: Request) {
  const apiUrl = process.env.OnlineServerApi || "";

  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const requestBody = await request.json();

    if (
      !Array.isArray(requestBody) ||
      requestBody.some((item) => typeof item !== "string")
    ) {
      return NextResponse.json(
        { error: "Invalid request body. Expected a string array." },
        { status: 400 }
      );
    }

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      cache: "no-store",
      body: JSON.stringify(requestBody),
    });
    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    const data: IOnlineServerResponse[] = await response.json();

    // Modify the statusCode before sending the response
    const modifiedData = data.map((item: any) => ({
      ...item,
      statusCode:
        item.statusCode === "active"
          ? "online"
          : item.statusCode === "inactive"
          ? "offline"
          : item.statusCode,
    }));
    return NextResponse.json(modifiedData, { status: 200 });
  } catch (error) {
    // Log error to telemetry
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "getOnlineServer",
        statuscode: error instanceof Response ? error.status : 0,
        response:
          error instanceof Response ? await error.text() : "No response",
      },
    });

    // Log error to the console
    console.error("Error in OnlineServerGetList API:", error);

    // Return error response
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
