// app/api/access-request/route.ts
import { getAllHashFromRedis } from "@/app/lib/redisHelper";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const schoolId = searchParams.get("schoolId");

    if (!schoolId) {
      return NextResponse.json(
        { error: "School ID is required" },
        { status: 400 }
      );
    }

    const key = `AccessRequest:${schoolId}`;

    const requests = await getAllHashFromRedis(key);

    if (!requests) {
      return NextResponse.json({});
    }

    // Parse the JSON strings stored in Redis
    const parsedRequests = Object.entries(requests).reduce(
      (acc, [candidateNumber, requestData]) => {
        try {
          acc[candidateNumber] = JSON.parse(requestData);
        } catch (error) {
          console.error(
            `Error parsing request data for candidate ${candidateNumber}:`,
            error
          );
        }
        return acc;
      },
      {} as Record<string, any>
    );

    return NextResponse.json(parsedRequests);
  } catch (error) {
    console.error("Error fetching access requests from Redis:", error);
    return NextResponse.json(
      { error: "Failed to fetch access requests" },
      { status: 500 }
    );
  }
}
