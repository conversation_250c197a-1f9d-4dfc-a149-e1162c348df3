// app/api/schools/[schoolId]/blocked-users/route.ts
import { authOptions } from "@/app/api/auth/authOptions";
import { addToSet, removeFromSet } from "@/app/lib/redisHelper";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";

// Hjelpefunksjon for å generere Redis-nøkkel
const getRedisKey = (schoolId: string) => `BlockedUsers:${schoolId}`;

// POST - Blokker en bruker
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ schoolId: string; userId: string }> }
) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { schoolId, userId } = await params;

    if (!userId || !schoolId) {
      return NextResponse.json(
        { error: "userId og schoolId er påkrevd" },
        { status: 400 }
      );
    }

    const key = getRedisKey(schoolId);
    const result = await addToSet(key, userId);

    return NextResponse.json(
      {
        message:
          result === 1 ? "Bruker blokkert" : "Bruker var allerede blokkert",
        schoolId,
        userId,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Feil ved blokkering av bruker:", error);
    return NextResponse.json({ error: "Intern serverfeil" }, { status: 500 });
  }
}

// DELETE - Fjern en blokkert bruker
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ schoolId: string; userId: string }> }
) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { schoolId, userId } = await params;

    if (!userId || !schoolId) {
      return NextResponse.json(
        { error: "userId og schoolId er påkrevd" },
        { status: 400 }
      );
    }

    const key = getRedisKey(schoolId);
    const removed = await removeFromSet(key, userId);

    return NextResponse.json(
      {
        message: removed === 0 ? "Bruker var ikke blokkert" : "Bruker fjernet",
        schoolId,
        userId,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Feil ved fjerning av blokkert bruker:", error);
    return NextResponse.json({ error: "Intern serverfeil" }, { status: 500 });
  }
}
