"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { IoIosArrowBack } from "react-icons/io";

export default function GoBackButton() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleGoBack = () => {
    const params = new URLSearchParams(searchParams);

    // Remove part1 and part2 parameters
    params.delete("part1");
    params.delete("part2");

    // Construct the URL without part1 and part2 parameters
    const backUrl = params.toString()
      ? `/pgs-monitor?${params.toString()}`
      : "/pgs-monitor";

    router.replace(backUrl, { scroll: false });
  };

  return (
    <nav>
      <Button
        variant="ghost"
        className="text-base p-0 flex items-center gap-2 pr-2"
        onClick={handleGoBack}
        aria-label="Gå tilbake til forrige side"
      >
        <IoIosArrowBack role="img" aria-label="pil tilbake" />
        <span>Tilbake</span>
      </Button>
    </nav>
  );
}
