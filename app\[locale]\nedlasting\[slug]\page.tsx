"use client";

import React, { useEffect, useRef, useState, useCallback, use } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { IoIosArrowBack, IoMdDownload } from "react-icons/io";
import { DataTable } from "../../../../components/tables/nedlasting/candidate-list/data-table";
import { createColumns } from "../../../../components/tables/nedlasting/candidate-list/columns";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { ImSpinner2 } from "react-icons/im";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { Skeleton } from "@/components/ui/skeleton";
import { useExamPaper } from "@/hooks/useExamPaper";

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function Page(props: PageProps): JSX.Element {
  const [showPart2, setShowPart2] = useState<boolean>(false);

  const router = useRouter();
  const ref = useRef<HTMLDivElement>(null);
  const params = use(props.params);
  const group = params.slug.replace(/%20/g, " ");

  // Use the hook for sessionStorage data
  const {
    data: examPaperData,
    isLoading,
    summary,
    download,
  } = useExamPaper({
    fromSessionStorage: true,
  });

  const data = examPaperData as IExamPaperInternal | null;

  useEffect(() => {
    if (data?.candidates && data.candidates.length > 0) {
      setShowPart2(data.candidates[0].deliveryStatusPart2 !== -1);
    }
  }, [data]);

  const downloadAllSubmissions = useCallback(
    async (data: IExamPaperInternal) => {
      await download.downloadGroup(data);
    },
    [download]
  );

  return (
    <>
      <div className="bg-header">
        <div className="container-wrapper py-6">
          <h1 className="text-4xl">Nedlasting av besvarelser</h1>
          <p className="mt-4">
            <span className="font-bold">Gruppe:</span>{" "}
            {decodeURIComponent(group)}
          </p>
          {data ? (
            <p className="text-sm">
              {data.subjectCode} {data.subjectName}
            </p>
          ) : (
            <p className="text-sm">Laster inn faginfo...</p>
          )}
        </div>
      </div>
      <div className="w-full bg-[#F0E8DC]">
        <div className="container-wrapper flex flex-col items-start sm:flex-row sm:items-center justify-between py-1">
          <div className="flex flex-col lg:flex-row lg:gap-6 text-sm">
            {data ? (
              <>
                <span>
                  Antall kandidater:{" "}
                  <span className="font-medium">{data.candidates.length}</span>
                </span>
                <span>
                  Kandidater med leverte filer:{" "}
                  <span className="font-medium">{summary.deliveredCandidates}</span>
                </span>
                <span>
                  Antall leverte filer:{" "}
                  <span className="font-medium">{summary.totalFiles}</span>
                </span>
              </>
            ) : (
              <>
                <span className="flex items-center gap-2">
                  Antall kandidater:{" "}
                  <Skeleton className="h-4 w-8 bg-gray-300" />
                </span>
                <span className="flex items-center gap-2">
                  Kandidater med leverte filer:{" "}
                  <Skeleton className="h-4 w-8 bg-gray-300" />
                </span>
                <span className="flex items-center gap-2">
                  Antall leverte filer:{" "}
                  <Skeleton className="h-4 w-8 bg-gray-300" />
                </span>
              </>
            )}
          </div>
          <div aria-label="Nedlastingsalternativer">
            {data ? (
              <DownloadButton
                onClick={() => data && downloadAllSubmissions(data)}
                downloading={download.state.downloading}
                progress={download.state.progress}
                totalSize={summary.totalSize}
                data={data}
                totalFiles={summary.totalFiles}
              />
            ) : (
              <Skeleton className="h-8 w-52 bg-gray-300" />
            )}
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-3 container-wrapper my-3">
        <div>
          <Button
            variant="ghost"
            className="text-base p-0 flex items-center gap-2 pr-2"
            onClick={() => router.back()}
            aria-label="Gå tilbake til forrige side"
          >
            <IoIosArrowBack role="img" aria-label="pil tilbake" />
            <span>Tilbake</span>
          </Button>
        </div>

        <section
          className="w-full"
          ref={ref}
          aria-label="Liste over kandidater og besvarelser"
        >
          {data ? (
            <DataTable
              columns={createColumns(showPart2)}
              data={data.candidates}
              extraCandidateInfo={data}
            />
          ) : (
            <div
              className="mx-10 mb-4 flex items-center"
              role="status"
              aria-live="polite"
            >
              <AiOutlineLoading3Quarters className="animate-spin mr-2 text-2xl" />
              <span>Henter kandidatliste...</span>
            </div>
          )}
        </section>
      </div>
    </>
  );
}

// Helper Components
interface DownloadButtonProps {
  onClick: () => void;
  downloading: boolean;
  progress: number;
  totalSize: number;
  data: IExamPaperInternal | undefined;
  totalFiles: number;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  onClick,
  downloading,
  progress,
  totalSize,
  data,
  totalFiles,
}) => {
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "kB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = bytes / Math.pow(k, i);
    
    // For filer under 1 MB (kB), rund opp til nærmeste hele kB
    if (i === 1) {
      return Math.ceil(size) + " " + sizes[i];
    }
    
    return size > 10 && i >= 2
      ? size.toFixed(1) + " " + sizes[i]
      : size.toFixed(2) + " " + sizes[i];
  };

  const progressText =
    totalSize > 0
      ? `${formatSize(progress)} / ${formatSize(totalSize)} (${Math.round(
          (progress / totalSize) * 100
        )}%)`
      : "";

  return (
    <div className="flex items-center space-x-4">
      {downloading && (
        <span className="text-sm text-gray-600" aria-live="polite">
          {progressText}
        </span>
      )}
      <Button
        onClick={onClick}
        variant="default"
        className="h-8 rounded-sm flex items-center justify-center"
        disabled={downloading || totalFiles === 0 || !data}
        aria-busy={downloading}
      >
        {downloading ? (
          <>
            <ImSpinner2
              className="animate-spin mr-2 text-lg"
              role="img"
              aria-label="spinner ikon"
            />
            <span>Laster ned</span>
          </>
        ) : (
          <>
            <IoMdDownload
              className="text-lg mr-2"
              role="img"
              aria-label="last ned ikon"
            />
            <span>
              Last ned besvarelsene
              {totalSize > 0 && ` (${formatSize(totalSize)})`}
            </span>
          </>
        )}
      </Button>
    </div>
  );
}