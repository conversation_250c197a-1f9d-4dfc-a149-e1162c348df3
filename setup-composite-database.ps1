# PowerShell script to set up database with composite primary key
# Usage: .\setup-composite-database.ps1 -ServerName "localhost" -DatabaseName "yourdb" -Username "sa" -Password "yourpassword"

param(
    [Parameter(Mandatory=$true)]
    [string]$ServerName,
    
    [Parameter(Mandatory=$true)]
    [string]$DatabaseName,
    
    [Parameter(Mandatory=$true)]
    [string]$Username,
    
    [Parameter(Mandatory=$true)]
    [string]$Password
)

Write-Host "Setting up database tables with composite primary key..." -ForegroundColor Green
Write-Host "Server: $ServerName" -ForegroundColor Cyan
Write-Host "Database: $DatabaseName" -ForegroundColor Cyan

try {
    # Build connection string
    $connectionString = "Server=$ServerName;Database=$DatabaseName;User Id=$Username;Password=$Password;TrustServerCertificate=true;"
    
    # Read SQL script
    $sqlScript = Get-Content -Path ".\database-setup-composite.sql" -Raw
    
    # Create SQL connection
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to database successfully" -ForegroundColor Green
    
    # Execute script
    $command = New-Object System.Data.SqlClient.SqlCommand($sqlScript, $connection)
    $command.CommandTimeout = 300 # 5 minutes timeout
    
    Write-Host "Executing database setup script..." -ForegroundColor Yellow
    $reader = $command.ExecuteReader()
    
    # Read and display all result sets
    do {
        while ($reader.Read()) {
            for ($i = 0; $i -lt $reader.FieldCount; $i++) {
                Write-Host "$($reader.GetName($i)): $($reader.GetValue($i))" -ForegroundColor White
            }
            Write-Host "---" -ForegroundColor Gray
        }
    } while ($reader.NextResult())
    
    $reader.Close()
    
    Write-Host "Database setup completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    if ($connection) {
        $connection.Close()
        Write-Host "Database connection closed" -ForegroundColor Cyan
    }
}

Write-Host "`nComposite Primary Key Setup Complete!" -ForegroundColor Yellow
Write-Host "Key format: {Eksamensperiode}_{Fagkode} (e.g., 'H-2025_LIM3102')" -ForegroundColor White
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test the import function in the admin panel" -ForegroundColor White
Write-Host "2. Verify records are created with correct composite IDs" -ForegroundColor White
