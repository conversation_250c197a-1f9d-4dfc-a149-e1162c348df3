name: Build and deploy Node.js app to Azure Web App

on:
  push:
    branches:
      - main
      - develop

  workflow_dispatch:
env:
  APP_VERSION: ""
  CUSTOMCONNSTR_APPINSIGHTS_CONNECTIONSTRING: ${{ secrets.APPINSIGHTS_CONNECTION_STRING }}

jobs:
  Build:
    runs-on: ubuntu-latest

    steps:
      - id: SetVersionNumber
        name: Set APP_VERSION environment variable
        run: echo "APP_VERSION=$(date +'%Y.%m').${{ github.run_number }}" >> $GITHUB_ENV

      - id: SetVersionNumberOutPut
        run: echo "APP_VERSION=$(date +'%Y.%m').${{ github.run_number }}" >> $GITHUB_OUTPUT

      - name: Show environment variables
        run: |
          echo "APP_VERSION: $APP_VERSION"

      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: Cache Node.js modules
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: ${{ runner.os }}-node-

      - name: npm install, build, and test
        run: |
          npm install
          npm run build
          cp -r ./.next/static ./.next/standalone/.next/
          cp -r ./public ./.next/standalone/
          cp package.json ./.next/standalone/
          cp package-lock.json ./.next/standalone/

      - name: Zip build artifacts
        run: zip -r build-artifacts.zip ./.next/standalone/

      - name: Upload zipped build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: ./build-artifacts.zip

    outputs:
      output1: ${{ steps.SetVersionNumberOutPut.outputs.APP_VERSION }}

  Dev:
    needs: Build
    runs-on: ubuntu-latest
    env:
      APP_VERSION: ${{ needs.Build.outputs.output1 }}
    environment:
      name: "Dev"
    steps:
      - name: Download zipped build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ./

      - name: Unzip build artifacts
        run: unzip build-artifacts.zip

      - name: "Deploy to Azure Web App"
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: "wa-pgs-admin-dev"
          slot-name: "Production"
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ./.next/standalone

      - name: Show environment variables
        run: |
          echo "APP_VERSION in deploy step: $APP_VERSION"

  Test:
    needs: Dev
    runs-on: ubuntu-latest
    environment:
      name: "Test"
    steps:
      - name: Download zipped build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ./

      - name: Unzip build artifacts
        run: unzip build-artifacts.zip

      - name: "Deploy to Azure Web App"
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: "wa-pgs-admin-test"
          slot-name: "Production"
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ./.next/standalone

      - name: Show environment variables
        run: |
          echo "APP_VERSION in deploy step: $APP_VERSION"

  Qa:
    needs: Test
    runs-on: ubuntu-latest
    environment:
      name: "Qa"
    steps:
      - name: Download zipped build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ./

      - name: Unzip build artifacts
        run: unzip build-artifacts.zip

      - name: "Deploy to Azure Test Web App"
        uses: azure/webapps-deploy@v3
        with:
          app-name: "wa-pgs-admin-qa"
          slot-name: "Production"
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ./.next/standalone

  Prod:
    needs: Qa
    runs-on: ubuntu-latest
    environment:
      name: "Prod"
    steps:
      - name: Download zipped build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ./

      - name: Unzip build artifacts
        run: unzip build-artifacts.zip

      - name: "Deploy to Azure Test Web App"
        uses: azure/webapps-deploy@v3
        with:
          app-name: "wa-pgs-admin-prod"
          slot-name: "Production"
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ./.next/standalone
