"use client";

import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";
import { IRoleObject } from "@/interface/IRoleObject";
import dayjs from "dayjs";

interface RoleContextType {
  selectedRole: IRoleObject | undefined;
  setSelectedRole: React.Dispatch<
    React.SetStateAction<IRoleObject | undefined>
  >;
  selectedPeriod: string;
  setSelectedPeriod: React.Dispatch<React.SetStateAction<string>>;
}

const RoleContext = createContext<RoleContextType | undefined>(undefined);

export const RoleProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [selectedRole, setSelectedRole] = useState<IRoleObject | undefined>();
  const [selectedPeriod, setSelectedPeriod] = useState<string>("");

  // Hent og valider `selectedRole` og `selectedPeriod` fra localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Hent og valider `selectedRole`
      const storedRole = localStorage.getItem("selectedRole");
      if (storedRole) {
        const parsedRole = JSON.parse(storedRole);

        // Sjekk om `selectedRole` er utløpt
        if (parsedRole.expiry && dayjs().isBefore(parsedRole.expiry)) {
          setSelectedRole(parsedRole.role);
        } else {
          // Fjern utløpt `selectedRole` fra localStorage
          localStorage.removeItem("selectedRole");
        }
      }

      // Hent og valider `selectedPeriod`
      const storedPeriod = localStorage.getItem("selectedPeriod");
      if (storedPeriod) {
        const parsedPeriod = JSON.parse(storedPeriod);

        // Sjekk om `selectedPeriod` er utløpt
        if (parsedPeriod.expiry && dayjs().isBefore(parsedPeriod.expiry)) {
          setSelectedPeriod(parsedPeriod.period);
        } else {
          // Fjern utløpt `selectedPeriod` fra localStorage
          localStorage.removeItem("selectedPeriod");
        }
      }
    }
  }, []);

  // Lagre `selectedRole` i localStorage og cookie
  useEffect(() => {
    async function setRoleInCookie() {
      await fetch("/api/setrole", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role: selectedRole?.role }),
      });
    }

    if (typeof window !== "undefined" && selectedRole) {
      // Beregn slutten av dagen med dayjs
      const endOfDay = dayjs().endOf("day").valueOf(); // Unix-tid i millisekunder

      // Lagre role og utløpstid i localStorage
      const roleWithExpiry = {
        role: selectedRole,
        expiry: endOfDay,
      };
      localStorage.setItem("selectedRole", JSON.stringify(roleWithExpiry));
    }

    if (selectedRole) {
      setRoleInCookie();
    }
  }, [selectedRole]);

  // Lagre `selectedPeriod` i localStorage
  useEffect(() => {
    if (typeof window !== "undefined" && selectedPeriod) {
      // Beregn slutten av dagen med dayjs
      const endOfDay = dayjs().endOf("day").valueOf(); // Unix-tid i millisekunder

      // Lagre period og utløpstid i localStorage
      const periodWithExpiry = {
        period: selectedPeriod,
        expiry: endOfDay,
      };
      localStorage.setItem("selectedPeriod", JSON.stringify(periodWithExpiry));
    }
  }, [selectedPeriod]);

  return (
    <RoleContext.Provider
      value={{
        selectedRole,
        setSelectedRole,
        selectedPeriod,
        setSelectedPeriod,
      }}
    >
      {children}
    </RoleContext.Provider>
  );
};

export const useRole = () => {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error("useRole must be used within a RoleProvider");
  }
  return context;
};

export default RoleContext;
