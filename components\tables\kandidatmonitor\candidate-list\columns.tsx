"use client";

import { ColumnDef } from "@tanstack/react-table";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { Button } from "@/components/ui/button";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

import {
  HiOutlineArrowNarrowDown,
  HiOutlineArrowNarrowUp,
} from "react-icons/hi";
import { IAccessRequestUpdate } from "@/hooks/accessRequestProvider";

interface ColumnProps {
  onShowCandidateInfo: (candidate: ICandidateMonitor) => void;
  onShowIpDialog: (candidate: ICandidateMonitor) => void;
  authorizedCandidates: Record<string, boolean>;
  accessRequests: IAccessRequestUpdate[];
  blockedUsers: Record<string, boolean>;
  removeBlockedUser: (userId: string, candidateNumber: string) => Promise<void>;
  updateBlockedUsers: (newMap: Record<string, boolean>) => void;
  redisResult: IRedisObject[];
  refreshRedisData: () => Promise<IRedisObject[]>;
  isTwoPartExam: boolean;
  isOnePartExam: boolean;
  candidatesWaitingForExamStart: Record<string, boolean>;
  getLatestSession: (candidateNumber: string) => IRedisObject | undefined;
  showIp: boolean;
}

const createSortableHeader =
  (title: string) =>
  ({ column }: { column: any }) => {
    const isSorted = column.getIsSorted();

    return (
      <Button
        variant="ghost"
        className="p-0 hover:bg-transparent"
        onClick={() => column.toggleSorting(isSorted === "asc")}
      >
        {title}
        <div className="ml-2 flex items-center -space-x-[6px]">
          <HiOutlineArrowNarrowUp
            className="h-[14px] w-[14px]"
            strokeWidth={isSorted === "asc" ? 4 : 2}
          />
          <HiOutlineArrowNarrowDown
            className="h-[14px] w-[14px]"
            strokeWidth={isSorted === "desc" ? 4 : 2}
          />
        </div>
      </Button>
    );
  };

const getDeliveryStatusText = (
  candidate: ICandidateMonitor,
  part: 1 | 2
): string | null => {
  if (part === 2 && candidate.deliveryStatusPart2 === -1) return null;

  const status =
    part === 1 ? candidate.deliveryStatusPart1 : candidate.deliveryStatusPart2;
  const examStart =
    part === 1
      ? dayjs.utc(candidate.partOneStartDateTime).tz('Europe/Oslo')
      : dayjs.utc(candidate.partTwoStartDateTime).tz('Europe/Oslo');

  if (
    [
      CandidateStatusEnum.DokumentertFravaer,
      CandidateStatusEnum.IkkeDokumentertFravaer,
    ].includes(status)
  ) {
    return "";
  }

  if (dayjs().tz('Europe/Oslo').isBefore(examStart)) {
    return `Starter kl. ${examStart.format("HH:mm")}`;
  }

  if (
    [
      CandidateStatusEnum.IkkeInnlogget,
      CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
      CandidateStatusEnum.InnloggetAutentisert,
    ].includes(status)
  ) {
    return "Ikke lastet opp";
  }

  switch (status) {
    case CandidateStatusEnum.LastetOpp:
      return "Laster opp";
    case CandidateStatusEnum.Levert:
      return "Levert digitalt";
    case CandidateStatusEnum.LevertManuelt:
      return "Sendes i posten";
    default:
      return "";
  }
};

const examPartSorting = (part: 1 | 2) => {
  return (rowA: any, rowB: any) => {
    const candidateA = rowA.original;
    const candidateB = rowB.original;

    const textA = getDeliveryStatusText(candidateA, part) || "";
    const textB = getDeliveryStatusText(candidateB, part) || "";

    if (!textA && textB) return 1;
    if (textA && !textB) return -1;

    return textA.localeCompare(textB);
  };
};

const createExamColumn = (part: 1 | 2, header: string) => ({
  accessorKey: `deliveryStatusPart${part}`,
  header: createSortableHeader(header),
  cell: ({ row }: { row: { original: ICandidateMonitor } }) => {
    const status = getDeliveryStatusText(row.original, part);
    return status && <div className="whitespace-pre-line">{status}</div>;
  },
  enableSorting: true,
  enableHiding: false,
  sortingFn: examPartSorting(part),
});

const createIpColumn = (
  getLatestSession: (candidateNumber: string) => IRedisObject | undefined
) => ({
  accessorKey: "IP",
  header: createSortableHeader("IP-adresse"),
  cell: ({ row }: { row: { original: ICandidateMonitor } }) => {
    const lastSession = getLatestSession(row.original.candidateNumber);
    return (
      <div className="flex flex-col gap-1 text-wrap">
        {lastSession?.activeIp}
      </div>
    );
  },
  enableSorting: true,
  enableHiding: true,
  sortingFn: (rowA: any, rowB: any) => {
    const ipA = getLatestSession(rowA.original.candidateNumber)?.activeIp || "";
    const ipB = getLatestSession(rowB.original.candidateNumber)?.activeIp || "";
    return ipA.localeCompare(ipB);
  },
});

export const createColumns = ({
  accessRequests,
  authorizedCandidates,
  candidatesWaitingForExamStart,
  blockedUsers,
  isTwoPartExam,
  isOnePartExam,
  getLatestSession,
  showIp,
}: ColumnProps): ColumnDef<ICandidateMonitor>[] => {
  // Define base columns that are always included
  const baseColumns: ColumnDef<ICandidateMonitor>[] = [
    {
      accessorKey: "candidateName",
      header: createSortableHeader("Kandidat"),
      cell: ({ row }) => (
        <div className="flex flex-col gap-1">
          <span>{row.original.candidateName}</span>
          <div className="flex flex-col">
            <span className="text-sm text-gray-800 font-light">
              {row.original.candidateNumber}
            </span>
          </div>
        </div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "groupName",
      header: createSortableHeader("Gruppe/Fag"),
      cell: ({ row }) => (
        <div className="flex flex-col gap-1">
          <span>{row.original.groupName}</span>
          <span className="text-sm text-gray-800 font-light">
            {row.original.subjectName}
          </span>
        </div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
  ];

  // Define examination columns based on whether the exam is two-part
  const examinationColumns = isTwoPartExam
    ? [
        createExamColumn(1, `Del 1 ${!isOnePartExam ? "(Kun på papir)" : ""}`),
        createExamColumn(2, "Del 2"),
      ]
    : [createExamColumn(1, "Eksamen")];

  // Create final columns array based on showIp flag
  const finalColumns = [...baseColumns, ...examinationColumns];

  // Add IP column after exam columns if showIp is true
  if (showIp) {
    finalColumns.push(createIpColumn(getLatestSession));
  }

  return finalColumns;
};

export default createColumns;
