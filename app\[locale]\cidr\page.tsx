// app/page.tsx
"use client";

import { IPRangeInput } from "./ipRangeInput";

const schoolId = "123"; // This could come from a context, prop, or other source

export default function Page() {
  return (
    <>
      <div className="py-6 bg-stalbla">
        <div className="container-wrapper">
          <div className="flex md:flex-row flex-col md:justify-between md:items-center items-start">
            <div>
              <h1 className="text-4xl">Legg til Ip-områder</h1>
              <p className="mt-4">
                Oppgi gyldige IP-områder i CIDR-format for alle aktuelle
                nettverk hos skolen. Dette er nødvendig for å kunne overvåke at
                kandidatene gjennomfører eksamen på skolens nettverk.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="container-wrapper my-8">
        <IPRangeInput schoolId={schoolId} />
      </div>
    </>
  );
}
