import jwt, { JwtPayload, SignOptions } from "jsonwebtoken";

interface TokenOptions {
  audience: string;
  lifetime: number; // Lifetime in seconds
}

const connectionString = process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING;
const accessKey = connectionString?.match(/AccessKey=(.*?);/)?.[1] ?? "";

export const generateSignalRAccessToken = (options: TokenOptions): string => {
  const { audience, lifetime } = options;

  const expire = Math.floor(Date.now() / 1000) + lifetime;

  const payload: JwtPayload = {
    aud: audience,
    exp: expire,
  };

  const token = jwt.sign(payload, accessKey, {
    algorithm: "HS256",
  } as SignOptions);

  return token;
};