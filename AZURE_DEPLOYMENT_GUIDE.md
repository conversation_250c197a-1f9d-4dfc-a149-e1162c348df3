# Azure App Service Deployment Guide - FIXED

## Overview
This document explains the final solution to fix chunk load exceptions in the Next.js application deployed on Azure App Service with Linux.

## Problem Description
The application was experiencing chunk load exceptions with errors like:
```
GET /_next/static/chunks/9916-df2b293cd71d10b9.js
Error: The request has been aborted and the network socket has closed.
```

## Root Causes Identified & Fixed
1. **web.config file**: ❌ Was designed for Windows/IIS → ✅ **DELETED**
2. **Custom server.js**: ❌ ES modules conflict + poor chunk handling → ✅ **REMOVED**
3. **Build configuration**: ❌ Custom `distDir` interfered with deployment → ✅ **FIXED**

## Final Solution Applied

### 1. **Eliminated Custom Server**
- Removed problematic `server.js` (saved as backup)
- Using Next.js built-in optimized server via `npm start`
- Eliminates ES modules conflicts and improves chunk loading

### 2. **Optimized Next.js Configuration** (`next.config.js`)
- Removed `distDir: "build"` to use default `.next` directory
- Added chunk-specific optimizations:
  ```javascript
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  }
  ```

### 3. **Fixed Package.json**
- Updated start script: `"start": "next start -p 4000"`
- Maintains port consistency for Azure App Service

### 4. **Corrected GitHub Actions Workflow**
- Copies essential files to standalone directory
- Removes server.js dependency
- Proper artifact packaging for Azure deployment

## Azure App Service Configuration

### **CRITICAL: Required Settings**
1. **Startup Command**: `npm start`
2. **Node.js Version**: 20.x
3. **Environment**: Linux

### Environment Variables
Ensure these are set in Azure App Service:
- `CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING`
- `PORT` (Azure sets this automatically)
- Any other environment variables your app requires

## Why This Fixes Chunk Loading Issues

### ✅ **Specific Chunk Improvements:**
1. **Proper Cache Headers**: Chunks get `immutable` cache headers
2. **Correct Static Paths**: Files served from `/_next/static/` not `/public`
3. **Optimized Server**: Next.js built-in server handles chunks perfectly
4. **No ES Modules Conflicts**: Eliminates startup crashes
5. **Better Connection Handling**: Next.js server manages timeouts properly

### 📁 **Deployment Structure**
```
.next/standalone/
├── server.js (Next.js generated - optimized)
├── .next/
│   ├── static/ (chunks and assets)
│   └── ...
├── public/ (static files)
├── package.json
└── package-lock.json
```

## Benefits of Final Solution

### ✅ **Improvements**
- **Zero startup crashes** - no ES modules conflicts
- **Optimized chunk loading** - Next.js built-in server handles this perfectly
- **Proper caching** - immutable headers for static assets
- **Simpler deployment** - no custom server complexity
- **Better error handling** - Next.js server is more robust
- **Faster cold starts** - optimized standalone build

## Troubleshooting

### If App Still Won't Start
1. **Verify Azure startup command**: Must be `npm start`
2. **Check Azure logs**: Look for module loading errors
3. **Verify deployment package**: Ensure `.next/standalone` contains all files
4. **Port configuration**: App should use `process.env.PORT || 4000`

### If Chunk Errors Persist
1. Verify static files are served from `/_next/static/` path
2. Check cache headers are applied correctly
3. Monitor network tab in browser for failed chunk requests

## Deployment Steps

### 1. **Update Azure App Service**
- Go to Azure Portal → Your App Service → Configuration
- Set **Startup Command** to: `npm start`
- Save and restart the service

### 2. **Deploy Changes**
- Push these changes to your repository
- GitHub Actions will automatically deploy with new configuration
- Monitor deployment logs for any issues

### 3. **Verify Fix**
- Check that app starts successfully in Azure
- Test chunk loading by navigating between pages
- Monitor Application Insights for reduced chunk errors

## Rollback Plan
If issues occur:
1. Restore original server: `mv server.js.backup server.js`
2. Change startup command back to `node server.js`
3. Revert next.config.js and workflow changes

## Expected Results
- ✅ **App starts successfully** in Azure App Service
- ✅ **No more chunk load exceptions** in Application Insights
- ✅ **Improved performance** with proper caching
- ✅ **Better stability** with Next.js optimized server

This solution addresses all root causes and provides a clean, maintainable deployment approach.
