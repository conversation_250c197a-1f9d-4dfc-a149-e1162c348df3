"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { usePathname } from "next/navigation";
import { FaExternalLinkAlt } from "react-icons/fa";

export default function BrukerVeiledningButton() {
  const path = usePathname();

  const handleClick = () => {
    let newPath = `/brukerveiledning${path}`;

    // Check if path matches "/pgs-monitor/{some-id}"
    if (/^\/pgs-monitor\/[^/]+$/.test(path)) {
      newPath = "/brukerveiledning/pgs-monitor/leverFor";
    }

    window.open(newPath, "_blank");  };

  return (
    <Button
      variant="default"
      onClick={handleClick}
      className="flex items-center gap-2"
    >
      <span>Se brukerveiledning</span>
      <FaExternalLinkAlt role="img" aria-label="ekstern link ikon" />
    </Button>
  );
}
