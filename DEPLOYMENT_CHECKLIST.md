# 🚀 Azure Deployment Checklist - CRITICAL STEPS

## ✅ Code Changes Complete
The following fixes have been applied to resolve chunk load exceptions:

### Files Modified:
1. **next.config.js** - Optimized for chunk loading with cache headers
2. **package.json** - Updated start script to use port 4000
3. **.github/workflows/azure-webapps-node.yml** - Fixed deployment structure
4. **server.js** - REMOVED (moved to server.js.problematic)
5. **web.config** - DELETED (not needed on Linux)

## 🎯 CRITICAL: Azure App Service Settings

### **MUST CHANGE IN AZURE PORTAL:**

1. **Go to Azure Portal → Your App Service → Configuration**
2. **Set Startup Command to:** `npm start`
3. **Save and Restart** the service

### Environment Check:
- **Runtime Stack**: Node 20.x
- **Platform**: Linux
- **Environment Variables**: Ensure all your existing env vars are still set

## 🔍 Why This Fixes Chunk Loading

### Root Problems Solved:
- ❌ **ES Modules Conflict** → ✅ Removed custom server
- ❌ **Wrong Static File Paths** → ✅ Next.js handles `/_next/static/` correctly
- ❌ **Poor Connection Handling** → ✅ Next.js optimized server
- ❌ **Missing Cache Headers** → ✅ Added immutable headers for chunks

### Chunk-Specific Improvements:
1. **Proper Cache Headers**: `Cache-Control: public, max-age=31536000, immutable`
2. **Optimized Server**: Next.js built-in server handles chunks perfectly
3. **Correct Build Structure**: Standalone output with proper static file handling

## 📋 Deployment Process

### 1. **Push Code Changes**
```bash
git add .
git commit -m "Fix chunk loading issues - use Next.js built-in server"
git push
```

### 2. **Update Azure Startup Command**
- Portal → App Service → Configuration → General Settings
- **Startup Command**: `npm start`
- Click **Save** and **Restart**

### 3. **Monitor Deployment**
- Watch GitHub Actions workflow complete
- Check Azure logs for successful startup
- Test application functionality

## 🎉 Expected Results

After deployment:
- ✅ **App starts successfully** (no more crashes)
- ✅ **Chunk loading errors eliminated** 
- ✅ **Better performance** with proper caching
- ✅ **Stable connections** - no more socket aborts

## 🆘 If Something Goes Wrong

### Immediate Rollback:
1. Change Azure startup command back to: `node server.js.backup`
2. Rename: `server.js.backup` to `server.js`
3. Push emergency fix

### Logs to Check:
- **Azure App Service Logs**: Portal → Log Stream
- **GitHub Actions**: Check deployment completed successfully
- **Application Insights**: Monitor for reduced chunk errors

## 📞 Next Steps

1. **Update Azure startup command** (most critical!)
2. **Deploy via GitHub Actions**
3. **Test chunk loading** by navigating between pages
4. **Monitor Application Insights** for error reduction

The key is that you now have a clean, optimized Next.js deployment without custom server complexity!
