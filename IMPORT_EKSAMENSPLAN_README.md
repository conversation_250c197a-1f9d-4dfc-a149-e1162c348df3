# Import Eksamensplan - Admin Funksjon

## Oversikt

Denne funksjonen lar systemadministratorer importere eksamensplandata fra UDIR's eksterne API direkte til PGS-Admin databasen. Funksjonen henter XML-data fra `https://eksamenapi-tst2.udir.no/pgsd/eksamensplan` og populerer tabellene `Fagkodeeksamen` og `Eksamensdel`.

**Viktig**: API-et returnerer XML-data i format `PgsdEksamensplanerViewModel` som må parses og konverteres til vårt interne format.

## Database Setup

### Composite Primary Key Approach

Systemet bruker en sammensatt primærnøkkel for `Fagkodeeksamen` som kombinerer eksamensperiode og fagkode:

**Format**: `{Eksamensperiode}_{Fagkode}`
**Eksempler**: 
- `H-2025_LIM3102` (Høst 2025, Fagkode LIM3102)
- `V-2024_MAT1015` (Vår 2024, Fagkode MAT1015)

### Setup Script

Kjør PowerShell-scriptet for å sette opp databasen:

```powershell
.\setup-composite-database.ps1 -ServerName "localhost" -DatabaseName "yourdb" -Username "sa" -Password "yourpassword"
```

Scriptet vil:
1. Droppe eksisterende tabeller hvis de finnes
2. Opprette nye tabeller med sammensatt primærnøkkel
3. Legge til trigger for automatisk ID-generering
4. Teste oppsettet med en test-record
5. Verifisere at alt fungerer som forventet

## Tilgang

- **Rolle krav**: `urn:udir:pgsa:administrator`
- **Meny plassering**: Admin → Import Eksamensplan
- **URL**: `/admin/import-eksamensplan`

## Funksjonalitet

### 1. Database Modeller

#### Fagkodeeksamen
- **Primary Key**: FagkodeeksamensID (composite key: "{Eksamensperiode}_{Fagkode}")
- **Felter**: Fagkode, Variantkode, Eksamensperiode, Fagnavn, Varighet, Eksamensdato, Eksamenstid, ErTestFagkode
- **Timestamps**: CreatedDate, ModifiedDate
- **Eksempel ID**: "H-2025_LIM3102", "V-2024_MAT1015"

#### Eksamensdel
- **Primary Key**: EksamensdelID (auto-increment)
- **Foreign Key**: FagkodeeksamensID → Fagkodeeksamen (string reference)
- **Felter**: EksamensdelType, GjennomforingStart, GjennomforingStopp, Gjennomforingsystem, Eksamensveiledning, ErPlagiatkontroll
- **Timestamps**: CreatedDate, ModifiedDate

### 2. Import Prosess

1. **Validering**: Sjekker eksamensperiode format (H-ÅÅÅÅ eller V-ÅÅÅÅ)
2. **API Kall**: Henter XML-data fra ekstern API med retry-logikk (3 forsøk)
3. **XML Parsing**: Konverterer XML-struktur (`PgsdEksamensplanerViewModel`) til vårt interne format
4. **Database Lagring**: Bruker transaksjonshåndtering for hver fagkode
5. **Upsert Logikk**: Oppdaterer eksisterende data eller oppretter nye poster

### 3. XML Format Håndtering

API-et returnerer data i følgende XML-struktur:
```xml
<PgsdEksamensplanerViewModel>
  <Eksamensplaner>
    <PgsdEksamensplanViewModel>
      <Eksamensperiode><Kode>H-2025</Kode></Eksamensperiode>
      <Eksamener>
        <PgsdEksamenViewModel>
          <Fagkode>LIM3102</Fagkode>
          <Eksamensdeler>
            <PgsdEksamensdelViewModel>
              <GjennomforingStart>2025-09-03T09:00:00</GjennomforingStart>
            </PgsdEksamensdelViewModel>
          </Eksamensdeler>
        </PgsdEksamenViewModel>
      </Eksamener>
    </PgsdEksamensplanViewModel>
  </Eksamensplaner>
</PgsdEksamensplanerViewModel>
```

Servicen parser automatisk XML-dataene og ekstraherer:
- Fagkoder fra `<Fagkode>` elementer
- Eksamensdeler med gjennomføringstider fra `<Eksamensdeler>`
- Eksamensperiode fra `<Eksamensperiode><Kode>`

### 4. Feilhåndtering

- **Retry-logikk**: Eksponentiell backoff for API-kall
- **Timeout**: 30 sekunder per API-kall
- **XML Parsing**: Robust håndtering av XML-struktur med fallback-verdier
- **Transaksjonshåndtering**: Rollback ved feil per fagkode
- **Logging**: Detaljert logging til Application Insights
- **Error Tracking**: Comprehensive feilsporing og metrics

### 5. Sikkerhet

- **Autentisering**: Next.js session validering
- **Autorisering**: Administrator-rolle påkrevet
- **Audit Logging**: Alle operasjoner logges med brukerinformasjon
- **Input Validering**: Validering av eksamensperiode format
- **XML Security**: Sikker parsing av XML-data uten eksterne entiteter

## Bruk

### Gjennom Web Interface

1. Naviger til Admin → Import Eksamensplan
2. Skriv inn eksamensperiode (f.eks. "H-2025")
3. Klikk "Start Import"
4. Vent på resultat og se importstatistikk

### API Endepunkt

```typescript
POST /api/admin/import-eksamensplan
Content-Type: application/json

{
  "eksamensperiode": "H-2025"
}
```

**Response:**
```typescript
{
  "success": true,
  "message": "Import fullført på 2341ms. 156 vellykket, 0 feil.",
  "data": {
    "eksamensperiode": "H-2025",
    "importedCount": 156,
    "errorCount": 0,
    "user": "John Doe"
  }
}
```

## Tekniske Detaljer

### Dependencies

- **TypeORM**: Database ORM for SQL Server
- **Next.js**: Web framework og API routes
- **Application Insights**: Logging og monitoring
- **Lucide React**: Icons for UI
- **xml2js**: XML parsing library for handling API responses

### Filer

```
db/
├── models/
│   ├── Fagkodeeksamen.ts       # Database model with composite primary key
│   └── Eksamensdel.ts          # Database model with string foreign key
├── services/
│   └── eksamensplanService.ts  # Business logic
└── data-source.ts              # Updated med nye entities

database-setup-composite.sql   # SQL script for composite key tables
setup-composite-database.ps1   # PowerShell setup script

app/
├── api/admin/import-eksamensplan/
│   └── route.ts                # API endpoint
└── [locale]/admin/import-eksamensplan/
    └── page.tsx                # Admin UI

interface/
└── IEksamensplanImport.ts      # TypeScript interfaces

app/lib/
└── accessControl.ts            # Updated med ny meny item
```

### Performance

- **Connection Pooling**: Bruker TypeORM connection pooling
- **Batch Processing**: Prosesserer data sekvensielt med transaksjonshåndtering
- **Memory Management**: Efficient håndtering av store datasett
- **Monitoring**: Real-time metrics på import duration og success rate

### Error Scenarios

1. **API Utilgjengelig**: Retry-logikk med eksponentiell backoff
2. **Ugyldig XML**: Robust XML parsing med detaljert error logging  
3. **Tom Response**: Graceful handling av tomme API-svar
4. **Database Feil**: Transaction rollback og error reporting
5. **Timeout**: 30 sekunder timeout med clear error messages
6. **Autorisering**: Proper HTTP status codes og security logging
7. **XML Structure Changes**: Failsafe parsing som håndterer struktur-endringer

## Eksempel Bruk

```bash
# Eksempel eksamensperioder
H-2024  # Høst 2024
V-2025  # Vår 2025
H-2025  # Høst 2025
```

## Monitoring

Alle operasjoner logges til Application Insights med følgende events:
- `ImportEksamensplan_Started`
- `ImportEksamensplan_Completed`
- `ImportEksamensplan_Unauthorized`
- `ImportEksamensplan_Forbidden`
- `ImportEksamensplan_InvalidFormat`

## Support

For support eller spørsmål om import-funksjonen, kontakt PGS-Admin utviklingsteamet.
