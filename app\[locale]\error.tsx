"use client"

import dayjs from "dayjs";
import { useEffect } from "react";
import "dayjs/locale/nb"; // Importerer norsk locale

dayjs.locale("nb"); // Setter locale til norsk

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="mx-auto my-28 flex flex-col gap-6">
      <h1 className="text-5xl font-bold">Ooops! En feil har oppstått...</h1>
      <p>Beklager noe gikk galt. Prøv igjen senere.</p>
      <div className="w-full lg:w-2/3 p-6 bg-gray-100 border-red-600 border-2 grid grid-cols-[1fr_3fr] gap-2 rounded">
        <div>Tjeneste</div>
        <div className="font-semibold">PGS</div>
        <div>Side</div>
        <div className="font-semibold overflow-clip">
          {window.location.pathname}
          {window.location.search}
        </div>
        <div>Klokkeslett</div>
        <div className="font-semibold">
          {dayjs().format("D. MMMM [kl:] HH:mm")}
        </div>
        <div>Nettleser</div>
        <div className="font-semibold">{window.navigator.userAgent}</div>
        <div>Språkinnstilling</div>
        <div className="font-semibold">{window.navigator.language}</div>
        <div>FeilType</div>
        <div className="font-semibold">{error.name}</div>
        <div>Feilkode</div>
        <div>{error.digest}</div>
        {process.env.NODE_ENV === "development" && (
          <>
            <div>Feilmelding</div>
            <div>{error.message}</div>
          </>
        )}
      </div>

      <button
        onClick={() => reset()}
        className="hover:bg-secondColor max-w-sm whitespace-nowrap rounded border-none btn btn-neutral text-white text-sm px-4 py-2"
      >
        Prøv igjen
      </button>
    </div>
  );
}
