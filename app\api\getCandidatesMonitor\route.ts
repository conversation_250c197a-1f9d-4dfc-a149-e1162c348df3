"use server";

import { getServerSession } from "next-auth/next";
import { getAccessToken } from "@/lib/getAccessToken";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { IExamPaperPayload } from "@/interface/IExamPaperPayload";
import { NextResponse } from "next/server";
import { getAppInsightsServer } from "@/lib/appInsightsServer";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

const telemetryClient = getAppInsightsServer();

export async function POST(request: Request) {
  let response = null;

  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate input
    let examPaperInternal: IExamPaperPayload;
    try {
      examPaperInternal = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid JSON payload" },
        { status: 400 }
      );
    }

    if (!examPaperInternal.schoolId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Ensure the user has permission for this school
    const userRoles = Array.isArray(session.user.role)
      ? session.user.role
      : [session.user.role];

    const hasValidRole = userRoles.some((role) =>
      role.endsWith(`:${examPaperInternal.schoolId}`)
    );

    /*  if (!hasValidRole) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }*/

    const userId = session.user.userInfo.userId;

    // Get access token
    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    if (!accessToken) {
      return NextResponse.json(
        { error: "Failed to obtain access token" },
        { status: 500 }
      );
    }

    // Make the API call
    const apiUrl = `${PgsaApiUrl}/api/Monitor/candidates/${userId}/${examPaperInternal.schoolId}`;

    response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      const errorMessage = `Failed to fetch candidate groups. Status: ${
        response.status
      }, Message: ${await response.text()}`;
      console.error(errorMessage);
      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    if (response.status === 204) return NextResponse.json([]);

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "getExamPaper",
        statuscode: response ? response.status : 0,
        response: response ? await response.text() : "Tom respons",
      },
    });
    console.error("Error in exam paper API:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
