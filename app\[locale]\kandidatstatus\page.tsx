"use client";

import { useState, useEffect } from "react";
import useS<PERSON> from "swr";
import dayjs from "dayjs";
import {
  Table,
  TableBody,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";

interface UserStatus {
  statusCode: number;
  statusName: string;
  count: number;
  percentage?: number;
}

interface StatusMapping {
  [key: string]: string;
}

const fetcher = async (url: string): Promise<UserStatus[]> => {
  const res = await fetch(url);
  if (!res.ok) {
    throw new Error("An error occurred while fetching the data.");
  }
  return res.json();
};

// Status name mapping - No changes needed here

const getModifiedStatusName = (originalName: string): string => {
  const statusMapping: StatusMapping = {
    "Venter på dagspassord": "Venter på tilgang",
    Innlogget: "Autorisert",
    "Lastet opp": "Laster opp",
    Levert: "Levert digitalt",
    "Ikke dokumentert fravær": "Ikke-dokumentert fravær",
  };

  return statusMapping[originalName] || originalName;
};

const STATUS_COLOR_MAP: Record<string, string> = {
  "Venter på tilgang": "#BB893E",
  Autorisert: "#5BA27E",
  "Laster opp": "#30A1BB",
  "Levert digitalt": "#255F41",
  "Ikke-dokumentert fravær": "#353535",
  "Dokumentert fravær": "#6C7C94",
  "Annen status": "#A1BB30", 
  "Ikke innlogget": "#B0B0B0", 
};

export default function Home(): JSX.Element {
  
  const [currentTime, setCurrentTime] = useState<dayjs.Dayjs>(dayjs());
  const date: string = dayjs().format("YYYY-MM-DD");

   useEffect(() => {
    document.title = "Kandidatstatus | PGS-admin";
  }, []);

  const { data, error, isValidating } = useSWR<UserStatus[], Error>(
    `/api/candidateStatus?date=${date}`,
    fetcher,
    {
      refreshInterval: 60000,
      revalidateOnFocus: true,
    }
  );

  useEffect(() => {
    if (isValidating) {
      setCurrentTime(dayjs());
    }
  }, [isValidating]);

  // Calculate total users
  const totalUsers: number = data
    ? data.reduce((acc, user) => acc + Number(user.count), 0)
    : 0;

  // Modify the data to use new status names and add percentage
  const modifiedData: UserStatus[] | undefined = data?.map((user) => ({
    ...user,
    statusName: getModifiedStatusName(user.statusName),
    percentage: totalUsers > 0 ? (user.count / totalUsers) * 100 : 0,
  }));

  // Prepare data for the donut chart
  const chartData = modifiedData?.map((item) => ({
    name: item.statusName,
    value: item.count,
    color: STATUS_COLOR_MAP[item.statusName] || "#CCCCCC",
    percentage: item.percentage,
  }));

  // Skeleton for the table card
  const TableCardSkeleton = (): JSX.Element => (
    <Card>
      <CardHeader />
      <CardContent>
        <div className="space-y-2">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="flex justify-between">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/6" />
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <div className="flex justify-between w-full">
          <Skeleton className="h-4 w-1/4" />
          <Skeleton className="h-4 w-1/6" />
        </div>
      </CardFooter>
    </Card>
  );

  // Skeleton for the chart card
  const ChartCardSkeleton = (): JSX.Element => (
    <Card>
      <CardContent className="flex justify-center items-center min-h-[350px]">
        <Skeleton className="h-64 w-64 rounded-full" />
      </CardContent>
    </Card>
  );

  // Error message component
  const ErrorMessage = ({ message }: { message: string }): JSX.Element => (
    <Alert variant="destructive" className="col-span-1 xl:col-span-2">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Feil</AlertTitle>
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );

  // Custom Tooltip for Recharts
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background p-3 border rounded shadow-sm text-sm">
          <p className="font-semibold">{data.name}</p>
          <p>
            Antall: {data.value} ({data.percentage?.toFixed(1) ?? 0}%)
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom Legend for Recharts
  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="text-sm mt-4 text-center flex justify-center">
        <div className="grid grid-cols-2 gap-y-1 gap-x-8 shadow-sm border border-muted rounded p-2">
          {payload.map((entry: any, index: number) => (
            <div key={`legend-${index}`} className="flex items-center gap-2">
              <span
                className="inline-block w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-xs truncate" title={entry.value}>
                {entry.payload.name} -{" "}
                <span className="font-semibold">{entry.payload.value}</span> (
                {entry.payload.percentage?.toFixed(1) ?? 0}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="bg-header">
        <div className="container-wrapper py-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
            <div>
              <h1 className="text-3xl md:text-4xl">
                Kandidatstatus per status
              </h1>
              <p className="mt-4">
                Her er en oversikt over antall kandidater per status.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}

        <div className="mb-6">
          <p className="text-muted-foreground text-sm mt-1">
            Oppdatert:{" "}
            <span className="font-semibold">
              {currentTime.format("DD. MMMM YYYY HH:mm")}
            </span>
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Error Display */}
          {error && (
            <ErrorMessage message="Det oppstod en feil ved henting av kandidatstatus. Vennligst prøv igjen senere." />
          )}

          {/* Table Card */}
          <div className="lg:col-span-1">
            {!modifiedData && !error ? (
              <TableCardSkeleton />
            ) : modifiedData ? (
              <Card>
                <CardContent className="p-6">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-1/4 md:w-1/5">
                          Statuskode
                        </TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right w-fit">
                          Antall
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {modifiedData.length === 0 && !error ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center h-24">
                            Ingen data tilgjengelig.
                          </TableCell>
                        </TableRow>
                      ) : (
                        modifiedData.map((user) => (
                          <TableRow key={user.statusCode}>
                            <TableCell className="font-medium">
                              {user.statusCode}
                            </TableCell>
                            <TableCell>{user.statusName}</TableCell>
                            <TableCell className="text-right">
                              {user.count}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                    <TableFooter>
                      <TableRow>
                        <TableCell colSpan={2} className="font-semibold">
                          Total
                        </TableCell>
                        <TableCell className="text-right font-semibold">
                          {totalUsers}
                        </TableCell>
                      </TableRow>
                    </TableFooter>
                  </Table>
                </CardContent>
              </Card>
            ) : null}
          </div>

          {/* Chart Card */}
          <div className="lg:col-span-1">
            {!chartData && !error ? (
              <ChartCardSkeleton />
            ) : chartData ? (
              <Card>
                <CardContent className="relative min-h-[430px] p-2">
                  {chartData.length > 0 ? (
                    <ResponsiveContainer width="100%" height={430}>
                      <PieChart>
                        <Pie
                          data={chartData}
                          cx="50%"
                          cy="50%"
                          innerRadius={100} // Adjusted for better look
                          outerRadius={150} // Adjusted for better look
                          fill="#8884d8"
                          paddingAngle={3}
                          dataKey="value"
                          labelLine={false} // Optional: remove label lines
                        >
                          {chartData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                              stroke={entry.color}
                              strokeWidth={1}
                            />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomTooltip />} />
                        <Legend content={<CustomLegend />} />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                      Ingen data å vise i diagram.
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : null}
          </div>
        </div>
      </div>
    </>
  );
}
