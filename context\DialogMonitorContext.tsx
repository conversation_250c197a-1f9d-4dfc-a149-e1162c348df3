"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor"; // Forsikre deg om at denne stien er riktig
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TestPartsEnum } from "@/enums/TestPart";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import CandidateInfoModal from "@/app/[locale]/nedlasting/[slug]/candidateInfoModal";
import IpAdressDialog from "@/app/[locale]/pgs-monitor/ipAdressDialog";
import ActivityLog from "@/app/[locale]/pgs-monitor/activityLog";
import { Loader2 } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import ActivityLogDialog from "@/app/[locale]/pgs-monitor/activityLogDialog";

export type DialogType =
  | "info"
  | "ip"
  | "partSelect"
  | "undoDelivered"
  | "warning"
  | "warningAbsence"
  | null;

interface DialogCallbacks {
  onConfirmDeliveredOnPaper?: (testPartId: number) => Promise<void>;
  onConfirmUndoDelivered?: (testPartId: number) => Promise<void>;
  onConfirmSetAbsence?: () => Promise<void>;
}

interface DialogState {
  type: DialogType;
  candidate: ICandidateMonitor | null;
  callbacks: DialogCallbacks;
}

interface DialogContextType {
  openDialog: (
    type: DialogType,
    candidate: ICandidateMonitor,
    callbacks?: DialogCallbacks
  ) => void;
  closeDialog: () => void;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

interface DialogProviderProps {
  children: ReactNode;
}

export function DialogProvider({ children }: DialogProviderProps) {
  const [dialogState, setDialogState] = useState<DialogState>({
    type: null,
    candidate: null,
    callbacks: {},
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPart, setSelectedPart] = useState<number | null>(null);

  const openDialog = (
    type: DialogType,
    candidate: ICandidateMonitor,
    callbacks: DialogCallbacks = {}
  ) => {
    setDialogState({
      type,
      candidate,
      callbacks,
    });
    setSelectedPart(null);
  };

  const closeDialog = () => {
    if (isLoading) return;
    setDialogState({
      type: null,
      candidate: null,
      callbacks: {},
    });
    setSelectedPart(null);
  };

  const handleConfirm = async () => {
    if (!dialogState.candidate) return;

    setIsLoading(true);
    try {
      let actionCompleted = false;

      if (
        dialogState.type === "warning" &&
        dialogState.callbacks.onConfirmDeliveredOnPaper
      ) {
        await dialogState.callbacks.onConfirmDeliveredOnPaper(
          TestPartsEnum.Eksamen // Representerer hele eksamen for 'Sendes i posten'
        );
        actionCompleted = true;
      } else if (
        dialogState.type === "warningAbsence" &&
        dialogState.callbacks.onConfirmSetAbsence
      ) {
        await dialogState.callbacks.onConfirmSetAbsence();
        actionCompleted = true;
      } else if (
        dialogState.type === "partSelect" &&
        selectedPart &&
        dialogState.callbacks.onConfirmDeliveredOnPaper
      ) {
        await dialogState.callbacks.onConfirmDeliveredOnPaper(selectedPart);
        actionCompleted = true;
      } else if (
        dialogState.type === "undoDelivered" &&
        selectedPart &&
        dialogState.callbacks.onConfirmUndoDelivered
      ) {
        await dialogState.callbacks.onConfirmUndoDelivered(selectedPart);
        actionCompleted = true;
      }

      if (actionCompleted) {
        setDialogState({ type: null, candidate: null, callbacks: {} });
        setSelectedPart(null);
      }
    } catch (error) {
      console.error("Dialog confirm error:", error);
      toast({
        title: "En feil har oppstått",
        // Original error description text
        description: "Kunne ikke utføre handlingen. Vennligst prøv igjen.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to determine if the "Sendes i posten" (whole exam) option should be disabled
  const isSendesIPostenDisabled = (
    candidate: ICandidateMonitor | null
  ): boolean => {
    if (!candidate) return true;
    const part1Status = candidate.deliveryStatusPart1;
    const part2Status = candidate.deliveryStatusPart2;
    const part2Exists = part2Status !== -1;

    if (
      part1Status === CandidateStatusEnum.Levert ||
      (part2Exists && part2Status === CandidateStatusEnum.Levert)
    ) {
      return true;
    }

    if (!part2Exists) {
      return part1Status === CandidateStatusEnum.LevertManuelt;
    } else {
      return (
        part1Status === CandidateStatusEnum.LevertManuelt &&
        part2Status === CandidateStatusEnum.LevertManuelt
      );
    }
  };

  // Helper function to render the currently active dialog
  const renderActiveDialog = () => {
    if (!dialogState.candidate || !dialogState.type) {
      return null;
    }

    const candidate = dialogState.candidate;

    switch (dialogState.type) {
      case "info":
        return (
          <Dialog open={true} onOpenChange={closeDialog}>
            <DialogContent className="sm:max-w-[90%] sm:max-h-[90vh] overflow-auto">
              <DialogTitle></DialogTitle>
              <DialogDescription></DialogDescription>
              <CandidateInfoModal
                candidateInfo={candidate}
                subjectCode={candidate.subjectCode}
                subjectName={candidate.subjectName}
                testPeriod={candidate.testPeriod}
                
              />
            </DialogContent>
          </Dialog>
        );

      case "ip":
        return (
          <Dialog open={true} onOpenChange={closeDialog}>
            <DialogContent className="sm:max-w-[90%] md:max-w-[80%] lg:max-w-[70%] md:max-h-[90vh] overflow-y-auto overflow-x-hidden w-full block z-[200]">
              <DialogTitle></DialogTitle>
              <DialogDescription></DialogDescription>
              <div className="max-w-[1200px] mx-auto">
                <Tabs defaultValue="activityLog" className="w-full">
                  <TabsList className="flex overflow-x-auto h-full space-x-1 w-full max-w-[400px] mb-4 text-wrap ">
                    <TabsTrigger value="activityLog" className="flex-1">
                      Aktivitetslogg
                    </TabsTrigger>
                    <TabsTrigger value="activityLogV2" className="flex-1">
                      AktivitetsloggV2
                    </TabsTrigger>
                    <TabsTrigger value="ip" className="flex-1 ">
                      Innloggingssesjon/IP
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="activityLog" className="w-full">
                    <ActivityLog
                      redisObject={candidate.redisObject}
                      candidate={candidate}
                    />
                  </TabsContent>
                  <TabsContent value="activityLogV2" className="w-full">
                    <ActivityLogDialog candidate={candidate} subjectCode={candidate.subjectCode} examDate={candidate.partOneStartDateTime}  />
                  </TabsContent>
                  <TabsContent value="ip" className="w-full">
                    <IpAdressDialog
                      redisObject={candidate.redisObject}
                      candidate={candidate}
                    />
                  </TabsContent>
                </Tabs>
              </div>
            </DialogContent>
          </Dialog>
        );

      case "partSelect":
      case "undoDelivered":
        const part2Exists = candidate.deliveryStatusPart2 !== -1;
        return (
          <Dialog open={true} onOpenChange={closeDialog}>
            <DialogContent className="sm:max-w-[40%]">
              {/* Original Title/Description */}
              <DialogTitle>
                {dialogState.type === "partSelect"
                  ? "Velg eksamensdel"
                  : "Velg eksamensdel for å angre status 'Sendes i posten'"}
              </DialogTitle>
              <DialogDescription></DialogDescription>
              <div className="flex flex-col gap-2 pt-4">
                {/* Original Helper Texts */}
                {dialogState.type === "partSelect" && (
                  <>
                    <div className="text-sm">
                      Merk at statusen "Sendes i posten" kun skal benyttes
                      dersom skolen velger å sende hele besvarelsen via post.
                    </div>
                    <div className="text-sm text-red-600">
                      Sensor vil <i className="mx-1">ikke</i> se filer lastet
                      opp i PGS hvis du velger "Sendes i posten".
                    </div>
                  </>
                )}

                {/* Original OBS text */}
                {dialogState.type === "partSelect" &&
                  candidate.documents.length > 0 && (
                    <p className="text-red-500 text-sm">
                      <span className="font-bold">OBS:</span> For denne
                      kandidaten er det lastet opp en eller flere filer.
                    </p>
                  )}
                <div className="flex flex-col sm:flex-row gap-4 mt-3 items-start sm:items-center">
                  <Select
                    onValueChange={(value) => setSelectedPart(Number(value))}
                    value={selectedPart ? String(selectedPart) : undefined}
                  >
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Eksamensdel" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {/* Del 1 */}
                        <SelectItem
                          value={TestPartsEnum.EksamenDel1.toString()}
                          disabled={
                            (dialogState.type === "partSelect" &&
                              (candidate.deliveryStatusPart1 ===
                                CandidateStatusEnum.Levert ||
                                candidate.deliveryStatusPart1 ===
                                  CandidateStatusEnum.LevertManuelt)) ||
                            (dialogState.type === "undoDelivered" &&
                              candidate.deliveryStatusPart1 !==
                                CandidateStatusEnum.LevertManuelt)
                          }
                        >
                          Del 1
                          {dialogState.type === "partSelect" &&
                            candidate.deliveryStatusPart1 ===
                              CandidateStatusEnum.Levert &&
                            " (Levert digitalt)"}
                        </SelectItem>

                        {/* Del 2 (conditional) */}
                        {part2Exists && (
                          <SelectItem
                            value={TestPartsEnum.EksamenDel2.toString()}
                            disabled={
                              (dialogState.type === "partSelect" &&
                                (candidate.deliveryStatusPart2 ===
                                  CandidateStatusEnum.Levert ||
                                  candidate.deliveryStatusPart2 ===
                                    CandidateStatusEnum.LevertManuelt)) ||
                              (dialogState.type === "undoDelivered" &&
                                candidate.deliveryStatusPart2 !==
                                  CandidateStatusEnum.LevertManuelt)
                            }
                          >
                            Del 2
                            {dialogState.type === "partSelect" &&
                              candidate.deliveryStatusPart2 ===
                                CandidateStatusEnum.Levert &&
                              " (Levert digitalt)"}
                            {/* Add back original text if needed */}
                          </SelectItem>
                        )}

                        {/* Del 1 og Del 2 (conditional) */}
                        {part2Exists && (
                          <SelectItem
                            value={TestPartsEnum.EksamenDel1ogDel2.toString()}
                            disabled={
                              (dialogState.type === "partSelect" &&
                                (candidate.deliveryStatusPart1 ===
                                  CandidateStatusEnum.Levert ||
                                  candidate.deliveryStatusPart1 ===
                                    CandidateStatusEnum.LevertManuelt ||
                                  candidate.deliveryStatusPart2 ===
                                    CandidateStatusEnum.Levert ||
                                  candidate.deliveryStatusPart2 ===
                                    CandidateStatusEnum.LevertManuelt)) ||
                              (dialogState.type === "undoDelivered" &&
                                !(
                                  candidate.deliveryStatusPart1 ===
                                    CandidateStatusEnum.LevertManuelt &&
                                  candidate.deliveryStatusPart2 ===
                                    CandidateStatusEnum.LevertManuelt
                                ))
                            }
                          >
                            Del 1 og del 2
                          </SelectItem>
                        )}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <div className="flex gap-2 w-full sm:w-auto justify-end mt-4 sm:mt-0">
                    <Button
                      variant="outline"
                      onClick={closeDialog}
                      disabled={isLoading}
                      className="flex-grow sm:flex-grow-0"
                    >
                      Avbryt
                    </Button>
                    <Button
                      variant="default"
                      className="w-28 flex-grow sm:flex-grow-0" // Original width
                      onClick={handleConfirm}
                      disabled={!selectedPart || isLoading}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2
                            className="h-4 w-4 animate-spin" // Or original 'text-xl' ?
                            role="img"
                            aria-label="spinner"
                          />
                          {/* Original Loading Text */}
                          <span>Vent...</span>
                        </div>
                      ) : (
                        // Original Confirm Text
                        "Bekreft"
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        );

      case "warning":
        return (
          <Dialog open={true} onOpenChange={closeDialog}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogTitle>Sendes i posten</DialogTitle>
              <DialogDescription></DialogDescription>
              <div className="flex flex-col gap-2 pt-4">
                <div className="text-sm">
                  Merk at statusen "Sendes i posten" kun skal benyttes dersom
                  skolen velger å sende hele besvarelsen via post.
                </div>
                <span className="text-sm text-red-600">
                  Sensor vil <i className="mx-1">ikke</i> se filer lastet opp i
                  PGS hvis du velger "Sendes i posten".
                </span>

                {/* Original OBS Text */}
                {candidate.documents.length > 0 && (
                  <p className="text-red-500 text-sm mt-2">
                    <span className="font-bold">OBS:</span> For denne kandidaten
                    er det lastet opp en eller flere filer.
                  </p>
                )}
                {/* Removed the extra warning about digital delivery to keep it original */}
                <div className="mt-4">
                  {" "}
                  {/* Original had space-y-4 wrapper? Keeping simplified mt-4 */}
                  <div className="flex justify-end gap-3">
                    <Button
                      variant="outline"
                      onClick={closeDialog}
                      disabled={isLoading}
                    >
                      Avbryt
                    </Button>
                    <Button
                      variant="default" // Original variant
                      onClick={handleConfirm}
                      disabled={isLoading}
                      className="w-28" // Original width? Check if needed
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2
                            className="h-4 w-4 animate-spin" // Or original 'text-xl' ?
                            role="img"
                            aria-label="spinner"
                          />
                          {/* Original Loading Text */}
                          <span>Venter...</span>
                        </div>
                      ) : (
                        // Original Confirm Text
                        "Bekreft"
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        );

      case "warningAbsence":
        const hasDigitalDelivery =
          candidate.deliveryStatusPart1 === CandidateStatusEnum.Levert ||
          (candidate.deliveryStatusPart2 !== -1 &&
            candidate.deliveryStatusPart2 === CandidateStatusEnum.Levert);
        const hasManualDelivery =
          candidate.deliveryStatusPart1 === CandidateStatusEnum.LevertManuelt ||
          (candidate.deliveryStatusPart2 !== -1 &&
            candidate.deliveryStatusPart2 ===
              CandidateStatusEnum.LevertManuelt);

        return (
          <Dialog open={true} onOpenChange={closeDialog}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogTitle>Varsel om setting av fravær</DialogTitle>
              <DialogDescription></DialogDescription>
              <div className="flex flex-col gap-2 pt-4">
                {hasDigitalDelivery && (
                  <div>
                    <p className="">
                      Kandidaten har levert en besvarelse, er du sikker på at du
                      vil sette fravær?
                    </p>
                  </div>
                )}
                {hasManualDelivery && (
                  <div>
                    <p className="">
                      Kandidaten har fremdriftsstatus "Sendes i posten" noe som
                      indikerer at kandidaten har levert inn sin besvarelse på
                      papir.
                    </p>
                  </div>
                )}

                <p className="mt-2">
                  {" "}
                  Ved å sette fravær på kandidaten vil besvarelsen ikke bli
                  vurdert av sensor.
                </p>
                <div className="mt-4">
                  {" "}
                  <div className="flex justify-end gap-3">
                    <Button
                      variant="outline"
                      onClick={closeDialog}
                      disabled={isLoading}
                    >
                      Avbryt
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleConfirm}
                      disabled={isLoading}
                      className="w-28"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2
                            className="h-4 w-4 animate-spin"
                            role="img"
                            aria-label="spinner"
                          />

                          <span>Venter...</span>
                        </div>
                      ) : (
                        "Bekreft"
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        );

      default:
        const _exhaustiveCheck: never = dialogState.type;
        return null;
    }
  };

  return (
    <DialogContext.Provider value={{ openDialog, closeDialog }}>
      {children}
      {renderActiveDialog()}
    </DialogContext.Provider>
  );
}

export function useDialogMonitor() {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error("useDialogMonitor must be used within a DialogProvider");
  }
  return context;
}
