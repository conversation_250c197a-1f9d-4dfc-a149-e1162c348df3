"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";

function minimizeSuffix(filename: string) {
  const parts = filename.split(".");
  if (parts.length > 1) {
    const suffix = parts.pop();
    return parts.join(".") + "." + suffix?.toLowerCase();
  }

  return filename;
}
export const columnsError: ColumnDef<IUploadedFile>[] = [
  {
    accessorKey: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all errors"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label={`Select row ${row.original.FileGuid} `}
      />
    ),
  },
  {
    accessorKey: "Name",
    header: ({ table, column }) => {
      const rowCount = table.getRowCount();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize text-black">Filer ({rowCount})</div>{" "}
          <ArrowUpDown className="ml-2 h-4 w-4" role="img" aria-label="pil opp ned ikon"/>
        </Button>
      );
    },
    cell: ({ row }) => {
      const file = row.original.File;
      return <div>{minimizeSuffix(file.name)}</div>;
    },
    enableSorting: true,
  },

  {
    accessorKey: "Errors",
    header: ({ table, column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => {
            column.toggleSorting(column.getIsSorted() === "asc");
          }}
        >
          <div className="capitalize text-black">Feil</div>{" "}
          <ArrowUpDown className="ml-2 h-4 w-4" role="img" aria-label="pil opp ned ikon"/>
        </Button>
      );
    },
    cell: ({ row }) => {
      const errors = row.original.Errors;
      return (
        <ul>
          {errors?.map((error, index) => (
            <li className="list-disc list-inside" key={index}>
              {error}
            </li>
          ))}
        </ul>
      );
    },
    enableSorting: true,
  },
];
