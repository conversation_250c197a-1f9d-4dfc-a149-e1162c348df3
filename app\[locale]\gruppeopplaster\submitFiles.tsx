"use client";

import React, { use<PERSON><PERSON>back, useEffect, useState, useRef } from "react";
import { toast } from "@/components/ui/use-toast";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "../../../components/tables/gruppeopplaster-fileTable/data-table";
import { DataTableError } from "../../../components/tables/gruppeopplaster-fileTableError/data-table-error";
import { DataTableSubmitted } from "@/components/tables/gruppeopplaster-fileTableSubmitted/data-table-submitted";
import { columnsSubmitted } from "@/components/tables/gruppeopplaster-fileTableSubmitted/columns";
import { columnsError } from "../../../components/tables/gruppeopplaster-fileTableError/columnsError";
import { FileUploadArea } from "../../../components/fileUploadArea";
import useFileHandler from "@/hooks/useFileHandler";
import { useRole } from "@/context/RoleContext";
import {
  handleRejectedFile,
  fileValidator,
  deliverFiles,
  updateTotalSizeAndFiles,
  handleAcceptedFiles,
} from "./fileUtils";
import { Button } from "@/components/ui/button";
import { VscSend } from "react-icons/vsc";
import { ImSpinner2 } from "react-icons/im";
import SchoolSelector from "@/components/schoolSelector";
import { Loader2 } from "lucide-react";
import { FaCircleInfo } from "react-icons/fa6";
import { IUploadedFile } from "@/interface/IUploadedFile";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { logEvent, logMetric, logPageView } from "@/lib/appInsightsClient";
import GroupUploadSkeleton from "./groupUploadSkeleton";
import { FileRejection } from "react-dropzone";

interface ISubmitFilesProps {
  mimeTypes: IAllowedMimeTypes[];
}

interface BatchInfo {
  totalFiles: number;
  uploadedFiles: number;
  rejectedFiles: number;
  totalSize: number;
}

export default function SubmitFiles({ mimeTypes }: ISubmitFilesProps) {
  const { uploadedFiles, addFile, deliverFile, isUploading } = useFileHandler();
  const { selectedRole } = useRole();
  const [totalSize, setTotalSize] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [totalFiles, setTotalFiles] = useState(0);
  const [selectedTab, setSelectedTab] = useState("opplastedeFiler");
  const [batchInfo, setBatchInfo] = useState<BatchInfo>({
    totalFiles: 0,
    uploadedFiles: 0,
    rejectedFiles: 0,
    totalSize: 0,
  });
  const [errorFiles, setErrorFiles] = useState<IUploadedFile[]>([]);
  const errorTabRef = useRef<HTMLButtonElement>(null);

  const MAX_BATCH_FILE_COUNT = 100;
  const MAX_BATCH_SIZE = 300 * 1024 * 1024; // 300 MB in bytes

  // Update total size and files when uploadedFiles changes
  useEffect(() => {
    const { totalSize: newTotalSize, totalFiles: newTotalFiles } =
      updateTotalSizeAndFiles(uploadedFiles);
    setTotalSize(newTotalSize);
    setTotalFiles(newTotalFiles);
    const newErrorFiles = uploadedFiles.filter((file) => file.IsRejected);
    setErrorFiles(newErrorFiles);
  }, [uploadedFiles]);

  useEffect(() => {
    const logPage = async () => {
      await logPageView("PGSX - Gruppeopplasting");
    };
    logPage();
  }, []);

  useEffect(() => {
    if (errorFiles.length > 0) {
      setSelectedTab("feilFiler");
    }
  }, [errorFiles.length]);

  // Handle file drop
  const onDropFiles = async (
    acceptedFiles: File[],
    fileRejections: FileRejection[]
  ) => {
    if (isUploading) {
      return;
    }

    setSelectedTab("opplastedeFiler");
    resetBatchInfo(acceptedFiles, fileRejections);

    try {
      await processFiles(acceptedFiles, fileRejections);
    } catch (error) {
      handleUploadError(error);
    } finally {
      if (fileRejections.length > 0) setSelectedTab("feilFiler");
    }
  };

  const processFiles = async (
    acceptedFiles: File[],
    fileRejections: FileRejection[]
  ) => {
    let allFilesValid = true;
    let successfullyUploadedCount = 0;

    try {
      const batchTotalFiles = acceptedFiles.length + fileRejections.length;
      const batchTotalSize = acceptedFiles.reduce(
        (sum, file) => sum + file.size,
        0
      );

      // Check batch limits
      if (batchTotalFiles > MAX_BATCH_FILE_COUNT) {
        toast({
          variant: "destructive",
          title: "For mange filer",
          description: `Du kan ikke laste opp mer enn ${MAX_BATCH_FILE_COUNT} filer om gangen.`,
        });
        return;
      }

      if (batchTotalSize > MAX_BATCH_SIZE) {
        toast({
          variant: "destructive",
          title: "Størrelse overskredet",
          description: `Total filstørrelse for hver opplasting kan ikke overstige ${
            MAX_BATCH_SIZE / (1024 * 1024)
          } MB.`,
        });
        return;
      }

      // Handle accepted files
      for (const file of acceptedFiles) {
        const isValid = await handleAcceptedFiles(
          [file],
          uploadedFiles.filter(
            (f) => f.SchoolId === selectedRole?.selectedSchoolId
          ),
          handleFileUpload,
          mimeTypes,
          selectedRole?.selectedSchoolId ?? ""
        );

        if (isValid) {
          successfullyUploadedCount++;
        }
      }

      // Handle rejected files
      for (const rejection of fileRejections) {
        await handleRejectedFile(rejection, addFile);
      }

      // Update batch info
      updateBatchInfo({
        totalFiles: batchTotalFiles,
        uploadedFiles: successfullyUploadedCount,
        rejectedFiles: fileRejections.length,
        totalSize: batchTotalSize,
      });

      await logMetric("groupUploadBatch", batchTotalFiles, {
        uploadedFiles: successfullyUploadedCount,
        rejectedFiles: fileRejections.length,
        numberOfFilesInBatch: batchTotalFiles,
        totalSizeOfBatch: batchTotalSize,
      });
    } catch (error) {
      console.error("Error processing files:", error);
      toast({
        variant: "destructive",
        title: "En feil oppstod under behandling av filer",
        description:
          "Vennligst prøv igjen. Hvis problemet vedvarer, kontakt support.",
      });
    } finally {
      if (!allFilesValid) {
        setSelectedTab("feilFiler");
      }
    }
  };

  // Reset batch info for new uploads
  const resetBatchInfo = (acceptedFiles: File[], fileRejections: any[]) => {
    const totalFiles = acceptedFiles.length + fileRejections.length;
    const totalSize = acceptedFiles.reduce((sum, file) => sum + file.size, 0);

    setBatchInfo({
      totalFiles,
      uploadedFiles: 0, // Start med 0, vil oppdateres under opplasting
      rejectedFiles: 0,
      totalSize,
    });
  };

  // Handle individual file upload
  const handleFileUpload = async (file: IUploadedFile) => {
    await addFile(file);
    setBatchInfo((prevInfo) => ({
      ...prevInfo,
      uploadedFiles: !file.IsRejected
        ? prevInfo.uploadedFiles + 1
        : prevInfo.uploadedFiles,
      rejectedFiles: file.IsRejected
        ? prevInfo.rejectedFiles + 1
        : prevInfo.rejectedFiles,
    }));

    if (file.IsRejected) {
      // Check if a file with the same name already exists in errorFiles
      setErrorFiles((prev) => {
        const fileExists = prev.some(
          (existingFile) => existingFile.Name === file.Name
        );
        if (!fileExists) {
          return [...prev, file];
        }
        return prev;
      });
    }
  };

  // Update batch info
  const updateBatchInfo = (newInfo: BatchInfo) => {
    setBatchInfo(newInfo);
  };

  // Handle upload error
  const handleUploadError = (error: any) => {
    console.error("Error processing files:", error);
    toast({
      variant: "destructive",
      title: "En feil er oppstått under opplasting av filer",
      description:
        "Vennligst prøv igjen senere. Dersom problemet vedvarer, kontakt brukerstøtte.",
    });
  };

  // Handle file submission
  const handleSubmitFiles = async () => {
    const fileMissingExamPart = await deliverFiles(
      uploadedFiles,
      deliverFile,
      selectedRole?.selectedSchoolId
    );
    if (fileMissingExamPart) {
      setIsOpen(true);
    } else setSelectedTab("leverteFiler");
  };

  // Render tabs
  const renderTabs = () => {
    const uploadedFilesCount = uploadedFiles.filter(
      (file) =>
        !file.IsRejected &&
        !file.Delivered &&
        selectedRole?.selectedSchoolId === file.SchoolId
    ).length;
    const deliveredFilesCount = uploadedFiles.filter(
      (file) =>
        file.Delivered &&
        selectedRole?.selectedSchoolId === file.SchoolId
    ).length;
    const errorFilesCount = errorFiles.length;
    const circularBadgeStyle =
      "rounded-full w-5 h-5 flex items-center justify-center text-[11px] mr-1 text-white drop-shadow-sm";

    return (
      <Tabs
        defaultValue={selectedTab}
        className="relative mr-auto w-full"
        value={selectedTab}
        onValueChange={setSelectedTab}
      >
        <TabsList className="text-lg">
          <TabsTrigger value="feilFiler" ref={errorTabRef}>
            <div className={`${circularBadgeStyle} bg-red-600`}>
              {errorFilesCount}
            </div>
            Filer med feil
          </TabsTrigger>

          <TabsTrigger value="opplastedeFiler">
            <div className={`${circularBadgeStyle} bg-primary`}>
              {uploadedFilesCount}
            </div>
            Opplastede filer
          </TabsTrigger>
          <TabsTrigger value="leverteFiler">
            <div className={`bg-green-700 ${circularBadgeStyle}`}>
              {deliveredFilesCount}
            </div>
            Leverte filer
          </TabsTrigger>
        </TabsList>

        <TabsContent value="feilFiler">
          <div className="mt-6">
            <div>
              <DataTableError columnsFeil={columnsError} data={errorFiles} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="opplastedeFiler">
          <div className="mt-6">
            <div>
              <DataTable
                data={uploadedFiles.filter(
                  (file) =>
                    !file.IsRejected &&
                    !file.Delivered &&
                    selectedRole?.selectedSchoolId === file.SchoolId
                )}
                columns={[]}
                isUploading={isUploading}
              />
            </div>
            <Button
              variant="default"
              className="mt-6 h-10 rounded-[3px] normal-case font-normal w-full sm:w-44"
              onClick={handleSubmitFiles}
              disabled={isUploading || uploadedFilesCount === 0}
              aria-label="Lever alle filer som er klare for levering"
            >
              {uploadedFiles.some((file) => file.IsSubmitting) ? (
                <>
                  <ImSpinner2
                    className="animate-spin mr-2 text-lg"
                    aria-hidden="true"
                    role="img"
                    aria-label="spinner ikon"
                  />
                  <span>Leverer filer...</span>
                </>
              ) : (
                <>
                  <VscSend
                    className="h-5 w-5 mr-2"
                    aria-hidden="true"
                    role="img"
                    aria-label="send ikon"
                  />
                  <span>Lever alle filer</span>
                </>
              )}
            </Button>
          </div>
        </TabsContent>
        <TabsContent value="leverteFiler">
          <div className="mt-6">
            <div>
              <DataTableSubmitted
                columns={columnsSubmitted}
                data={uploadedFiles.filter(
                  (file) =>
                    file.Delivered &&
                    selectedRole?.selectedSchoolId === file.SchoolId
                )}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    );
  };

  if (!selectedRole) {
    return <GroupUploadSkeleton />;
  }

  return (
    <div className="flex flex-col gap-6">
      {selectedRole && (
        <div className="flex flex-col gap-1 bg-stalbla rounded-md p-3 w-full sm:w-fit">
          <span className="text-sm font-medium">Last opp filer for:</span>{" "}
          <SchoolSelector />
        </div>
      )}
      <div className="flex items-center gap-2">
        <FaCircleInfo
          className="text-lg text-primary"
          role="img"
          aria-label="Info ikon"
        />
        <p className="text-xs md:text-sm">
          Maksgrense er totalt <span className="font-bold">100</span> filer
          eller <span className="font-bold">300 MB</span> om gangen.
        </p>
      </div>
      {selectedRole.selectedSchoolId && (
        <>
          <FileUploadArea
            onDrop={onDropFiles}
            isLoading={isUploading}
            fileValidator={(file) =>
              fileValidator(file, mimeTypes, uploadedFiles, selectedRole)
            }
            totalFilesInBatch={batchInfo.totalFiles}
            numUploadedFiles={batchInfo.uploadedFiles}
            numFilesRejected={batchInfo.rejectedFiles}
            totalSize={batchInfo.totalSize}
          />
          <div className="flex flex-col gap-10">{renderTabs()}</div>
        </>
      )}
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Noen filer mangler eksamensdel</AlertDialogTitle>
            <AlertDialogDescription>
              Filer som mangler eksamensdel kan ikke leveres. Filer med gyldig
              eksamensdel har blitt levert.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button
              variant="default"
              className="w-20"
              onClick={() => setIsOpen(false)}
            >
              Ok
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
