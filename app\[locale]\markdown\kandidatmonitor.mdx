## Innledning

Denne brukerveiledningen er laget for eksamensvakter som skal benytte PGS-monitor i
PGS. Merk at brukerstøtte går tjenestevei. For eksempel skal eksamensvakter kontakte sin
skoleleder eller eksamensansvarlig på skolen.

PGS er det gamle gjennomføringssystemet for eksamen. For eksamensgjennomføringen våren
2024 (V-2024) benyttes PGS for todelte eksamener etter [LK20](https://www.udir.no/laring-og-trivsel/lareplanverket/) og alle eksamener etter LK06.
Det vil si at alle todelte eksamener gjennomføres i PGS.

De fleste eksamenene etter LK20 vil for V-2024 skje i gjennomføringssystemet for heldigital
eksamen (EPS), også kjent som nytt gjennomføringssystem. Majoriteten av kandidatene skal
gjennomføre eksamen i EPS. Hvis du er vakt for noen av disse, vil du i PAS-eksamen se en lenke
til «monitorering av eksamener etter ny læreplan». Du finner veiledning for EPS-monitoren i PAS,
i monitorbildet.

PGS består av en kandidatmodul som benyttes kun av kandidater, og en administrasjonsmodul
for eksamensvakter og skoleadministratorer.

Administrasjonsmodulen har tre hovedfunksjoner: PGS-monitor, gruppeopplastning og
nedlastning av tidligere eksamensbesvarelser. PGS-monitor og gruppeopplastning er
tilgjengelig for alle som er tildelt rolletilgangen "kandidatgruppeansvarlig" for PGS. Nedlastning
av tidligere eksamensbesvarelser er kun tilgjengelig for de som er kandidatgruppeansvarlig og i
tillegg har rolletilgangen "skoleadministrator" i PAS-eksamen. Dersom du er usikker på hvilke
tilganger du har, kan du får oversikt over dine tildelte roller og systemtilganger ved å gå til
[minkonto.udir.no](minkonto.udir.no). Tilgangene tildeles av skoleadministrator i PAS.

Denne brukerveiledningen forklarer hvordan PGS-monitor i PGS benyttes og hvordan du kan
levere en besvarelse på vegne av en kandidat.

Merk at det er flere brukerveiledninger for PGS. Det er egne brukerveiledninger for
kandidatgjennomføring og gruppeopplastning. Disse finner du på:

[https://www.udir.no/eksamen-og-prover/eksamen/administrere-eksamen/#a110486](https://www.udir.no/eksamen-og-prover/eksamen/administrere-eksamen/#a110486)

<br />
På udir.no finner du også nyttig informasjon om organisering og gjennomføring av
sentralt gitt skriftlig eksamen:

[https://www.udir.no/eksamen-og-prover/eksamen/](https://www.udir.no/eksamen-og-prover/eksamen/)

## Naviger til PGS-monitor

Logg inn som eksamensvakt eller skoleadministrator i PAS-eksamen på [eksamen.udir.no](eksamen.udir.no).
Gå til «Monitor» og «Gå til monitorering av PGS eksamener». Du kommer deretter til
administrasjonsmodulen i PGS. Her velger du funksjonen "PGS-monitor".

Under ser du et skjermbilde av PGS-monitor. Alle persondata som vises i
skjermbilder i denne veiledningen er fiktive data.

![PGS admin](/images/kandidatmonitor.png)

## Finn kandidatene dine

I PGS-monitor får du oversikt over kandidatene dine.

1. Velg skole, fag og eventuelt kandidatgruppe for å se kandidatene.
2. Kandidatene vises med fullt navn, kandidatnummer, kandidatgruppe og hvilken status de har i PGS. Status vises per eksamensdel. For eksamener med én del er kolonnen «Del 2» tom.
3. Du kan også søke etter kandidater i søkefeltet.

![finn kandidater](/images/monitor-finnKandidater.png)

## Oversikt over PGS-monitor

I dette kapitlet gis det en kort beskrivelse av funksjonaliteten i PGS-monitor.

### Varighet

Varighet vises når man har valgt fagkode i PGS-monitor. Eksamen starter offisielt kl.
09:00, og derfor vil dette alltid være starttidspunktet. Avhengig av fagkoden er sluttidspunktet
13:00 eller 14:00. I enkelte tilfeller kan en kandidat ha fått innvilget utvidet tid. Det kan også i
sjeldne tilfeller oppstå avbrudd i gjennomføringen som gjør at det er aktuelt å utvide
eksamenstiden. Det er derfor ingen frister eller sperrer i PGS når det kommer til sluttidspunktet

For todelt eksamen vil ikke varigheten vise oppstartstidspunktet for del to, men når del 1 starter
og når del 2 avsluttes.

<h3 id="totaloversikt"> Totaloversikt </h3>

Totaloversikten viser oversikt over alle kandidatene på skolen du representerer. Den viser antall
påmeldte kandidater, antall med fravær og antall som har levert. For todelt eksamen vil man se
en telling for hver eksamensdel.

![totaloversikt](/images/monitor-totaloversikt.png)

### Minimer/maksimer

Trykker du på knappen med pil opp vil du minimere søkevinduet. Hensikten er å se flere av
kandidatene samtidig på skjermen. Knappen vil endre seg til pil ned, og trykker du denne vil du
maksimere søkevinduet igjen.

### Oppdater

PGS er basert på gammel teknologi. PGS-monitor oppdateres ikke automatisk, men du
må selv gjøre dette ved å klikke knappen "Oppdater". Du skal alltid klikke "Oppdater" rett før du
endrer status på en kandidat, i tilfelle kandidatens status har endret seg.

     ## Vis IP og online-status

Ved klikk på knappen vises IP og onlinestatus for kandidatene. Dette er beskrevet nærmere i et
eget kapittel senere i denne veiledningen.

## Hent kandidatliste fra PAS

Ved klikk på knappen hentes eventuelt nye etterpåmeldte kandidater fra PAS-eksamen. Dette er
beskrevet nærmere i et eget kapittel senere i denne veiledningen.

## Last opp

Ved klikk på knappen "Last opp" for en spesifikk kandidat kommer du til en egen side for å
levere besvarelsesfiler på vegne av den valgte kandidaten. Dette er beskrevet i detalj senere i
denne veiledningen.

## Vis

Hvis du klikker på knappen "Vis" for en spesifikk kandidat laster du ned alle besvarelsesfiler for
kandidaten som en zip-fil. Dette gjelder alle besvarelsesfiler enten de er lastet opp av en
eksamensvakt eller kandidaten, og om de har status "Lastet opp" eller "Levert". For todelt
eksamen vil filer fra både del 1 og del 2 lastes ned i zip-filen.

       <h2 id="endre-statusfravr">Endre status/fravær</h2>

I PGS-monitor kan du huke av en kandidat og trykke «Endre status/fravær». Du vil da
kunne velge hvilken status kandidaten skal få. Valgene du får opp avhenger av kandidatens
nåværende status.

    Dersom du huker av flere kandidater, vil du endre statusen på alle valgte kandidatene. Det
    tryggeste er å endre status hver kandidat for seg.

    <br />
    Merk at en kandidatstatus kan endre seg av flere årsaker:

    - at en kandidat logger på, autoriseres med dagspassord, laster opp en fil eller leverer
        besvarelsen sin
    - at en eksamensvakt endrer status i PGS-monitor, leverer på vegne av kandidat eller
        leverer via gruppeopplastning
    - at skoleadministrator gjør en endring i PAS-eksamen; setter fravær eller angir at
        kandidaten har deltatt

    I skjermbildet under vises statusendring for en enkelt kandidat som har eksamen med én del:

        ![endre status](/images/endrestatus.png)

## Gi tilgang til kandidater

> ## Infoboks
>
> Med **autentisering** mener vi her prosessen med å bekrefte en identitet. Dette skjer ved at kandidaten logger på
> med Feide eller kandidatnummer og passord. Autentiseringen foregår i Udir sin løsning for identitets- og tilgangskontroll:
> [UIDP](https://uidp-admin.udir.no/info/om-uidp).
>
> Med **autorisering** mener vi her prosessen med å avgjøre om en kandidat skal få tilgang til
> PGS. Kandidaten autoriseres ved at eksamensvakten gir tilgang i PGS-monitor eller at
> dagspassord benyttes.

Kandidaten autentiserer seg med sin Feide-bruker, eller med kandidatnummer og
kandidatpassord på innloggingssiden i UIDP. Skoleadministrator har tilgang på
kandidatnummer og kandidatpassord i PAS-eksamen. Der finner man også dagspassordet for
de forskjellige kandidatgruppene.

Se den separate brukerveiledningen for kandidatgjennomføring i PGS for en nærmere
beskrivelse av prosessen som kandidaten går igjennom ved innlogging.

Merk at kandidaten med fordel kan få tilgang til PGS i god tid før eksamen starter. Kandidaten
får uansett ikke tilgang til eksamensoppgaven i PGS før eksamen offisielt starter. Dette gjelder
også for todelt eksamen.

Kandidater som ikke tidligere har logget inn i PGS vil ha statusen «Ikke innlogget» i
PGS-monitor. Dette forutsetter nødvendigvis at eksamensvakten ikke har satt en annen
status for kandidaten (eksempelvis «Ikke-dokumentert fravær).

Når kandidaten har autentisert seg og logget inn i PGS vil hen komme til autoriseringssiden i
kandidatmodulen. Kandidatens navn vil vises i overskriften på siden. Kandidaten vil da ha status
«Venter på tilgang/dagspassord». Kandidaten har på dette tidspunktet ikke fått tilgang til PGS.

Som eksamensvakt har du en viktig rolle for å bidra til en sikker og rettferdig gjennomføring av
eksamen. Du bør påse at kandidaten er til stede i eksamenslokalet og er logget på med sin egen
bruker i PGS før du gir digital tilgang til PGS. Du kan også kontrollere at IP-adressen til 
kandidaten om du ønsker – du kan lese mer om dette i kapittelet om innloggingssesjon.

For at kandidaten skal få tilgang til eksamensoppgaven, og kunne levere eksamen digitalt må
kandidaten få digital tilgang (autoriseres) av en eksamensvakt i PGS-monitor. Husk å
oppdatere PGS-monitor før du gir tilgang. I PGS-monitor huker du av kandidaten som
skal ha tilgang og klikker på knappen «Endre status/fravær». Deretter klikker du på valget «Skal
levere digitalt» i dialogboksen som kommer opp. Videre gir du beskjed til kandidaten at hen har
fått tilgang. Kandidaten må klikke på knappen «Jeg har fått tilgang» på autoriseringssiden etter
at digital tilgang er gitt i PGS-monitor. Dersom eksamen ikke har startet vil kandidaten få
status «Venter på eksamensstart» og komme til ventesiden i kandidatmodulen. Dersom
eksamen har startet vil kandidaten få status «Startet» og komme til oppgavesiden i
kandidatmodulen.

Dersom kandidaten har fått digital tilgang av eksamensvakten før hen autentiserer seg, vil
kandidaten ha status «Autorisert». Dersom kandidaten så logger seg på og eksamen ikke har
startet, vil kandidaten sendes direkte til ventesiden etter autentisering på innloggingssiden.
Kandidaten vil da få status «Venter på eksamensstart». Dersom kandidaten logget seg på og
eksamen har startet, vil kandidaten sendes direkte til oppgavesiden etter autentisering på
innloggingssiden. Kandidaten vil da få status «Startet».

Dagpassord kan benyttes som et alternativ til at det gis tilgang via PGS-monitor.
Eksamensvakten skal da skrive inn dagspassordet i nettleseren til kandidaten. Merk at
dagspassordet skal være hemmelig og ikke deles med kandidaten. Dagspassord er tilgjengelig
for skoleadministrator i PAS-eksamen.

I noen sjeldne tilfeller vil det kun være mulig å autorisere kandidaten med dagspassord. Et
eksempel på dette er hvis kandidaten har lastet opp sine filer, men ikke har levert, og så ved en
feiltagelse logger ut. Da vil kandidaten ha status «Laster opp». Dersom kandidaten logger på
igjen vil det ikke være mulig å autorisere kandidaten via PGS-monitor – dagspassord må
benyttes.

Det er viktig å merke seg at autoriseringen må gjennomføres hver gang kandidaten logger inn
på nytt. Det vil si hver gang en ny brukersesjon starter. Noen eksempler på dette er:

- Dersom kandidaten logger ut for så å logge på igjen, må hen autoriseres på nytt.
- Dersom kandidaten allerede er autorisert i en nettleser (eksempelvis Edge), men ønsker å
  benytte en annen nettleser (eksempelvis Firefox), må kandidaten autoriseres på nytt når
  hen logger inn i PGS med Firefox.
- Dersom kandidaten allerede er autorisert på sin pc, men kandidaten må skifte pc pga.
  tekniske problemer. Kandidaten må da autoriseres på nytt når hen logger inn i PGS på
  den nye pc-en.

Det er mulig å angre valget om digital tilgang dersom kandidaten ennå ikke har logget på PGS.
Du kan da huke kandidaten og trykke «Endre status/fravær», og så velge «Skal ikke ha tilgang til
å levere digitalt». Merk at valget ikke vil føre til at kandidaten blir logget ut dersom hen allerede
er pålogget. Valget vil heller ikke hindre kandidaten fra å autentisere seg og logge på PGS.

Merk at for todelt eksamen kan det kun gis digital tilgang til del 2. Del 1 for todelt eksamen skal
kun gjennomføres på papir, og del 1 er derfor ikke tilgjengelig i PGS for kandidatene.

<h2 id="slette-fravr"> Slette fravær</h2>

Skriftlig eksamen starter kl. 09:00 norsk tid. Elever som møter før kl. 10:00, får gjennomføre
eksamen, men de får ikke kompensert tapt tid. De som møter etter kl. 10:00 får ikke
gjennomføre eksamen.

Dersom skolen har mottatt skriftlig dokumentasjon på fravær, så settes status «Dokumentert
fravær». Ellers settes status «Ikke-dokumentert fravær» ved fravær. Kandidater som har
dokumentert fravær ved eksamen, har rett til å gjennomføre første etterfølgende eksamen.

I PGS-monitor huker du av kandidaten som skal ha ikke-dokumentert fravær og klikker på
knappen «Endre status/fravær». Deretter klikker du på valget «Ikke-dokumentert fravær» i
dialogboksen som kommer opp. Kandidaten får da status «Ikke-dokumentert fravær».
Fremgangsmåten er tilsvarende hvis du skal sette «Dokumentert fravær». Husk å oppdatere
PGS-monitor før du setter status.

PGS og PAS-eksamen er integrerte systemer. Skoleadministrator kan sette fraværsstatuser i
PAS, og disse vil da automatisk overføres til PGS. På samme måte vil alle fraværsstatuser som
settes i PGS automatisk overføres til PAS. Dersom det mot formodning skulle være ulik status i
PAS og PGS, anbefaler vi at du trykker «Oppdater» i PGS-monitor og oppdaterer nettsiden i
PAS.

Kandidater som har fått status «Ikke-dokumentert fravær» eller «Dokumentert fravær» vil ved
pålogging komme direkte til fraværssiden i kandidatmodulen. Du kan lese mer om dette i den
separate brukerveiledningen for kandidatgjennomføring.

For todelt eksamen vil status for fravær vises både på del 1 og del 2.

Dersom det skal endres status for fravær etter eksamensdagen, skal det gjøres i PAS-eksamen.
Det er mulig å skifte mellom dokumentert og ikke-dokumentert fravær etter eksamensdagen i
PAS, hvis kandidaten leverer dokumentasjon senere.

<h2 id="hent-kandidatliste-fra-pas-etterpmelding">
  Hent kandidatliste fra PAS – Etterpåmelding
</h2>{" "}

Hvis skolen etterpåmelder kandidater på selve eksamensdagen vil ikke disse automatisk vises i
PGS-monitor. For å vise dem, må du velge «Hent kandidatliste fra PAS». Denne knappen
er kun synlig når både fag og gruppe er valgt. Da vil PGS-monitor hente alle kandidater
som er etterpåmeldt i kandidatgruppen.

![etterpåmelding](/images/etterpamelding.png)

## Vis ip og online status 2

I kolonnen for online-status ser du om en kandidat har en aktiv tilkobling til PGS, i så fall vises
status "Ja". Dette krever at kandidaten er pålogget PGS. Merk at det går noen sekunder mellom
hver gang PGS sjekker om kandidaten er online. Dersom kandidaten lukker nettleseren hvor hen
er innlogget, eller setter pc-en sin i dvalemodus vil online-status settes til "Nei".

Kolonnen IP-adresse viser hvilken IP som er i bruk ved pålogging og opplastning. Systemet vil
varsle når det benyttes en annen IP-adresse enn for de andre kandidatene i gruppen. For å se
detaljer om IP-adresse kan man trykke på «Vis IP og onlinestatus», og trykke på IP-adressen.

![etterpåmelding](/images/visIpAdress.png)

IT-ansvarlige på skolen burde kunne avdekke om det er snakk om uautorisert bruk. Merk at
også IP-adressen til eksamensvakten logges når en eksamensvakt endrer status for en
kandidat. Dette gjelder også når eksamensvakten laster opp og leverer filer på vegne av
kandidaten.

Det er en teknisk begrensning i PGS på vising av IP-adresser, for IPv6 adresser vises kun de
siste tolv tegnene i adressen (se skjermbildet på neste side). Aktiviteter fra PGS-monitor
vil alltid vise IPv4 adresser, mens aktiviteter fra kandidat kan vises med IPv6. Dette kan oppstå
dersom en bruker benytter VPN, eller dersom nettverksoppsettet bruker IPv6.

![etterpåmelding](/images/vi.png)

<h2 id="forskjeller-p-eksamen-med-n-del-og-todelt-eksamen">
  {" "}
  Forskjeller på eksamen med én del og todelt eksamen{" "}
</h2>

Det er flere forskjeller mellom eksamen med én del og todelt eksamen. I denne
brukerveiledningen er derfor laget separate beskrivelser for disse i egne kapitler.

For todelt eksamen skal del 1 kun gjennomføres på papir. Del 1 er derfor ikke tilgjengelig i PGS
for kandidatene. Del 1 har derfor ikke statuser knyttet til pålogging og autorisasjon for
kandidater. For todelt eksamen har del 2 en rekke likheter med eksamen med én del (del 1) da
begge disse kan gjennomføres digitalt.

Hvis du synes det er komplisert å følge med på statusendringer i PGS-monitor når den
inneholder både eksamen med én del og todelt eksamen, kan du se på hver kandidatgruppe for
seg.

Når du som eksamensvakt skal levere på vegne av en kandidat må du angi eksamensdel for
todelt eksamen. Dette er ikke nødvendig for eksamen med én del.

<h2 id="eksamen-med-n-del"> Eksamen med én del </h2>

For kandidater som gjennomfører eksamen med én del vil du se status kun for del 1.
Kolonnen for del 2 vil da stå tom.

Kapittelet "Gi tilgang til kandidater" beskriver hvordan du kan gi tilgang til eksamen med én
del.

Kapittelet "Sette fravær" beskriver hvordan du setter fravær for kandidater som har
eksamen med én del.

Kandidatene vil normalt sett levere besvarelsene digitalt i PGS. Dersom kandidaten velger å
levere besvarelsen på papir kan skolen skanne papirbesvarelsen og levere digitalt på vegne
av kandidaten. Kandidater som velger å levere på papir trenger ikke å logge inn i PGS. Gitt
at skolen velger å sende besvarelsen via post, skal dette markeres ved å sette status
«Sendes i posten».

Eksamensvakt har muligheten til å laste opp og levere på vegne av kandidaten, enten via
PGS-monitor eller via gruppeopplasting. Det er en separat brukerveiledning for
gruppeopplastning hvor du kan lese mer om dette.

<h3 id="statuser-eksamen-med-n-del"> Statuser – eksamen med én del </h3>

| Status                              | Beskrivelse                                                                                                                                                                                                                                                              |
| ----------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Ikke innlogget                      | Kandidaten har ikke logget på PGS denne eksamensdagen.                                                                                                                                                                                                                   |
| Autorisert                          | Kandidaten har ikke logget på PGS, men eksamensvakten har gitt kandidaten digital tilgang. Kandidaten er ikke autentisert, men hen er autorisert.                                                                                                                        |
| Venter på tilgang                   | Kandidaten er autentisert i UIDP og har en brukersesjon i PGS, men eksamensvakten har ikke gitt digital tilgang til PGS. Kandidaten er altså ikke autorisert.                                                                                                            |
| Venter på eksamensstart (kl. 09:00) | Kandidaten har logget på PGS og eksamensvakten har gitt digital tilgang, men eksamen har ikke startet ennå.                                                                                                                                                              |
| Startet                             | Kandidaten har logget på PGS og hen har fått digital tilgang, og eksamen har startet. Kandidaten skal ha tilgang til å se eksamensoppgaven og levere besvarelse digitalt.                                                                                                |
| Laster opp                          | Kandidaten eller eksamensvakten har lastet opp en eller flere besvarelsesfiler, men ikke levert.                                                                                                                                                                         |
| Levert digitalt                     | Kandidaten har levert besvarelsen digitalt. Statusen settes også dersom eksamensvakten leverer på kandidatens vegne. Digital tilgang er blokkert, og kandidaten får opp kvitteringssiden dersom hen logger på. Kandidaten markeres med grønn bakgrunn i PGS-monitor. |
| Sendes i posten                     | Kandidaten har sendes i posten. Skolen har valgt å sende besvarelsen i posten. Digital tilgang er blokkert, og kandidaten får opp kvitteringssiden dersom hen logger på. Kandidaten markeres med grønn bakgrunn i PGS-monitor.                                       |
| Ikke-dokumentert fravær             | Kandidaten har ikke møtt. Digital tilgang er blokkert, og kandidaten får opp fraværssiden dersom hen logger på.                                                                                                                                                          |
| Dokumentert fravær                  | Kandidaten har ikke møtt. Kandidaten har oversendt skriftlig dokumentasjon på fraværet. Digital tilgang er blokkert, og kandidaten får opp fraværssiden dersom hen logger på.                                                                                            |

### Levere besvarelse på vegne av kandidat – eksamen med én del

1. I PGS-monitor velger du «Last opp» på den kandidaten du ønsker å levere på vegne
   av.
2. Last opp en eller flere filer. Laster du opp feil fil, trykker du «X» til høyre for filnavnet for å
   fjerne filen.
3. Trykk «Last ned» for hver fil, og sjekk at du har lastet opp riktig fil.
4. Trykk «Lever filene».
5. Du vil få opp en dialogboks der du må taste inn kandidatnummer til kandidaten.
6. Når du er ferdig, gå tilbake til PGS-monitor ved å trykke på pilen øverst til
   venstre, ved siden av kandidatens navn.

Merk at når du har levert kan ikke kandidaten selv levere flere filer siden status da vil
være "Levert digitalt". Dersom kandidaten har behov for å levere flere filer eller
kandidaten ønsker å levere en fil på nytt, kan du gi tilgang til kandidaten ved å velge
«Skal levere på nytt» i PGS-monitor.

(bilder kommer)

## Todelt eksamen

For kandidater som tar eksamen med to deler vil du se én status pr del i PGS-monitor.

Kapittelet "Gi tilgang til kandidater" beskriver hvordan du kan gi tilgang for kandidater.
Beskrivelsen gjelder også for todelt eksamen, men merk at tilgangsstyringen kun gjelder for
del 2.

Kapittelet "Sette fravær" beskriver hvordan du setter fravær for kandidater. Beskrivelsen
gjelder også for todelt eksamen. Status for fravær vises både for del 1 og del 2.

For todelt eksamen er del 1 er alltid er uten digitale hjelpemidler. Derfor er det ikke mulig å
gi kandidaten tilgang til å levere digitalt for denne eksamensdelen. Det er vanlig at skolen
skanner kandidatens papirbesvarelse for del 1 og leverer denne digitalt i PGS. Som
eksamensvakt har du mulighet til å levere del 1 på vegne av kandidaten, enten via
PGS-monitor eller via gruppeopplasting.

Starttidspunktet for del 2 varier avhengig av fagkode. Del 2 starter 10:00, 10:45 eller 11:00.
Kandidaten vil ikke få tilgang på eksamensoppgaven for del 2 i PGS før starttidspunktet.
Dette gjelder selv om kandidaten er pålogget og autorisert i PGS.
Kandidatene vil normalt sett levere besvarelsene for del 2 digitalt i PGS. Dersom en
kandidat velger å levere besvarelsen på papir kan skolen skanne papirbesvarelsen og
levere digitalt på vegne av kandidaten. Kandidater som velger å levere del 2 på papir
trenger ikke å logge inn i PGS.

Skolen skal som hovedregel levere begge eksamensdelene digitalt i PGS. Alternativt kan
besvarelsen sendes i posten. Da skal både eksamen del én og del to sendes i posten. Dette
gjør det enklere for sensor å vurdere besvarelsen samlet. Gitt at skolen velger å sende
besvarelsen via post, skal dette markeres ved å sette status «Sendes i posten» både for del 1 og del 2.

### Statuser – todelt eksamen

Kolonnene del 1 og del 2 angir om en status gjelder for de respektive delene.

| Status                              | Del 1 | Del 2 | Beskrivelse                                                                                                                                                                                                                                            |
| ----------------------------------- | ----- | ----- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Gjennomføres på papir               | ✔     |       | Denne statusen vises som standard for del 1. Benyttes ikke for del 2                                                                                                                                                                                   |
| Ikke innlogget                      |       | ✔     | Kandidaten har ikke logget på PGS denne eksamensdagen.                                                                                                                                                                                                 |
| Autorisert                          |       | ✔     | Kandidaten har ikke logget på PGS, men eksamensvakten har gitt kandidaten digital tilgang til del 2. Kandidaten er ikke autentisert, men hen er autorisert.                                                                                            |
| Venter på tilgang                   |       | ✔     | Kandidaten er autentisert i UIDP og har en brukersesjon i PGS, men eksamensvakten har ikke gitt digital tilgang til del 2 i PGS. Kandidaten er altså ikke autorisert.                                                                                  |
| Venter på eksamensstart (kl. tt:mm) |       | ✔     | Kandidaten har logget på PGS og eksamensvakten har gitt digital tilgang til del 2, men eksamen har ikke startet ennå. Klokkeslettet i parentes angir starttidspunktet for del 2.                                                                       |
| Startet                             |       | ✔     | Kandidaten har logget på PGS og hen har fått digital tilgang, og eksamen har startet. Kandidaten skal ha tilgang til å se eksamensoppgaven og levere besvarelse digitalt.                                                                              |
| Laster opp                          | ✔     | ✔     | Kandidaten eller eksamensvakten har lastet opp en eller flere besvarelsesfiler, men ikke levert.                                                                                                                                                       |
| Levert digitalt                     | ✔     | ✔     | Kandidaten har levert besvarelsen digitalt. Statusen settes også dersom eksamensvakten leverer på kandidatens vegne. Dersom statusen er satt for del 2 vil digital tilgang være blokkert, og kandidaten får opp kvitteringssiden dersom hen logger på. |
| Sendes i posten                     | ✔     | ✔     | Kandidaten har sendes i posten. Skolen har valgt å sende besvarelsen i posten. Digital tilgang er blokkert, og kandidaten får opp kvitteringssiden dersom hen logger på.                                                                               |
| Ikke-dokumentert fravær             | ✔     | ✔     | Kandidaten har ikke møtt. Statusen settes for begge delene. Digital tilgang er blokkert, og kandidaten får opp fraværssiden dersom hen logger på.                                                                                                      |
| Dokumentert fravær                  | ✔     | ✔     | Kandidaten har ikke møtt. Kandidaten har oversendt skriftlig dokumentasjon på fraværet. Statusen settes for begge delene. Digital tilgang er blokkert, og kandidaten får opp fraværssiden dersom hen logger på.                                        |

### Levere besvarelse på vegne av kandidat – todelt eksamen

1. Velg «Last opp» på den kandidaten du ønsker å levere på vegne av.
2. Last opp en eller flere filer. Laster du opp feil fil, trykker du «X» til høyre for filnavnet for å
   fjerne filen.
3. Trykk «Last ned» for hver fil, og sjekk at du har lastet opp riktig fil.
4. For todelt eksamen, blir du bedt om å velge eksamensdel for hver fil.
5. Trykk «Lever filene».
6. Du vil få opp en dialogboks der du må taste inn kandidatnummer til valgt kandidat.
7. Når du har levert for en eksamensdel, kan ikke kandidaten levere på nytt for den
   delen, med mindre du endrer kandidatens status til «Skal levere på nytt» i
   PGS-monitor.
8. Når du er ferdig, gå tilbake til kandidatoversikten ved å trykke på pilen øverst til
   venstre, ved siden av kandidatens navn.

Hvis kandidaten har levert både del én og del to på papir, kan eksamensvakten velge å skanne
dem samlet og levere én fil som gjelder både del én og del to i PGS. Da må eksamensvakten for
valg av eksamensdel angi «Eksamen del 1 og del 2».

![levere på vegne av kandidat 1](/images/levere-pa-vegne-av-kandidat-1.png)

![levere på vegne av kandidat 2](/images/levere-pa-vegne-av-kandidat-2.png)

### Markering av kandidater som har levert todelt eksamen

For todelt eksamen vil en kandidat markeres med grønn farge i PGS-monitor når del 2 er
levert:

• Grønn ramme betyr at kandidaten har levert del 2, men del 1 er ikke registrert levert.
• Grønn bakgrunn betyr at begge eksamensdelene er kvittert ut som levert. Dvs. at både del
1 og del 2 har status «Levert digitalt» eller «Sendes i posten».

Dersom kun del 1 er levert vil ikke kandidaten markeres med grønn farge.

## Tilbakemelding

Vi ønsker dine tilbakemeldinger på brukerveiledningen og hvordan du opplever PGS som
system. Du kan sende e-post til [<EMAIL>](<EMAIL>)
