"use client";

import React, { useState } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import useFileHandler from "@/hooks/useFileHandler";

interface DataTableProps<IUploadedFile> {
  columnsFeil: ColumnDef<IUploadedFile>[];
  data: IUploadedFile[];
}

export function DataTableError<IUploadedFile>({
  columnsFeil: columns,
  data,
}: DataTableProps<IUploadedFile>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const { deleteFile } = useFileHandler();
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  async function deleteRows() {
    for (const row of table.getRowModel().rows) {
      if (row.getIsSelected()) {
        await deleteFile((row.original as any).FileGuid);
      }
    }
    table.resetRowSelection();
  }

  const selectedRowsCount = table.getSelectedRowModel().rows.length;

  return (
    <div className="bg-red-50 p-4">
      <div className="text-end">
        <Button
          variant="outline"
          className="border-2 h-12 rounded-sm normal-case font-normal w-full sm:w-auto"
          onClick={deleteRows}
          disabled={selectedRowsCount === 0}
          aria-label={`Fjern markerte ${selectedRowsCount}`}
        >
          Fjern markerte ({selectedRowsCount})
        </Button>
      </div>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="text-left font-bold">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-left">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Ingen filer med feil
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
