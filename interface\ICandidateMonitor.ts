import { IExamDocument } from "./IExamDocument";

export interface ICandidateMonitor {
  userId: string;
  groupCode: string;
  groupName: string;
  subjectCode: string;
  subjectName: string;
  candidateName: string;
  candidateNumber: string;
  candidateRegistrationId?: string; // påmeldingsid
  deliveryStatusPart1: number;
  deliveryStatusPart2: number;
  documents: IExamDocument[];
  testPeriod: string;
  //examStartDate: string;
  partOneStartDateTime?: string;
  partTwoStartDateTime?: string | null;
  redisObject: IRedisObject[];
  isPreauthenticated: boolean;
}
