"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import "dayjs/locale/nb"; // Import Norwegian locale
import { IActivityLogColumnDef } from "@/interface/IActivityLogColumnDef";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import {
  HiOutlineArrowNarrowDown,
  HiOutlineArrowNarrowUp,
} from "react-icons/hi";

dayjs.extend(utc);
dayjs.extend(timezone);

export const activityLogColumns: ColumnDef<IActivityLogColumnDef>[] = [
  {
    accessorKey: "Timestamp",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Tidspunkt
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    cell: ({ row }) => {
      const timestampString = row.original.TimestampString?.trim();
      if (!timestampString) return <div>-</div>;

      try {
        const date = dayjs.utc(timestampString).tz("Europe/Oslo");

        if (date.isValid()) {
          return <div>{date.format("HH:mm:ss")}</div>;
        }
        return <div>-</div>;
      } catch (error) {
        console.warn("Error formatting timestamp:", error);
        return <div>-</div>;
      }
    },
  },
  {
    accessorKey: "Rolle",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Rolle
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
  },
  {
    accessorKey: "operation_Operasjonstype",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Aktivitet
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
  },
  {
    accessorKey: "operation_BeskrivelseMal",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Beskrivelse
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
  },

  {
    accessorKey: "Filnavn",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Filnavn
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex flex-col gap-2">
          {String(row.getValue("Filnavn"))
            .split("<br/>")
            .map((line: string, index: number) => (
              <div key={index} className="whitespace-pre-wrap">
                {line}
              </div>
            ))}
        </div>
      );
    },
  },
  {
    accessorKey: "Eksamensdel",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Eksamensdel
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
  },
];