"use client";

import React from "react";
export default function Home() {
  return (
    <>
      <div className="prose">
        <section>
          <h2 id="innledning">Innledning</h2>
          <p>
            Denne brukerveiledningen er laget for eksamensvakter som skal
            benytte funksjonaliteten "Lever for kandidat" i PGS. Merk at
            brukerstøtte går
            <a
              href="https://www.udir.no/om-udir/tjenestevei-skole-barnehage/"
              className="ml-1 mr-1"
            >
              {" "}
              tjenestevei
            </a>
            . For eksempel skal eksamensvakter kontakte sin skoleleder eller
            eksamensansvarlige på skolen.
          </p>
          <p>
            Funksjonaliteten "Lever for kandidat" i PGS-admin er tilgjengelig
            for de som er eksamensvakter for PGS, dvs. de som har rolletilgangen
            eksamensvakt for PGS. Funksjonaliteten er også tilgjengelig for alle
            som er skoleadministrator i PAS-eksamen samt de som representerer
            skoleeier og statsforvalteren i PAS. Tilganger til PGS tildeles av
            skoleadministrator i PAS. Oversikt over dine tildelte rolletilganger
            finner du ved å gå til
            <a className="ml-1" href="https://uidp-admin.udir.no/min-konto">
              minkonto.udir.no
            </a>{" "}
            .
          </p>

          <p>
            Det er flere brukerveiledninger for PGS. Disse finner du på siden
            for{" "}
            <a href="/brukerveiledning">
              brukerveiledning
            </a>{" "}
            i PGS-admin.
          </p>
          <p>
            Merk at alle persondata som vises i skjermbilder i våre veiledninger
            er fiktive data.
          </p>
          <p>
            På udir.no finner du praktisk informasjon om organisering og
            gjennomføring av sentralt gitt skriftlig eksamen:{" "}
            <a href="https://www.udir.no/eksamen-og-prover/eksamen/">
              https://www.udir.no/eksamen-og-prover/eksamen/
            </a>
          </p>
        </section>
        <section>
          <h2 id="hurtigguide">Hurtigguide</h2>

          <ul className="list-decimal">
            <li>
              Logg inn i
              <a className="ml-1" href="https://pgs-admin.udir.no/">
                pgs-admin.udir.no
              </a>
              , velg{" "}
              <a
                className="ml-1 mr-1"
                href="/pgs-monitor"
              >
                PGS-monitor
              </a>
              i menyen, og benytt så valget "Lever for kandidat".
            </li>
            <li>Last opp filer for kandidaten.</li>
            <li>
              Eksamensdel: Ved todelt eksamen, er det viktig å angi hvilken
              eksamensdel hver fil tilhører.
            </li>
            <li>
              Marker at filen er kontrollert ved å klikke på knappen "Filen er
              sjekket"
            </li>
            <li>
              Når du er klar for å levere, kan du trykke «Lever alle filene».
            </li>
          </ul>
        </section>
        <section>
          <h2 id="naviger-til-lever-for-kandidat">
            Naviger til Lever For Kandidat{" "}
          </h2>

          <p>
            Logg inn i
            <a className="ml-1 mr-1" href="https://pgs-admin.udir.no">
              pgs-admin
            </a>
            og velg PGS-monitor i menyen. Klikk på nedtrekkslisten "Flere
            valg" og velg "Lever for kandidat". Du kommer da til siden "Lever
            for kandidat".
          </p>
          <p>
            Merk at valget "Lever for kandidat" ikke er tilgjengelig dersom
            kandidaten er registrert med status "Sendes i posten". Du vil heller
            ikke få opp valget "Lever for kandidat" dersom kandidaten er
            registrert med fravær.
          </p>
        </section>
        <section>
          <h2 id="last-opp-filer">Last opp filer</h2>
          <p>
            Last opp filer ved å velge filer i filutforskeren eller dra filer
            over det markerte feltet.
          </p>
          <p>
            I arkfanen for opplastede filer angis det hvor mange filer som er
            korrekt opplastet.
          </p>
          <div>
            <img
              src="/images/lever-for-opplastede.png"
              aria-label="opplastede filer"
            />
          </div>
          <p className="italic">
            Skjermdumpen over viser antall filer, total størrelse på filene,
            tidspunkt for opplastning og hvem filen ble lastet opp av.
          </p>
          <p>
            Dersom du trenger å slette en fil kan du gjøre dette ved å klikke på
            valget "Slett". Det er mulig å slette flere filer samtidig, Dette
            gjøres ved å markere filene og klikke på "Slett markerte".
          </p>
          <p>
            Du kan laste opp filer uavhengig av kandidatens fremdriftsstatus.
            Men det vil ikke være mulig å laste opp filer dersom kandidaten er
            registrert med fravær.
          </p>
          <p>
            Merk at du maksimalt kan laste opp 100 filer om gangen. Samlet
            størrelse på filene du laster opp kan ikke overstige 300 MB om
            gangen. Én enkelt fil kan maksimalt være 40 MB stor.
          </p>
          <p>
            Godkjente filtyper er: 7z, avi, bmp, c, css, doc, docm, docx, dot,
            dotm, dotx, emf, flv, ggb, gif, gz, h, hlp, htm, html, jpe, jpeg,
            jpg, js, m4a, mdb, mdi, mht, mm, mov, movie, mp3, mp4, mpeg, mpp,
            odb, odp, ods, odt, ott, pdf, png, ppsm, ppsx, ppt, pptm, pptx, qt,
            rap, rar, rm, rtf, sib, stw, svg, swf, sxc, sxw, tar, tex, tif,
            tiff, tii, txt, vsd, wav, wmf, wmv, wri, xls, xlsb, xlsm, xlsx, xml,
            xps, z, zip og zipx.
          </p>
        </section>
        <section>
          <h2 id="feilmeldinger">Feilmeldinger</h2>
          <p>
            Det er ikke mulig å laste opp filer med feil, de må fjernes. Du kan
            deretter korrigere feilen og laste opp på nytt. Vanlige feil er at
            filen er for stor eller at filtypen ikke er gyldig.
          </p>
          <p>Man får feilmeldinger for følgende feil:</p>
          <ul>
            <li>
              {" "}
              En fil med dette navnet er allerede lastet opp (filnavnet er ikke
              unikt)
            </li>
            <li> Filen har en ugyldig filtype</li>
            <li> Filen er for stor (over 40 MB).</li>
            <li>
              {" "}
              Kandidaten har blitt slettet (eksempelvis pga. feilpåmelding)
            </li>
            <li> Totalt antall filer er over 100.</li>
            <li> Samlet størrelse for filene er over 300 MB.</li>
          </ul>
          <div>
            <img
              src="/images/lever-for-feiltabell.png"
              aria-label="feilmeldinger"
            />
          </div>
          <p className="italic">
            Bildet over viser ulike feilmeldinger som kan oppstå ved opplasting.
            Dersom du laster opp en ugyldig fil vil du automatisk bli sendt til
            arkfanen «Filer med feil».
          </p>
        </section>
        <section>
          <h2 id="eksamen-med-en-del">Eksamen med én del</h2>

          <p>
            Når en fil er korrekt lastet opp for eksamen med én del, så vil PGS
            automatisk sette eksamensdelen til "Eksamen". Det er ikke mulig å
            endre eksamensdel for eksamen med én del.
          </p>

          <p>
            Opplastede filer for eksamen med én del vil være synlig i
            PGS-monitor og kvitteringssiden til kandidaten. Merk at
            kandidaten <span className="underline mx-1">ikke</span> vil se filer
            som er lastet opp av eksamensvakten på sin leveringsside. Dette
            gjelder både filer lastet opp via "Lever for kandidat" og via
            gruppeopplasteren.{" "}
          </p>
        </section>

        <section>
          <h2 id="todelt-eksamen">Todelt eksamen</h2>

          <p>
            Ved todelt eksamen må du angi hvilken eksamensdel hver fil tilhører.
            Det er viktig å angi korrekt del slik at visningen for sensor blir
            riktig.
          </p>
          <ul>
            <li> Eksamen del 1</li>
            <li> Eksamen del 2</li>
            <li> Eksamen del 1 og 2 </li>
          </ul>

          <p>
            Det siste alternativet skal kun benyttes dersom både del 1 og del 2
            er innskannet samlet slik det kun er én fil for begge delene.
          </p>
          <p>
            Eksamensbesvarelsene som ikke har fått angitt eksamensdel vil vises
            med rød tekst i knappen for eksamensdel.
          </p>

          <p>
            Når eksamensdel er angitt vil filene også være synlige i
            PGS-monitor. Leverte filer for «Eksamen del 1» og «Eksamen del
            1 og 2», vil vises på kandidatens kvitteringsside sammen med filer
            med "Eksamen del 2".
          </p>
          <p>
            Merk at kandidaten <span className="underline mx-1">ikke</span> vil
            se filer som er lastet opp av eksamensvakten på sin leveringsside.
            Dette gjelder både filer lastet opp via "Lever for kandidat" og via
            gruppeopplasteren.
          </p>
        </section>

        <section>
          <h2 id="lever-filene">Lever filene</h2>
          <p>
            Når alle filene er lastet opp og eksamensdel er angitt skal du
            trykke «Lever alle filene». Du vil da komme til fanen for "Leverte
            filer" som bekrefter at filene er levert.
          </p>
          <p>
            I fanen "Leverte filer" vil du se samtlige leverte filer for
            kandidaten. Dvs. filer levert av kandidaten selv, av andre
            eksamensvakter og filer levert via gruppeopplasteren.
          </p>
          <p>
            I fanen "Leverte filer" er det mulig å slette filer som er feilaktig
            levert. Merk da at filene vil slettes for godt og at det ikke er
            noen mulighet til å angre.
          </p>
        </section>
      </div>
    </>
  );
}
