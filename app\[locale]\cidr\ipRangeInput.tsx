"use client";

import { useEffect, useState, useCallback } from "react";
import { AlertCircle, Plus, Trash2, Save, Link2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import IPCIDR from "ip-cidr";

interface IPRangeInputProps {
  schoolId: string;
}

interface IPRange {
  cidr: string;
  isValid: boolean;
  error: string;
}

interface AddressRangeResult {
  start: string;
  end: string;
  total: number;
  error?: string;
}

interface SaveResponse {
  success: boolean;
  message: string;
}

const shortenIPv6 = (address: string): string => {
  // Fjern eventuell CIDR-notasjon
  const ipOnly = address.split("/")[0];

  // Del opp i grupper på 4 hex-sifre
  const groups = ipOnly.split(":");

  // Finn lengste sekvens av nuller
  let longestZeroStart = -1;
  let longestZeroLength = 0;
  let currentZeroStart = -1;
  let currentZeroLength = 0;

  for (let i = 0; i < groups.length; i++) {
    if (groups[i] === "0000" || groups[i] === "0") {
      if (currentZeroStart === -1) {
        currentZeroStart = i;
      }
      currentZeroLength++;
    } else {
      if (currentZeroLength > longestZeroLength) {
        longestZeroStart = currentZeroStart;
        longestZeroLength = currentZeroLength;
      }
      currentZeroStart = -1;
      currentZeroLength = 0;
    }
  }

  // Sjekk siste sekvens
  if (currentZeroLength > longestZeroLength) {
    longestZeroStart = currentZeroStart;
    longestZeroLength = currentZeroLength;
  }

  // Bygg forkortet adresse
  let shortened = groups.map((g) => {
    // Fjern ledende nuller
    return g.replace(/^0+/, "") || "0";
  });

  // Erstatt lengste nullsekvens med ::
  if (longestZeroLength > 1) {
    shortened.splice(longestZeroStart, longestZeroLength, "");
    if (longestZeroStart === 0) {
      shortened.unshift("");
    }
    if (longestZeroStart + longestZeroLength === groups.length) {
      shortened.push("");
    }
  }

  return shortened.join(":");
};

// Oppdatert calculateIPRange funksjon
const calculateIPRange = (cidr: string): AddressRangeResult => {
  try {
    const cidrInstance = new IPCIDR(cidr);

    // Forkort IPv6-adressene hvis det er IPv6
    const isIPv6 = !cidrInstance.address.v4;
    return {
      start: isIPv6
        ? shortenIPv6(cidrInstance.addressStart.address)
        : cidrInstance.addressStart.address,
      end: isIPv6
        ? shortenIPv6(cidrInstance.addressEnd.address)
        : cidrInstance.addressEnd.address,
      total: Number(cidrInstance.size),
    };
  } catch (error) {
    return { start: "", end: "", total: 0, error: "Invalid CIDR notation" };
  }
};

interface IPRangeItemProps {
  range: IPRange;
  index: number;
  handleCIDRChange: (index: number, value: string) => void;
  removeRange: (index: number) => void;
  isLast: boolean;
}

const IPRangeItem: React.FC<IPRangeItemProps> = ({
  range,
  index,
  handleCIDRChange,
  removeRange,
  isLast,
}) => {
  const result =
    range.isValid && range.cidr ? calculateIPRange(range.cidr) : null;

  return (
    <div className="p-3 rounded-lg border border-slate-200 bg-white shadow-sm hover:shadow-md transition-all duration-200">
      <div className="flex gap-3 items-start">
        <div className="bg-slate-100 rounded-lg p-2">
          <Link2 className="h-5 w-5 text-slate-500" />
        </div>
        <div className="flex-grow space-y-4">
          <div className="flex gap-2 items-start">
            <Input
              type="text"
              placeholder="CIDR (f.eks. ***********/24 eller 2001:0db8::/32)"
              value={range.cidr}
              onChange={(e) => handleCIDRChange(index, e.target.value)}
              className={`${
                !range.isValid && range.cidr
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "focus-visible:ring-slate-400"
              } bg-slate-50 text-base`}
            />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => removeRange(index)}
              disabled={isLast}
              className="shrink-0 hover:bg-slate-100"
            >
              <Trash2 className="h-4 w-4 text-slate-500" />
            </Button>
          </div>

          {range.error && (
            <Alert variant="destructive" className="py-2">
              <AlertDescription className="flex items-center gap-2 text-sm">
                <AlertCircle className="h-4 w-4" />
                <span>{range.error}</span>
              </AlertDescription>
            </Alert>
          )}

          {result && (
            <div className="p-4 bg-slate-50 rounded-lg space-y-2">
              {result.error ? (
                <div className="text-red-500">{result.error}</div>
              ) : (
                <div className="grid gap-1 text-sm">
                  <InfoRow label="Start" value={result.start} />
                  <InfoRow label="Slutt" value={result.end} />
                  <InfoRow
                    label="Totalt"
                    value={result.total.toLocaleString()}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface InfoRowProps {
  label: string;
  value: string;
}

const InfoRow: React.FC<InfoRowProps> = ({ label, value }) => (
  <div className="grid grid-cols-[60px,1fr] gap-2">
    <span className="font-medium text-slate-600">{label}:</span>
    <span className="font-mono text-slate-800">{value}</span>
  </div>
);

export function IPRangeInput({ schoolId }: IPRangeInputProps) {
  const [ranges, setRanges] = useState<IPRange[]>([
    { cidr: "", isValid: true, error: "" },
  ]);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [saveStatus, setSaveStatus] = useState<{
    success?: boolean;
    message?: string;
  }>({});

  const fetchRanges = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/ipRanges?schoolId=${schoolId}`);
      const data = await response.json();

      if (data.success && Array.isArray(data.ranges)) {
        const formattedRanges = data.ranges.map((cidr: string) => ({
          cidr,
          isValid: true,
          error: "",
        }));

        setRanges(
          formattedRanges.length > 0
            ? formattedRanges
            : [{ cidr: "", isValid: true, error: "" }]
        );
      } else {
        setSaveStatus({
          success: false,
          message: "Kunne ikke hente IP-områdene",
        });
      }
    } catch (error) {
      console.error("Error fetching IP ranges:", error);
      setSaveStatus({
        success: false,
        message: "Kunne ikke hente IP-områdene",
      });
    } finally {
      setIsLoading(false);
    }
  }, [schoolId]);

  useEffect(() => {
    fetchRanges();
  }, [fetchRanges]);

  const validateCIDR = (value: string): { isValid: boolean; error: string } => {
    try {
      if (value) {
        new IPCIDR(value).toRange();
      }
      return { isValid: true, error: "" };
    } catch (err) {
      return { isValid: false, error: "Ugyldig CIDR-notasjon" };
    }
  };

  const handleCIDRChange = (index: number, value: string): void => {
    const { isValid, error } = validateCIDR(value);
    const newRanges = ranges.map((range, i) =>
      i === index ? { ...range, cidr: value, isValid, error } : range
    );
    setRanges(newRanges);
    setSaveStatus({});
  };

  const handleSave = useCallback(async () => {
    const invalidRanges = ranges.filter(
      (range) => !range.isValid || !range.cidr
    );
    if (invalidRanges.length > 0) {
      setSaveStatus({
        success: false,
        message: "Alle IP-områder må være gyldige før lagring",
      });
      return;
    }

    setIsSaving(true);
    setSaveStatus({});

    try {
      const response = await fetch("/api/ipRanges", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          schoolId: schoolId,
          ranges: ranges.map((range) => range.cidr),
        }),
      });

      const data: SaveResponse = await response.json();

      setSaveStatus({
        success: data.success,
        message: data.success
          ? "IP-områdene er lagret"
          : "Kunne ikke lagre IP-områdene",
      });
    } catch (error) {
      setSaveStatus({
        success: false,
        message: "Kunne ikke lagre IP-områdene",
      });
    } finally {
      setIsSaving(false);
    }
  }, [ranges, schoolId]);

  const addRange = () => {
    setRanges([...ranges, { cidr: "", isValid: true, error: "" }]);
  };

  const removeRange = (index: number) => {
    setRanges((prevRanges) => prevRanges.filter((_, i) => i !== index));
  };

  if (isLoading) {
    return (
      <div className="w-full text-center p-4">Henter lagrede IP-områder...</div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {saveStatus.message && (
        <Alert
          variant={saveStatus.success ? "default" : "destructive"}
          className="mb-4"
        >
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {saveStatus.message}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {ranges.map((range, index) => (
          <IPRangeItem
            key={index}
            range={range}
            index={index}
            handleCIDRChange={handleCIDRChange}
            removeRange={removeRange}
            isLast={ranges.length === 1}
          />
        ))}
      </div>

      <div className="flex justify-between items-center space-x-4">
        <Button onClick={addRange} variant="ghost" className="w-52 h-10">
          <Plus className="h-4 w-4 mr-2" />
          Legg til nytt område
        </Button>

        <Button
          onClick={handleSave}
          disabled={isSaving || ranges.some((r) => !r.isValid || !r.cidr)}
          className="w-40 h-11 font-medium"
        >
          {isSaving ? (
            "Lagrer..."
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Lagre
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
