"use client";

import { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import { AlertCircle, Search } from "lucide-react";
import {
  MdFilterAltOff,
  MdOutlineKeyboardArrowDown,
  MdOutlineKeyboardArrowUp,
} from "react-icons/md";
import dayjs from "dayjs";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { DataTable } from "../../../components/tables/nedlasting/candidategroup-list/data-table";
import columns from "../../../components/tables/nedlasting/candidategroup-list/columns";
import { Input } from "@/components/ui/input";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRole } from "@/context/RoleContext";
import SchoolSelector from "@/components/schoolSelector";
import React from "react";
import { getAvailableTerms } from "@/lib/getAvailableTerms";
import SchoolFormSkeleton from "./schoolFormSkeleton";
import { Skeleton } from "@/components/ui/skeleton";
import { TableSkeleton } from "@/components/tableSkeleton";
import { useExamPaper } from "@/hooks/useExamPaper";

const INITIAL_FILTER_STATE = {
  date: undefined,
  subjectCode: "",
  candidateNumber: "",
  candidateName: "",
  subjectName: "",
};

export default function SchoolForm() {
  const [filters, setFilters] = useState(INITIAL_FILTER_STATE);
  const [hide, setHide] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const { selectedRole, selectedPeriod, setSelectedPeriod } = useRole();
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const availableTerms = useMemo(() => getAvailableTerms(3), []);

  // Use the new hook
  const {
    data: examPaperData,
    isLoading,
    error,
    summary: hookSummary,
    refetch,
  } = useExamPaper({
    schoolId: selectedRole?.selectedSchoolId,
    period: selectedPeriod,
  });

  const examPaper = (examPaperData as IExamPaperInternal[]) || [];
  const numSelectedFilters = Object.values(filters).filter(Boolean).length;

  // Sett standard term når komponenten lastes
  useEffect(() => {
    if (isInitialLoad && !selectedPeriod && availableTerms.length > 0) {
      setSelectedPeriod(availableTerms[0].value);
      setIsInitialLoad(false);
    }
  }, [isInitialLoad, selectedPeriod, availableTerms, setSelectedPeriod]);

  useEffect(() => {
    if (examPaperData) {
      setShowTable(true);
    }
  }, [examPaperData]);

  const searchAndShowTable = useCallback(() => {
    setShowTable(false);
    refetch();
  }, [refetch]);

  useEffect(() => {
    if (selectedRole?.selectedSchoolId && selectedPeriod && !isInitialLoad) {
      searchAndShowTable();
    }
  }, [selectedRole, selectedPeriod, searchAndShowTable, isInitialLoad]);

  const resetFilters = () => setFilters(INITIAL_FILTER_STATE);

  const handleFilterChange = (
    key: string,
    value: string | Date | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };
  const today = dayjs();
  const pastExamPapers = examPaper.filter((exams) => {
    return dayjs(exams.examStartDate).isBefore(today, "day");
  });

  const filteredData = pastExamPapers.filter((item) => {
    const { date, candidateNumber, candidateName, subjectName, subjectCode } =
      filters;
    return (
      (!candidateNumber ||
        item.candidates.some((ep) =>
          ep.candidateNumber
            .toLowerCase()
            .includes(candidateNumber.toLowerCase().trim())
        )) &&
      (!candidateName ||
        item.candidates.some((ep) =>
          ep.candidateName
            .toLowerCase()
            .includes(candidateName.toLowerCase().trim())
        )) &&
      (!subjectName ||
        item.subjectName
          .toLowerCase()
          .includes(subjectName.toLowerCase().trim())) &&
      (!subjectCode ||
        item.subjectCode
          .toLowerCase()
          .includes(subjectCode.toLowerCase().trim())) &&
      (!date || dayjs(item.examStartDate).isSame(dayjs(date), "day"))
    );
  });

  const summary = useMemo(() => {
    let totalCandidates = 0;
    let deliveredCandidates = 0;
    let totalFiles = 0;

    const deliveredCandidateIds = new Set<string>();

    filteredData.forEach((group) => {
      totalCandidates += group.candidates.length;
      group.candidates.forEach((candidate) => {
        if (candidate.documents.length > 0) {
          deliveredCandidateIds.add(candidate.candidateNumber);
        }

        totalFiles += candidate.documents.length;
      });
    });

    deliveredCandidates = deliveredCandidateIds.size;

    return { totalCandidates, deliveredCandidates, totalFiles };
  }, [filteredData]);

  if (!selectedRole) {
    return <SchoolFormSkeleton />;
  }

  return (
    <div>
      <div className="w-full bg-[#F0E8DC]">
        <div className="container-wrapper flex flex-col lg:flex-row lg:gap-6 text-sm py-4">
          {isLoading ? (
            <>
              <span className="flex items-center gap-2">
                Antall kandidatgrupper:{" "}
                <Skeleton className="h-4 w-8 bg-gray-300" />
              </span>
              <span className="flex items-center gap-2">
                Totalt antall kandidater:{" "}
                <Skeleton className="h-4 w-8 bg-gray-300" />
              </span>
              <span className="flex items-center gap-2">
                Kandidater med leverte filer:{" "}
                <Skeleton className="h-4 w-8 bg-gray-300" />
              </span>
              <span className="flex items-center gap-2">
                Leverte filer: <Skeleton className="h-4 w-8 bg-gray-300" />
              </span>
            </>
          ) : (
            <>
              <span>
                Antall kandidatgrupper:{" "}
                <span className="font-medium">{filteredData.length}</span>
              </span>
              <span>
                Totalt antall kandidater:{" "}
                <span className="font-medium">{summary.totalCandidates}</span>
              </span>
              <span>
                Kandidater med leverte filer:{" "}
                <span className="font-medium">
                  {summary.deliveredCandidates}
                </span>
              </span>
              <span>
                Leverte filer:{" "}
                <span className="font-medium">{summary.totalFiles}</span>
              </span>
            </>
          )}
        </div>
      </div>
      <div className="container-wrapper mb-8">
        <section className="flex flex-col mt-4 mb-6">
          <div className="gap-2 sm:gap-6 flex flex-col sm:flex-row flex-wrap">
            <SchoolSelector />
            <div className="flex flex-wrap gap-4 items-center">
              <Select
                value={selectedPeriod}
                onValueChange={(newPeriod) => {
                  setSelectedPeriod(newPeriod);
                }}
              >
                <SelectTrigger
                  className="w-full sm:w-[180px] border-black border-[2px] h-12"
                  aria-label="Periodevalg"
                  id="Periodevalg"
                  aria-controls="Periodevalg"
                >
                  <SelectValue placeholder="Periode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {availableTerms.map((term) => (
                      <SelectItem key={term.value} value={term.value}>
                        {term.label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <Button
                onClick={searchAndShowTable}
                className="flex items-center justify-start gap-2 w-28 h-12"
                disabled={isLoading}
                variant="default"
              >
                <Search className="h-4 w-4" role="img" aria-label="søk ikon" />
                {isLoading ? "Søker..." : "Søk"}
              </Button>
            </div>
            <div className="flex items-center justify-between w-full sm:w-auto">
              <Button
                variant="ghost"
                className="flex items-center gap-1"
                onClick={() => setHide(!hide)}
                aria-expanded={!hide}
                aria-controls="filter-options"
              >
                {hide ? (
                  <MdOutlineKeyboardArrowDown
                    className="text-xl"
                    role="img"
                    aria-label="pil ned ikon"
                  />
                ) : (
                  <MdOutlineKeyboardArrowUp
                    className="text-xl"
                    role="img"
                    aria-label="pil opp ikon"
                  />
                )}
                {hide ? "Åpne filter" : "Lukk filter"}
                {numSelectedFilters > 0 && (
                  <span className="text-primary">({numSelectedFilters})</span>
                )}
              </Button>
            </div>
          </div>
          <div
            id="filter-options"
            className={`
      ${hide ? "opacity-0 h-0 overflow-hidden" : "opacity-100 h-auto"}
      ${hide ? "" : "mt-4"}
      flex flex-wrap gap-4 transition-all duration-300 ease-in-out
    `}
          >
            {!hide && (
              <>
                <div className="w-full sm:w-[200px]">
                  <DatePicker
                    date={filters.date}
                    onSelect={(date) => handleFilterChange("date", date)}
                    placeholder="Dato"
                    showClear={true}
                    onClear={() => setFilters({ ...filters, date: undefined })}
                  />
                </div>
                <Input
                  type="text"
                  placeholder="Kandidatnummer"
                  className="border-black border-2 w-full sm:w-[200px]"
                  value={filters.candidateNumber}
                  onChange={(e) =>
                    handleFilterChange("candidateNumber", e.target.value)
                  }
                  aria-label="Filtrer på kandidatnummer"
                  autoComplete="on"
                />
                <Input
                  type="text"
                  placeholder="Kandidatnavn"
                  className="border-black border-2 w-full sm:w-[200px]"
                  value={filters.candidateName}
                  onChange={(e) =>
                    handleFilterChange("candidateName", e.target.value)
                  }
                  aria-label="Filtrer på kandidatnavn"
                  autoComplete="on"
                />
                <Input
                  type="text"
                  placeholder="Fagnavn"
                  className="border-black border-2 w-full sm:w-[200px]"
                  value={filters.subjectName}
                  onChange={(e) =>
                    handleFilterChange("subjectName", e.target.value)
                  }
                  aria-label="Filtrer på fagnavn"
                  autoComplete="on"
                />
                <Input
                  type="text"
                  placeholder="Fagkode"
                  className="border-black border-2 w-full sm:w-[200px]"
                  value={filters.subjectCode}
                  onChange={(e) =>
                    handleFilterChange("subjectCode", e.target.value)
                  }
                  aria-label="Filtrer på fagkode"
                  autoComplete="on"
                />
                {numSelectedFilters > 0 && (
                  <Button
                    variant="ghost"
                    onClick={resetFilters}
                    className="flex items-center gap-2 w-full sm:w-auto"
                    aria-label="Nullstill alle filtre"
                  >
                    <MdFilterAltOff className="text-xl" />
                    <span>Nullstill alle filtre</span>
                  </Button>
                )}
              </>
            )}
          </div>
        </section>
        {isLoading && <TableSkeleton />}
        {error && (
          <div
            className="mb-4 bg-red-600 text-white rounded-md"
            role="alert"
            aria-live="assertive"
          >
            <div className="flex items-center gap-2 p-4">
              <AlertCircle className="h-6 w-4" />
              <span>{error}</span>
            </div>
          </div>
        )}
        {showTable && (
          <section className="w-full my-0" aria-label="Resultat av søk">
            <h2 className="sr-only">Tabell med eksamenspapirer</h2>
            <div className="flex flex-col gap-3">
              <DataTable columns={columns} data={filteredData} />
            </div>
          </section>
        )}
      </div>
    </div>
  );
}
