import { getAllowedMimeTypes } from "@/lib/getAllowedMimeTypes";
import BrukerVeiledningButton from "./brukerveiledningButton";
import SubmitFiles from "./submitFiles";
import { Suspense } from "react";
import GroupUploadSkeleton from "./groupUploadSkeleton";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Gruppeopplaster - PGS-admin",
 
};
export default async function Home() {
  return (
    <>
      <div className="flex flex-col gap-6">
        <Header />
        <div className="container-wrapper mb-10">
          <Suspense fallback={<GroupUploadSkeleton />}>
            <AsyncHomeContent />
          </Suspense>
        </div>
      </div>
    </>
  );
}

function Header() {
  return (
    <div className="bg-header">
      <div className="container-wrapper py-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
          <div>
            <h1 className="text-3xl md:text-4xl">Gruppeopplasting</h1>
            <p className="mt-4">Her kan du levere flere filer på en gang.</p>
            <div className="flex flex-col gap-2">
              <span>
                Filnavnet må ha formatet:{" "}
                <code className="bg-gray-200 px-2 py-1 rounded text-xs md:text-sm mr-2">
                  Kandidatnummer-løpenummer
                </code>
                Eksempel:{" "}
                <span className="font-bold text-sm md:text-base">
                  123ABC-V-01.pdf
                </span>
              </span>
            </div>
          </div>
          <div className="mt-4 md:mt-0">
            <BrukerVeiledningButton />
          </div>
        </div>
      </div>
    </div>
  );
}

async function AsyncHomeContent() {
  let mimeTypes: IAllowedMimeTypes[] = [];
  let fetchError: string | null = null;

  try {
    mimeTypes = await getAllowedMimeTypes();
  } catch (err) {
    fetchError =
      "Det oppstod en feil ved lasting av tillatte filtyper. Vennligst prøv igjen senere.";
  }

  return (
    <div className="w-full">
      {fetchError ? (
        <div className="text-red-500 bg-red-100 p-4 rounded">{fetchError}</div>
      ) : (
        <SubmitFiles mimeTypes={mimeTypes} />
      )}
    </div>
  );
}
