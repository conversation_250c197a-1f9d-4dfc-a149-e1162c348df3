

import React from "react";

export default function Home() {
  return (
    <>
      <div className="prose">
        <section>
          <h2 id="innledning">Innledning</h2>
          <p>
            Denne brukerveiledningen er laget for eksamensvakter som skal
            benytte gruppeopplasteren i PGS. Merk at brukerstøtte går
            <a
              className="ml-1"
              href="https://www.udir.no/om-udir/tjenestevei-skole-barnehage/"
            >
              tjenestevei
            </a>
          </p>
          <p>
            Gruppeopplasteren i PGS er tilgjengelig for de som er eksamensvakter
            for PGS, dvs. de som har rolletilgangen
            <i className="ml-1 mr-1">eksamensvakt</i> for PGS. Tilganger til PGS
            tildeles av skoleadministrator i PAS-eksamen. Oversikt over dine
            tildelte rolletilganger finner du ved å gå til
            <a className="ml-1 " href="https://uidp-admin.udir.no/min-konto">
              minkonto.udir.no
            </a>
            .
          </p>
          <p>
            En forutsetning for at eksamensvakten skal kunne benytte seg av
            gruppeopplasteren er at skoleadministrator har gitt eksamensvakten
            tilgang til aktuelle kandidatgrupper i PAS.
          </p>
          <p>
            Gruppeopplasteren er også tilgjengelig for alle som er
            skoleadministrator i PAS samt de som representerer skoleeier og
            statsforvalteren i PAS. Dvs. brukere som har en av følgende roller i
            PAS-eksamen: Skoleadministrator, Skoleadministrator+, Kommune,
            Fylkeskommune, Vigo eksamensadministrator og Statsforvalter.
          </p>
          <p>
            Det er flere brukerveiledninger for PGS. Disse finner du på siden
            for
            <a
              className="ml-1 mr-1"
              href="/brukerveiledning"
            >
              brukerveiledning
            </a>{" "}
            i PGS-admin. Alle persondata som vises i skjermbilder i våre
            veiledninger er fiktive data.
          </p>

          <p>
            På udir.no finner du praktisk informasjon om organisering og
            gjennomføring av sentralt gitt skriftlig eksamen:
            <a
              className="ml-1"
              href="https://www.udir.no/eksamen-og-prover/eksamen/"
            >
              https://www.udir.no/eksamen-og-prover/eksamen/
            </a>
          </p>
        </section>
        <section>
          <h2 id="hurtigguide">Hurtigguide</h2>

          <ul className="list-decimal">
            <li>
              Innlogging: Logg inn i{" "}
              <a className="ml-1" href="https://pgs-admin.udir.no">
                pgs-admin.udir.no
              </a>
              , velg{" "}
              <a
                className="ml-1 mr-1"
                href="/gruppeopplaster"
              >
                Gruppeopplasting
              </a>{" "}
              i menyen.
            </li>
            <li>
              <div>
                <span>
                  Filnavn: Det er viktig med riktig format på filnavnet, fordi
                  filnavnet kobles mot kandidaten. Format er{" "}
                  <i className="ml-1 mr-1">Kandidatnummer-løpenummer</i> –
                  eksempelvis <b className="ml-1">123ABC-V-01.pdf</b>
                </span>
                <br />
                <span>
                  Kandidatnummeret er unikt og kobler filen til riktig kandidat.
                  Løpenummer brukes for at filnavnet skal være unikt og må
                  inneholde 2 siffer. Dersom kandidaten har flere filer, blir
                  neste fil i rekken 123ABC-V-<b>02</b>.pdf
                </span>
                <br />
                <span>
                  Hvis din datamaskin ikke viser filtypen i filnavnet, vil det
                  se slik ut 123ABC-V-01. Dette er også tillatt. Merk at PGS
                  ikke gjør forskjell på store eller små bokstaver i filnavnet.
                </span>
              </div>
            </li>
            <li>
              Eksamensdel: Ved todelt eksamen, er det viktig å angi hvilken
              eksamensdel hver fil tilhører.
            </li>
            <li>
              Lever filene: Når du er klar for å levere, kan du trykke «Lever
              alle filene».
            </li>
          </ul>
        </section>
        <section className="font-normal">
          <h2 id="naviger-til-gruppeopplasteren">
            Naviger til gruppeopplasteren{" "}
          </h2>
          <p>
            Logg inn i
            <a className="ml-1" href="https://pgs-admin.udir.no">
              pgs-admin.udir.no
            </a>
            , velg
            <a
              className="ml-1 mr-1"
              href="/gruppeopplaster"
            >
              Gruppeopplasting
            </a>
            i menyen.
          </p>
        </section>
        <section>
          <h2 id="Last-opp-filer">Last opp filer</h2>
          <p>
            Last opp filer ved å velge filer i filutforskeren eller dra filer
            over det markerte feltet.
          </p>
          <p>
            I arkfanen for opplastede filer angis det hvor mange filer som er
            korrekt opplastet. Tabellen under viser totaloversikt over antall
            filer, kandidater, fagkoder, total størrelse på filene og
            eksamensdel per fil.
          </p>
          <p>
            Dersom du trenger å slette en fil kan du gjøre dette ved å klikke på
            valget "Slett". Det er mulig å slette flere filer samtidig, Dette
            gjøres ved å markere filene og klikke på "Slett markerte".
          </p>
          <p>
            Merk at du maksimalt kan laste opp 100 filer om gangen. Samlet
            størrelse på filene du laster opp kan ikke overstige 300 MB om
            gangen. Én enkelt fil kan maksimalt være 40 MB stor.
          </p>
          <p>
            Godkjente filtyper er: 7z, avi, bmp, c, css, doc, docm, docx, dot,
            dotm, dotx, emf, flv, ggb, gif, gz, h, hlp, htm, html, jpe, jpeg,
            jpg, js, m4a, mdb, mdi, mht, mm, mov, movie, mp3, mp4, mpeg, mpp,
            odb, odp, ods, odt, ott, pdf, png, ppsm, ppsx, ppt, pptm, pptx, qt,
            rap, rar, rm, rtf, sib, stw, svg, swf, sxc, sxw, tar, tex, tif,
            tiff, tii, txt, vsd, wav, wmf, wmv, wri, xls, xlsb, xlsm, xlsx, xml,
            xps, z, zip og zipx.
          </p>
          <img
            src="/images/gruppeopplaster-lastopp.png"
            aria-label="last opp side"
          />
        </section>
        <section>
          <h2 id="Feilmeldinger">Feilmeldinger</h2>
          <p>
            Det er ikke mulig å laste opp filer med feil, de må fjernes. Du kan
            deretter korrigere feilen og laste opp på nytt. Vanlige feil er at
            filen er for stor eller at filnavnet ikke er gyldig.
          </p>
          <p>Man får feilmeldinger for følgende feil:</p>
          <ul>
            <li> Filen har et ugyldig filnavn</li>
            <li>
              En fil med dette navnet er allerede lastet opp (filnavnet er ikke
              unikt)
            </li>
            <li>Filen har en ugyldig filtype</li>
            <li>Filen er for stor (over 40 MB).</li>
            <li>
              Kandidaten finnes ikke i PGS med eksamen i dag for angitt skole
            </li>
            <li>
              Kandidaten har blitt slettet (eksempelvis pga. feilpåmelding)
            </li>
            <li>Totalt antall filer er over 100.</li>
            <li>Samlet størrelse for filene er over 300 MB.</li>
          </ul>
          <img
            src="/images/feilmeldingerGruppeopplaster.png"
            alt="Skjermdumpen viser ulike feilmeldinger som kan oppstå ved gruppeopplasting."
          />
        </section>
        <section>
          <h2 id="Eksamen-med-en-del">Eksamen med én del</h2>
          <p>
            Når en fil er korrekt lastet opp for eksamen med én del, så vil PGS
            automatisk sette eksamensdelen til "Eksamen". Det er ikke mulig å
            endre eksamensdel for eksamen med én del.
          </p>
          <p>
            Det er mulig å samtidig laste opp filer for både todelt eksamen og
            eksamen med én del.
          </p>
          <p>
            Merk at opplastede filer for eksamen med én del vil være synlig i
            PGS-monitor. Det er egne
            <a
              className="ml-1 mr-1"
              href="/brukerveiledning"
            >
              brukerveiledninger
            </a>
            for PGS-monitor og kandidatgjennomføring.
          </p>
          <img
            src="/images/gruppeopplaster-endelt.png"
            aria-label="endelte filer"
          />
        </section>

        <section>
          <h2 id="Todelt-eksamen">Todelt eksamen</h2>
          <p>
            Ved todelt eksamen må du angi hvilken eksamensdel hver fil tilhører.
            Det er viktig å angi korrekt del slik at visningen for sensor blir
            riktig.
          </p>
          <ul>
            <li>Eksamen del 1 </li>
            <li>Eksamen del 2</li>
            <li>Eksamen del 1 og 2</li>
          </ul>
          <p>
            Det siste alternativet skal kun benyttes dersom både del 1 og del 2
            er innskannet samlet slik det kun er én fil for begge delene.
          </p>
          <p>
            Eksamensbesvarelsene som ikke har fått tildelt eksamensdel blir
            automatisk markert med rødt.
          </p>
          <p>
            Det er mulig å endre eksamensdel for flere filer samtidig. Dette
            gjøres ved å markere filene og klikke på «Velg eksamensdel for
            markerte».
          </p>
          <p>
            Når eksamensdel er angitt vil filene også være synlige i
            PGS-monitor. Leverte filer for «Eksamen del 1» og «Eksamen del
            1 og 2», vil vises på kandidatens kvitteringsside sammen med filer
            med "Eksamen del 2".
          </p>
          <p>
            Merk at kandidaten ikke vil se filer som er lastet opp av
            eksamensvakten på sin leveringsside. Dette gjelder både filer lastet
            opp via "Lever for kandidat" og via gruppeopplasteren.
          </p>
          <img
            src="/images/gruppeopplaster-velgeksamensdel.png"
            aria-label="velg eksamensdel"
          />
        </section>
        <div>
          <h2 id="Lever-filene">Lever filene</h2>
          <p>
            Når alle filene er lastet opp og eksamensdel er angitt skal du
            trykke «Lever alle filene». Du vil da komme til fanen for "Leverte
            filer" som bekrefter at filene er levert.
          </p>
          <p>
            Dersom du ikke ønsker å vise filene som du allerede har lastet opp,
            kan du velge "Tøm listen". Merk at du da ikke får vist disse filene
            igjen i gruppeopplasteren. Filene vil imidlertid vises i
            PGS-monitor og på kandidatenes kvitteringsside.
          </p>
          <p>
            Merk at dersom du oppdaterer gruppeopplasteren i nettleseren vil
            alle listene over feilede filer, opplastede filer og leverte filer
            forsvinne fra visningen. Det vil da ikke være mulig å vise disse
            filene på nytt i gruppeopplasteren. Dette skyldes en teknisk
            begrensning som vi jobber med å utbedre. Eventuelle filer som ble
            lastet opp korrekt og har fått angitt eksamensdel kan håndteres
            videre i PGS-monitor.
          </p>{" "}
          <img
            src="/images/gruppeopplaster-levert.png"
            aria-label="leverte filer"
          />
        </div>
      </div>
    </>
  );
}
