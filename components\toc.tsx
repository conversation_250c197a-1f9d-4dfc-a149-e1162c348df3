"use client";
import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

type TOCItem = {
  id: string;
  text: string;
};

export default function TableOfContents() {
  const [toc, setToc] = useState<TOCItem[]>([]);
  const [active, setActive] = useState("");
  const HEADER_OFFSET = 100;

  const scrollToSection = useCallback((slug: string) => {
    const element = document.getElementById(slug);
    if (element) {
      const offsetPosition =
        element.getBoundingClientRect().top +
        window.pageYOffset -
        HEADER_OFFSET;
      window.scrollTo({ top: offsetPosition, behavior: "smooth" });
    }
  }, []);

  useEffect(() => {
    // Extract h2 headings and generate TOC
    const headings = Array.from(document.querySelectorAll("h2")).map(
      (heading) => ({
        id: heading.id || heading.innerText.replace(/\s+/g, "-").toLowerCase(),
        text: heading.innerText,
      })
    );
    setToc(headings);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + HEADER_OFFSET;
      const currentActive = toc.reduce((prev, heading) => {
        const element = document.getElementById(heading.id);
        return element && scrollPosition >= element.offsetTop - 10
          ? heading.id
          : prev;
      }, active);
      if (currentActive !== active) setActive(currentActive);
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => window.removeEventListener("scroll", handleScroll);
  }, [active, toc]);

  const handleClick = (slug: string, event: React.MouseEvent) => {
    event.preventDefault();
    scrollToSection(slug);
    setActive(slug);
  };

  return (
    <Card className="bg-stalbla ">
      <CardHeader>
        <CardTitle>
          <span className="text-lg">På denne siden:</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="ml-5">
          {toc.map((h) => (
            <li
              className={`p-0.5 text-sm list-disc ${
                h.id === active ? "font-bold" : ""
              }`}
              key={h.id}
            >
              <a href={`#${h.id}`} onClick={(e) => handleClick(h.id, e)}>
                {h.text}
              </a>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
