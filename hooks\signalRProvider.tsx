"use client";

import {
  createContext,
  useContext,
  useCallback,
  useState,
  useRef,
  ReactNode,
  useEffect,
} from "react";
import * as signalR from "@microsoft/signalr";
import { useRole } from "@/context/RoleContext";
import { useSession } from "next-auth/react";
import { SignalRMessageEnum } from "@/enums/SignalRMessageEnum";
import { IAuthorizationRevokedPayload } from "@/interface/IAuthorizationRevokedPayload";
import jwt from "jsonwebtoken";

export interface SignalRStatusUpdate {
  candidateNumber: string;
  statusPart1: number;
  statusPart2: number;
  testPartId: number;
}

export interface SignalRAttendenceUpdate {
  candidateNumber: string;
  attending: boolean;
  testPartId: number;
  paperOnly: boolean;
  schoolId: string;
}

export interface SignalRAccessRequestUpdate {
  candidateNumber: string;
  hasRequestedAccess: boolean;
  schoolId: string;
  timestamp?: string;
}

export interface SignalRAccessDeniedUpdate {
  id: string;
  candidateNumber: string;
}

interface SignalRConnectionContextValue {
  connection: signalR.HubConnection | null;
  reconnect: () => Promise<void>;
  isConnected: boolean;
  error: string | null;
  currentSchool: string | null;
  accessRequest: SignalRAccessRequestUpdate | null;
  accessDenied: SignalRAccessDeniedUpdate | null;
  authorizationRevoked: IAuthorizationRevokedPayload | null;
}

const SignalRConnectionContext = createContext<
  SignalRConnectionContextValue | undefined
>(undefined);
const MAX_RETRY_ATTEMPTS = 10;
const MAX_NEGOTIATE_RETRIES = 3;

// Connection pooling - simple cache for reusing connections
const connectionCache = new Map<string, signalR.HubConnection>();
const activeSchools = new Set<string>();

function getCachedConnection(schoolId: string): signalR.HubConnection | null {
  return connectionCache.get(schoolId) || null;
}

function setCachedConnection(schoolId: string, connection: signalR.HubConnection): void {
  connectionCache.set(schoolId, connection);
  activeSchools.add(schoolId);
}

function removeCachedConnection(schoolId: string): void {
  const conn = connectionCache.get(schoolId);
  if (conn) {
    connectionCache.delete(schoolId);
    activeSchools.delete(schoolId);
  }
}

export function SignalRConnectionProvider({
  children,
}: {
  children: ReactNode;
}) {
  const { selectedRole } = useRole();
  const { status: authStatus } = useSession(); // Rename to avoid conflict with SignalR status
  const schoolId = selectedRole?.selectedSchoolId;

  const [connection, setConnection] = useState<signalR.HubConnection | null>(
    null
  );
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [candidateStatus, setCandidateStatus] =
    useState<SignalRStatusUpdate | null>(null);
  const [candidateAttendence, setCandidateAttendence] =
    useState<SignalRAttendenceUpdate | null>(null);
  const [accessRequest, setAccessRequest] =
    useState<SignalRAccessRequestUpdate | null>(null);
  const [accessDenied, setAccessDenied] =
    useState<SignalRAccessDeniedUpdate | null>(null);
  const [authorizationRevoked, setAuthorizationRevoked] =
    useState<IAuthorizationRevokedPayload | null>(null);

  const handleAccessRequest = useCallback(
    (update: SignalRAccessRequestUpdate) => {
      setAccessRequest(update);
    },
    []
  );

  const handleAccessDenied = useCallback(
    (update: SignalRAccessDeniedUpdate) => {
      console.log(`Access denied for candidate ${update.candidateNumber}`);
      setAccessDenied(update);
    },
    []
  );

  const handleAuthorizationRevoked = useCallback(
    (payload: IAuthorizationRevokedPayload) => {
      console.log(`Authorization revoked for candidate ${payload.candidateNumber}`);
      console.log(`Deauthorized sessions:`, payload.deauthorizedSessions);
      setAuthorizationRevoked(payload);
    },
    []
  );

  const isConnecting = useRef(false);
  const currentSchoolRef = useRef<string | null>(null);
  const retryCount = useRef(0);

  // Helper function to determine if an error is retryable
  const isRetryableError = useCallback((error: any, statusCode?: number): boolean => {
    // Don't retry authentication errors
    if (statusCode === 401 || statusCode === 403) {
      return false;
    }
    
    // Don't retry client errors (400-499), but retry server errors (500+)
    if (statusCode && statusCode >= 400 && statusCode < 500) {
      return false;
    }

    // Retry network errors, timeouts, and server errors
    if (error instanceof Error) {
      if (error.name === 'AbortError') return true; // Timeout
      if (error.message.includes('fetch')) return true; // Network error
      if (error.message.includes('Failed to fetch')) return true; // Network error
    }
    
    // Retry server errors (5xx) and unknown errors
    return true;
  }, []);

  const getNegotiateResponse = useCallback(async (schoolId: string, attempt = 1): Promise<{ url: string; accessToken: string }> => {
    if (authStatus !== "authenticated") {
      throw new Error("Bruker er ikke autentiseret. Kan ikke kalle negotiate.");
    }

    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch("/api/negotiate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ schoolId }),
        credentials: "include",
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("Sesjonen er utløpt. Vennligst logg inn på nytt.");
        }
        
        // Check if we should retry this error
        if (attempt < MAX_NEGOTIATE_RETRIES && isRetryableError(null, response.status)) {
          console.warn(`Negotiate attempt ${attempt} failed with status ${response.status}, retrying...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000)); // Exponential backoff
          return getNegotiateResponse(schoolId, attempt + 1);
        }
        
        throw new Error(`Kunne ikke etablere SignalR-tilkobling: ${response.statusText}`);
      }

      return response.json() as Promise<{ url: string; accessToken: string }>;
    } catch (error) {
      clearTimeout(timeoutId);
      
      // Handle timeout and network errors with retry
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          if (attempt < MAX_NEGOTIATE_RETRIES) {
            console.warn(`Negotiate attempt ${attempt} timed out, retrying...`);
            await new Promise(resolve => setTimeout(resolve, attempt * 1000));
            return getNegotiateResponse(schoolId, attempt + 1);
          }
          throw new Error("Forespørsel tok for lang tid. Prøv igjen senere.");
        }
        
        // Retry network errors
        if (isRetryableError(error) && attempt < MAX_NEGOTIATE_RETRIES) {
          console.warn(`Negotiate attempt ${attempt} failed with error: ${error.message}, retrying...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          return getNegotiateResponse(schoolId, attempt + 1);
        }
      }
      
      throw error;
    }
  }, [authStatus, isRetryableError]);

  const setupConnection = useCallback(
    async (schoolId: string) => {
      if (authStatus !== "authenticated") {
        setError("Bruker er ikke autentisiert. SignalR-tilkobling startes ikke.");
        return null;
      }
      if (!schoolId || isConnecting.current) return null;

      // Check for existing cached connection first
      const existingConnection = getCachedConnection(schoolId);
      if (existingConnection && existingConnection.state === signalR.HubConnectionState.Connected) {
        console.log(`[SignalR] Reusing existing connection for school ${schoolId}`);
        return existingConnection;
      }

      isConnecting.current = true;

      try {
        const { url, accessToken: initialToken } = await getNegotiateResponse(schoolId);

        const newConnection = new signalR.HubConnectionBuilder()
          .withUrl(url, {
            accessTokenFactory: async () => {
              if (authStatus !== "authenticated") {
                throw new Error("Bruker er ikke autentiseret. Kan ikke fornye token.");
              }
              try {
                const decoded = jwt.decode(initialToken) as { exp: number } | null;
                if (decoded && typeof decoded.exp === 'number' && decoded.exp * 1000 > Date.now() + 30000) {
                  return initialToken;
                }
              } catch (error) {
                console.error("Token decode error:", error);
                // Continue to refresh token below
              }
              // Ensure negotiate is only called if authenticated - with retry logic
              const { accessToken } = await getNegotiateResponse(schoolId);
              return accessToken;
            },
          })
          .withAutomaticReconnect({
            nextRetryDelayInMilliseconds: (retryContext) => {
              if (retryContext.previousRetryCount >= MAX_RETRY_ATTEMPTS) {
                return null;
              }
              return Math.min(retryContext.previousRetryCount * 1000, 30000);
            },
          })
          .configureLogging(signalR.LogLevel.Warning)
          .build();

        // Set connection timeouts
        newConnection.keepAliveIntervalInMilliseconds = 15000;
        newConnection.serverTimeoutInMilliseconds = 30000;

        // Set up message handlers
        newConnection.on(
          SignalRMessageEnum.CandidateStatusChanged,
          setCandidateStatus
        );
        newConnection.on(
          SignalRMessageEnum.CandidateAttendenceChanged,
          setCandidateAttendence
        );
        newConnection.on(SignalRMessageEnum.AccessRequest, handleAccessRequest);
        newConnection.on(SignalRMessageEnum.AccessDenied, handleAccessDenied);
        newConnection.on(SignalRMessageEnum.AuthorizationRevoked, handleAuthorizationRevoked);

        // Connection state handlers
        newConnection.onreconnecting((error) => {
          setIsConnected(false);
          retryCount.current++;

          if (retryCount.current >= MAX_RETRY_ATTEMPTS) {
            setError(
              "Maks antall forsøk på tilkobling nådd. Vennligst last siden på nytt."
            );
            newConnection.stop();
          } else {
            setError(
              `Mistet tilkobling, prøver å koble til på nytt... (Forsøk ${retryCount.current}/${MAX_RETRY_ATTEMPTS})`
            );
          }
        });

        newConnection.onreconnected(() => {
          retryCount.current = 0;
          setIsConnected(true);
          setError(null);
        });

        await newConnection.start();
        
        // Cache the successful connection
        setCachedConnection(schoolId, newConnection);
        console.log(`[SignalR] New connection created and cached for school ${schoolId}`);
        
        return newConnection;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Tilkobling feilet";
        setError(errorMessage);
        return null;
      } finally {
        isConnecting.current = false;
      }
    },
    [getNegotiateResponse, authStatus, handleAccessRequest, handleAccessDenied, handleAuthorizationRevoked]
  );

  const cleanup = useCallback(
    async (conn: signalR.HubConnection | null, schoolId: string | null) => {
      if (!conn) return;

      try {
        // Remove all event handlers
        conn.off(SignalRMessageEnum.CandidateStatusChanged);
        conn.off(SignalRMessageEnum.CandidateAttendenceChanged);
        conn.off(SignalRMessageEnum.AccessRequest);
        conn.off(SignalRMessageEnum.AccessDenied);
        conn.off(SignalRMessageEnum.AuthorizationRevoked);

        if (schoolId && authStatus === "authenticated") {
          // Add timeout for cleanup call too
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          try {
            await fetch("/api/negotiate", {
              method: "DELETE",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ schoolId }),
              credentials: "include",
              signal: controller.signal,
            });
            clearTimeout(timeoutId);
          } catch (cleanupError) {
            clearTimeout(timeoutId);
            console.warn("Cleanup negotiate call failed:", cleanupError);
            // Don't throw here - cleanup should be non-blocking
          }
        }

        await conn.stop();
        
        // Remove from cache when stopping connection
        if (schoolId) {
          removeCachedConnection(schoolId);
          console.log(`[SignalR] Connection removed from cache for school ${schoolId}`);
        }
        
        setConnection(null);
        setIsConnected(false);
        setError(null);
        retryCount.current = 0;
      } catch (error) {
        console.error("Cleanup error:", error);
        // Don't throw here - cleanup should be non-blocking
      }
    },
    [authStatus]
  );

  // Handle school changes
  useEffect(() => {
    let isActive = true;

    const handleSchoolChange = async () => {
      if (authStatus !== "authenticated") {
        if (connection) { // Cleanup if there was a connection from a previous authenticated state
          await cleanup(connection, currentSchoolRef.current);
          currentSchoolRef.current = null;
        }
        setError("Bruker er ikke autentiseret. SignalR-tilkobling ikke aktiv.");
        setIsConnected(false);
        return;
      }

      if (schoolId === currentSchoolRef.current && connection?.state === signalR.HubConnectionState.Connected) return;

      if (connection && currentSchoolRef.current) {
        await cleanup(connection, currentSchoolRef.current);
        // Add delay between cleanup and new connection
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      if (!isActive || !schoolId) {
         if (connection) await cleanup(connection, currentSchoolRef.current);
         currentSchoolRef.current = null;
        return;
      }
      
      setError(null); // Clear previous errors if any
      const newConnection = await setupConnection(schoolId);
      if (newConnection && isActive) {
        setConnection(newConnection);
        setIsConnected(true);
        currentSchoolRef.current = schoolId;
      } else if (isActive && authStatus === "authenticated") {
        // If setupConnection returned null due to other reasons but user is authenticated
        setError("Kunne ikke etablere SignalR-tilkobling.");
      }
    };

    handleSchoolChange();

    return () => {
      isActive = false;
    };
  }, [schoolId, cleanup, setupConnection, authStatus]);

  const reconnect = useCallback(async () => {
    if (!currentSchoolRef.current || authStatus !== "authenticated") return;

    retryCount.current = 0;
    await cleanup(connection, currentSchoolRef.current);
    const newConnection = await setupConnection(currentSchoolRef.current);
    if (newConnection) {
      setConnection(newConnection);
      setIsConnected(true);
    }
  }, [connection, cleanup, setupConnection, authStatus]);

  return (
    <SignalRConnectionContext.Provider
      value={{
        connection,
        reconnect,
        isConnected,
        error,
        currentSchool: currentSchoolRef.current,
        accessRequest,
        accessDenied,
        authorizationRevoked,
      }}
    >
      {children}
    </SignalRConnectionContext.Provider>
  );
}

export function useSignalRConnection() {
  const context = useContext(SignalRConnectionContext);
  if (!context) {
    throw new Error(
      "useSignalRConnection must be used within a SignalRConnectionProvider"
    );
  }
  return context;
}
