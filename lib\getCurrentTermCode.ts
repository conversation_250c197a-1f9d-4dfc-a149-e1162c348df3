import dayjs, { Dayjs } from "dayjs";

export const getCurrentTermCode = (date: Dayjs = dayjs()): string => {
  const currentYear = date.year();
  const currentMonth = date.month() + 1; // dayjs months are 0-indexed

  // Semester cutoff dates
  const springStart = 1; // January
  const fallStart = 7; // July

  const isSpring = currentMonth >= springStart && currentMonth < fallStart;
  const termPrefix = isSpring ? "V-" : "H-";

  return `${termPrefix}${currentYear}`;
};
