import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { generateSasTokenUrl } from "@/lib/blobHelper";
import { getAppInsightsServer } from "@/lib/appInsightsServer";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET(request: Request) {
  try {
    const session: ISession | null = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    let permission: string;
    const { searchParams } = new URL(request.url);
    const blobName = searchParams.get("blobname");
    const container = searchParams.get("container");
    const download = searchParams.get("download");

    // Sjekker om blobName er gyldig
    if (!blobName || !container) {
      return NextResponse.json(
        { message: "Mangler blobnavn eller containernavn i forespørselen" },
        { status: 400 }
      );
    }

    if (download === "true") permission = "r";
    else permission = "d";

    return NextResponse.json({
      sastoken: await generateSasTokenUrl(container, blobName, permission),
    });
  } catch (error) {
    // Logger feilen og sender en feilmelding til klienten
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "getblobsastoken",
        response: error instanceof Error ? error.message : "Unknown error",
      },
    });
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
