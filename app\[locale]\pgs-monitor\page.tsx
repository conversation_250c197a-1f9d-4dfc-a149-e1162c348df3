import React from "react";
import { CandidateProvider } from "@/context/CandidateMonitorContext";
import BrukerVeiledningButton from "../gruppeopplaster/brukerveiledningButton";
import { DialogProvider } from "@/context/DialogMonitorContext";
import SearchForm from "./searchForm";
import SchoolSelector from "@/components/schoolSelector";
import SchoolSelectorContainer from "./schoolSelectorContainer";
import { SignalRConnectionProvider } from "@/hooks/signalRProvider";
import { AccessRequestsProvider } from "@/hooks/accessRequestProvider";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "PGS-monitor - PGS-admin",
 
};

export default async function Home() {
  // Get server time to pass to CandidateProvider
  const serverTime = new Date().toISOString();

  return (
    <>
      <div className="py-6 bg-header">
        <div className="container-wrapper-lg flex flex-col gap-3">
          <div className="flex md:flex-row flex-col md:justify-between md:items-center items-start">
            <div>
              <h1 className="text-4xl">PGS-monitor</h1>
            </div>
            <div className="mt-4 md:mt-0 ">
              <BrukerVeiledningButton />
            </div>
          </div>
          <SchoolSelectorContainer />
        </div>
      </div>
      <div className="container-wrapper-lg mb-8 mt-4">
        <SignalRConnectionProvider>
          <CandidateProvider serverTime={serverTime}>
            <AccessRequestsProvider>
              <DialogProvider>
                <SearchForm />
              </DialogProvider>
            </AccessRequestsProvider>
          </CandidateProvider>
        </SignalRConnectionProvider>
      </div>
    </>
  );
}
