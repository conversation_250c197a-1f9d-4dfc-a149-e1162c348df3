import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { IAccessRequestUpdate } from "@/hooks/accessRequestProvider";

// Type definitions for access status
export enum AccessStatusType {
  DOCUMENTED_ABSENCE = "Dokumentert fravær",
  UNDOCUMENTED_ABSENCE = "Ikke-dokumentert fravær",
  WAITING_FOR_EXAM_START = "Venter på eksamensstart",
  HAS_ACCESS = "Har tilgang",
  BLOCKED_ACCESS = "Digital tilgang sperret",
  WAITING_FOR_ACCESS = "Venter på tilgang",
  NO_ACCESS = "Ikke tilgang",
}

export enum AccessStatusColor {
  GRAY = "#6B7280",
  GREEN = "#7DBF9D",
  ORANGE = "#EF6C00",
  YELLOW = "#FFA726",
  LIGHT_GRAY = "#D1D5DB",
}

export interface AccessStatusResult {
  text: AccessStatusType;
  color: AccessStatusColor;
}

interface StatusCalculationParams {
  candidate: ICandidateMonitor;
  authorizedCandidates: Record<string, boolean>;
  accessRequests: IAccessRequestUpdate[];
  blockedUsers: Record<string, boolean>;
  hasAbsence: Record<string, boolean>;
  candidatesWithLevertStatus: Record<string, boolean>;
  candidatesWaitingForExamStart: Record<string, boolean>;
}

/**
 * Centralized function to calculate candidate access status
 * This replaces the duplicate logic in getDisplayStatusText and getStatusText
 */
export const getCandidateAccessStatus = ({
  candidate,
  authorizedCandidates,
  accessRequests,
  blockedUsers,
  hasAbsence,
  candidatesWithLevertStatus,
  candidatesWaitingForExamStart,
}: StatusCalculationParams): AccessStatusResult => {
  const candidateNumber = candidate.candidateNumber;
  const status =
    candidate.deliveryStatusPart2 !== -1
      ? candidate.deliveryStatusPart2
      : candidate.deliveryStatusPart1;

  const statusEnum =
    CandidateStatusEnum[status as unknown as keyof typeof CandidateStatusEnum];
  const isBlocked = blockedUsers[candidateNumber];
  const isAuthorized = authorizedCandidates[candidateNumber];
  const hasLevertStatus = candidatesWithLevertStatus[candidateNumber];
  const hasAbsenceStatus = hasAbsence[candidateNumber];
  const isWaitingForExamStart = candidatesWaitingForExamStart[candidateNumber];

  const hasAccessRequest = accessRequests.some(
    (req) => req.candidateNumber === candidateNumber
  );

  const isWaitingForAccess = !isAuthorized && hasAccessRequest;
  const isRequestingNewAccess = isAuthorized && hasAccessRequest;

  // Priority-based status calculation (same logic as before, but centralized)
  if (hasAbsenceStatus) {
    const text =
      status === CandidateStatusEnum.DokumentertFravaer
        ? AccessStatusType.DOCUMENTED_ABSENCE
        : AccessStatusType.UNDOCUMENTED_ABSENCE;
    return { text, color: AccessStatusColor.GRAY };
  }

  if (isAuthorized && isWaitingForExamStart) {
    return {
      text: AccessStatusType.WAITING_FOR_EXAM_START,
      color: AccessStatusColor.GREEN,
    };
  }

  if ((!isBlocked && isAuthorized) || isRequestingNewAccess) {
    return {
      text: AccessStatusType.HAS_ACCESS,
      color: AccessStatusColor.GREEN,
    };
  }

  if (isBlocked) {
    return {
      text: AccessStatusType.BLOCKED_ACCESS,
      color: AccessStatusColor.ORANGE,
    };
  }

  if (hasLevertStatus) {
    return {
      text: AccessStatusType.NO_ACCESS,
      color: AccessStatusColor.LIGHT_GRAY,
    };
  }

  if (isWaitingForAccess) {
    return {
      text: AccessStatusType.WAITING_FOR_ACCESS,
      color: AccessStatusColor.YELLOW,
    };
  }

  return {
    text: AccessStatusType.NO_ACCESS,
    color: AccessStatusColor.LIGHT_GRAY,
  };
};
