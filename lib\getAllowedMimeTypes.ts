import { getAppInsightsServer } from "./appInsightsServer";
import { getAccessToken } from "./getAccessToken";
import { getValueFromRedis, setValueInRedis } from "@/app/lib/redisHelper";

const PgsaApiUrl = process.env.PGSA_PGS_API_URL;
const redisKey = "PGS:AllowedMimeTypes";

const telemetryClient = getAppInsightsServer();

export async function getAllowedMimeTypes(): Promise<IAllowedMimeTypes[]> {
  let mimeTypesResponse = null;

  try {
    const redisResponse = await getValueFromRedis(redisKey);
    if (redisResponse) {
      return JSON.parse(redisResponse) as IAllowedMimeTypes[];
    }

    const clientId: string =
      process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
    const clientSecret: string =
      process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
    const scope: string =
      process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
    const accesstokenKey: string = "PGSE:PGSA:AccessToken";

    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    mimeTypesResponse = await fetch(`${PgsaApiUrl}/api/utils/mimetype`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!mimeTypesResponse.ok) {
      throw new Error("Response not ok");
    }

    const fileTypesDataJson: any = await mimeTypesResponse.json();
    const filetypesData: IAllowedMimeTypes[] =
      fileTypesDataJson?.MimeTypeFileExtension ?? [];

    const allowedMimeTypes = filetypesData.filter(
      (mimeType) => mimeType.AllowedInUpload
    );

    await setValueInRedis(redisKey, JSON.stringify(allowedMimeTypes), 1200);
    return allowedMimeTypes;
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "deleteFileNotification",
        status: mimeTypesResponse ? mimeTypesResponse.status : 0,
        response: mimeTypesResponse
          ? await mimeTypesResponse.text()
          : "Tom respons",
      },
    });
    console.error(error);
    throw new Error("Klarte ikke å hente tillatte mime-typer");
  }
}
