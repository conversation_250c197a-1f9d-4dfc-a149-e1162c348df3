"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";

const statusOptions = [
  { value: "0", label: "Ikke innlogget" },
  { value: "1", label: "Venter på tilgang" },
  { value: "2", label: "Innlogget og autentisert" },
  { value: "8", label: "Lastet opp" },
  { value: "6", label: "Levert digitalt" },
  { value: "4", label: "Levert manuelt" },
  { value: "11", label: "Dokumentert fravær" },
  { value: "7", label: "Ikke-dokumentert fravær" },
];

export default function MockStatusPage() {
  const [candidateNumber, setCandidateNumber] = useState("");
  const [status, setStatus] = useState("");
  const [testPartId, setTestPartId] = useState("1");
  const [isSending, setIsSending] = useState(false);

  const sendMockStatus = async () => {
    if (!candidateNumber || !status) {
      toast({
        title: "Manglende input",
        description: "Vennligst fyll ut alle felt",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);

    try {
      const response = await fetch("/api/sendStatusUpdateRequestSignalRDemo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          candidateNumber,
          status: parseInt(status),
          testPartId: parseInt(testPartId),
          schoolId: "874605972",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send mock status");
      }

      toast({
        title: "Status sendt",
        description: `Status oppdatert for kandidat ${candidateNumber}`,
      });
    } catch (error) {
      console.error("Error sending mock status:", error);
      toast({
        title: "Feil",
        description: "Kunne ikke sende statusoppdatering",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Mock Status Oppdatering</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Kandidatnummer
            </label>
            <Input
              placeholder="Skriv inn kandidatnummer"
              value={candidateNumber}
              onChange={(e) => setCandidateNumber(e.target.value)}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Status</label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Velg status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Test Del</label>
            <Select value={testPartId} onValueChange={setTestPartId}>
              <SelectTrigger>
                <SelectValue placeholder="Velg del" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Del 1</SelectItem>
                <SelectItem value="2">Del 2</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={sendMockStatus}
            disabled={isSending}
            className="w-full"
          >
            {isSending ? "Sender..." : "Send Status Oppdatering"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
