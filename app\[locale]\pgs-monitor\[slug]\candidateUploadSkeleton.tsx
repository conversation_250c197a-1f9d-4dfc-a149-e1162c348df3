import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

const CandidateUploadSkeleton = () => {
  return (
    <div className="flex flex-col gap-4">
      {/* Info box */}
      <div className="bg-s<PERSON><PERSON> rounded p-4 w-96">
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-40" />
          <Skeleton className="h-4 w-36" />
          <Skeleton className="h-4 w-44" />
        </div>
      </div>

      {/* Upload area */}
      <div className="border-2 border-dashed rounded-lg p-6 w-3/5">
        <div className="flex flex-col items-center gap-4">
          <Skeleton className="h-10 w-10 rounded-full" />
          <Skeleton className="h-6 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Tabs */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>

        {/* Table header */}
        <div className="grid grid-cols-6 gap-4 p-4 border-b">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
        </div>

        {/* Table content placeholder */}
        <div className="p-2 flex flex-col gap-2">
          <Skeleton className="h-6" />
          <Skeleton className="h-6" />
          <Skeleton className="h-6" />
        </div>

        {/* Action buttons */}
        <Skeleton className="h-10 w-32" />
      </div>
    </div>
  );
};

export default CandidateUploadSkeleton;
