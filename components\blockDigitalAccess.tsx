import { toast } from "@/components/ui/use-toast";
import { useRole } from "@/context/RoleContext";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { useCandidate } from "@/context/CandidateMonitorContext";
import { ActivityLogger } from "@/lib/services/ActivityLogger";
import { OperationEnum } from "@/enums/OperationEnum";

interface BlockDigitalAccessProps {
  candidateInfo: ICandidateMonitor;
}

const BlockDigitalAccess: React.FC<BlockDigitalAccessProps> = ({
  candidateInfo,
}) => {
  const { selectedRole } = useRole();
  const { blockedUsers, updateBlockedUsers } = useCandidate();

  const isBlocked = blockedUsers[candidateInfo.candidateNumber];

  const showToast = (
    title: string,
    description: string,
    variant: "destructive" | "success"
  ) => {
    toast({
      title,
      description,
      className: variant === "destructive" ? "bg-red-50" : "bg-green-50",
    });
  };

  const handleAccessToggle = async () => {
    try {
      // 1. Update blocked status in backend
      const response = await fetch(
        `/api/blockedUsers/${selectedRole?.selectedSchoolId}/${candidateInfo.userId}`,
        {
          method: isBlocked ? "DELETE" : "POST",
        }
      );

      if (!response.ok) {
        throw new Error(
          isBlocked
            ? "Could not unblock user access"
            : "Could not block user access"
        );
      }

      // 2. If blocking (not unblocking), also deauthorize user objects
      if (!isBlocked) {
        const deauthorizedResponse = await fetch(
          "/api/deauthorizeUserObjects",
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              candidateNumber: candidateInfo.candidateNumber,
            }),
          }
        );

        if (!deauthorizedResponse.ok) {
          throw new Error(
            "Could not deauthorize user objects. Digital access not blocked."
          );
        }

        // 3. Try to remove any grant access requests (optional)
        try {
          await fetch(`/api/grantAccessRequest`, {
            method: "DELETE",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              candidateNumber: candidateInfo.candidateNumber,
              schoolId: selectedRole?.selectedSchoolId,
            }),
          });
        } catch (error) {
          console.error("Failed to remove grant access request", error);
        }
      }

      await ActivityLogger.logActivity(
        isBlocked ? OperationEnum.StatusUnBlocked : OperationEnum.StatusBlocked,
        {
          userId: candidateInfo.userId,
          candidateName: candidateInfo.candidateName || "",
          candidateNumber: candidateInfo.candidateNumber || "",
          candidateRegistrationId: candidateInfo.candidateRegistrationId || candidateInfo.userId,
        },
        {
          roleName: `${selectedRole?.displayRoleName}`,
        }
      );

      // 4. Update local state
      updateBlockedUsers({
        ...blockedUsers,
        [candidateInfo.candidateNumber]: !isBlocked,
      });

      // 5. Show success toast
      showToast(
        `${candidateInfo.candidateName} (${candidateInfo.candidateNumber})`,
        isBlocked
          ? "Digital tilgang er gjenopprettet"
          : "Digital tilgang er sperret",
        isBlocked ? "success" : "destructive"
      );
    } catch (error) {
      // 6. Show error toast if something fails
      showToast(
        "En feil har oppstått",
        isBlocked
          ? "Kunne ikke gjenopprette digital tilgang. Prøv igjen."
          : "Kunne ikke sperre digital tilgang. Prøv igjen.",
        "destructive"
      );
      console.error("Error in handleAccessToggle:", error);
    }
  };

  return (
    <button onClick={handleAccessToggle} className="text-sm">
      {isBlocked ? "Opphev sperre" : "Sperr digital tilgang"}
    </button>
  );
};

export default BlockDigitalAccess;
