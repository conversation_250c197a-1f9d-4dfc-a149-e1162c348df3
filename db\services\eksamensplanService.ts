import { getDbConnection } from "../connection";
import { Fagkodeeksamen } from "../models/Fagkodeeksamen";
import { Eksamensdel } from "../models/Eksamensdel";
import { IsNull } from "typeorm";
import { IEksamensplanApiData } from "@/interface/IEksamensplanImport";
import { parseStringPromise } from "xml2js";

export type EksamensplanData = IEksamensplanApiData;

interface XmlEksamensdelData {
  GjennomforingStart?: string[];
  GjennomforingStopp?: string[];
  Gjennomforingsystem?: string[];
  Eksamensveiledning?: string[];
  ErPlagiatkontroll?: boolean[];
}

interface XmlEksamenData {
  Fagkode: string[];
  Variantkode?: string[];
  Fagnavn?: string[];
  Varighet?: number[];
  Eksamensdato?: string[];
  Eksamenstid?: string[];
  ErTestFagkode?: boolean[];
  Eksamensdeler?: XmlEksamensdelData[];
}

interface XmlEksamensplanData {
  Opplaeringsniva?: string[];
  Oppgaveansvar?: string[];
  Eksamensperiode?: {
    Kode: string[];
  }[];
  Eksamener?: {
    PgsdEksamenViewModel: XmlEksamenData[];
  }[];
}

export class EksamensplanService {
  /**
   * Konverterer XML-data til vårt interne format
   */
  private parseXmlToEksamensplanData(
    xmlData: any,
    eksamensperiode: string
  ): EksamensplanData[] {
    const result: EksamensplanData[] = [];

    try {
      // Navigate the XML structure
      const root = xmlData.PgsdEksamensplanerViewModel;
      if (!root?.Eksamensplaner?.[0]?.PgsdEksamensplanViewModel) {
        console.warn("No eksamensplan data found in XML structure");
        return result;
      }

      const eksamensplaner = root.Eksamensplaner[0].PgsdEksamensplanViewModel;

      // Handle both single object and array cases
      const planArray = Array.isArray(eksamensplaner)
        ? eksamensplaner
        : [eksamensplaner];

      for (const plan of planArray) {
        if (!plan.Eksamener?.[0]?.PgsdEksamenViewModel) {
          continue;
        }

        const eksamener = plan.Eksamener[0].PgsdEksamenViewModel;
        const eksamenArray = Array.isArray(eksamener) ? eksamener : [eksamener];

        for (const eksamen of eksamenArray) {
          if (!eksamen.Fagkode?.[0]) {
            console.warn("Skipping eksamen without fagkode");
            continue;
          }

          let fagkode = eksamen.Fagkode[0];
          let variantkode: string | undefined = eksamen.Variant?.[0] || undefined;
          
          // Only use explicit variant field - fagkode is always the full fagkode
          // Examples:
          // - <Fagkode>REA3049</Fagkode><Variant>DIV</Variant> -> fagkode: REA3049, variantkode: DIV
          // - <Fagkode>MAT0015-SMA</Fagkode><Variant/> -> fagkode: MAT0015-SMA, variantkode: undefined
          
          if (variantkode && variantkode.trim() !== '') {
            console.log(`Fagkode with variant: fagkode: ${fagkode}, variantkode: ${variantkode}`);
          } else {
            variantkode = undefined;
            console.log(`Fagkode without variant: fagkode: ${fagkode}`);
          }
          
          let fagnavn = eksamen.Fagnavn?.[0] || fagkode; // Use fagkode as fallback

          // Validate field lengths to prevent database truncation errors
          if (fagkode && fagkode.length > 8) {
            console.warn(`Fagkode too long (${fagkode.length} chars): ${fagkode}. Truncating to 8 characters.`);
            fagkode = fagkode.substring(0, 8);
          }
          
          if (variantkode && variantkode.length > 8) {
            console.warn(`Variantkode too long (${variantkode.length} chars): ${variantkode}. Truncating to 8 characters.`);
            variantkode = variantkode.substring(0, 8);
          }

          if (fagnavn && fagnavn.length > 255) {
            console.warn(`Fagnavn too long (${fagnavn.length} chars): ${fagnavn}. Truncating to 255 characters.`);
            fagnavn = fagnavn.substring(0, 255);
          }

          // Parse eksamensdeler
          const eksamensdeler: any[] = [];
          if (eksamen.Eksamensdeler?.[0]?.PgsdEksamensdelViewModel) {
            const deler = eksamen.Eksamensdeler[0].PgsdEksamensdelViewModel;
            const delerArray = Array.isArray(deler) ? deler : [deler];

            for (let i = 0; i < delerArray.length; i++) {
              const del = delerArray[i];
              eksamensdeler.push({
                eksamensdelType: del.EksamensdelType?.[0],
                gjennomforingStart: del.GjennomforingStart?.[0],
                gjennomforingStopp: del.GjennomforingStopp?.[0],
                gjennomforingsystem: del.Gjennomforingsystem?.[0],
                eksamensveiledning: del.Eksamensveiledning?.[0],
                erPlagiatkontroll:
                  del.ErPlagiatkontroll?.[0] === "true" ||
                  del.ErPlagiatkontroll?.[0] === true,
              });
            }
          }

          result.push({
            fagkode,
            variantkode,
            eksamensperiode,
            fagnavn,
            varighet: eksamen.Varighet?.[0]
              ? parseInt(eksamen.Varighet[0])
              : undefined,
            eksamensdato: eksamen.Eksamensdato?.[0],
            eksamenstid: eksamen.Eksamenstid?.[0],
            erTestFagkode:
              eksamen.ErTestFagkode?.[0] === "true" ||
              eksamen.ErTestFagkode?.[0] === true,
            eksamensdeler,
          });
        }
      }

      console.log(`Parsed ${result.length} fagkoder from XML data`);
      return result;
    } catch (error) {
      console.error("Error parsing XML data:", error);
      throw new Error(
        `Failed to parse XML data: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Henter eksamensplandata fra ekstern API med retry-logikk
   */
  async fetchEksamensplanData(
    eksamensperiode: string
  ): Promise<EksamensplanData[]> {
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second base delay

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(
          `Attempt ${attempt} - Fetching XML data for periode: ${eksamensperiode}`
        );

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        const response = await fetch(
          `https://eksamenapi-tst2.udir.no/pgsd/eksamensplan?eksamensperiode=${eksamensperiode}`,
          {
            signal: controller.signal,
            headers: {
              Accept: "application/xml, text/xml, */*",
              "User-Agent": "PGS-Admin-Import/1.0",
            },
          }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(
            `HTTP error! status: ${response.status} - ${response.statusText}`
          );
        }

        // Get response as text since it's XML
        const xmlText = await response.text();

        if (!xmlText || xmlText.trim().length === 0) {
          throw new Error("API returned empty response");
        }

        // Parse XML to JavaScript object
        const xmlData = await parseStringPromise(xmlText, {
          explicitArray: true,
          ignoreAttrs: true,
          trim: true,
        });

        // Convert XML structure to our expected format
        const data = this.parseXmlToEksamensplanData(xmlData, eksamensperiode);

        console.log(
          `Successfully parsed ${data.length} records for periode: ${eksamensperiode}`
        );
        return data;
      } catch (error) {
        const isLastAttempt = attempt === maxRetries;
        const isAbortError =
          error instanceof Error && error.name === "AbortError";

        console.warn(`Attempt ${attempt} failed:`, {
          error: error instanceof Error ? error.message : String(error),
          isAbortError,
          eksamensperiode,
        });

        if (isLastAttempt) {
          console.error("All retry attempts failed for API call");
          throw new Error(
            `Failed to fetch data after ${maxRetries} attempts: ${
              error instanceof Error ? error.message : String(error)
            }`
          );
        }

        // Exponential backoff: wait longer between retries
        const delay = retryDelay * Math.pow(2, attempt - 1);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw new Error("Unexpected error in retry logic");
  }

  /**
   * Lagrer eksamensplandata til database med optimalisert bulk operasjoner
   */
  async saveEksamensplanData(
    eksamensplanData: EksamensplanData[]
  ): Promise<{ success: number; errors: number }> {
    const AppDataSource = await getDbConnection();
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let successCount = 0;
    let errorCount = 0;

    try {
      const fagkodeeksamensRepository =
        queryRunner.manager.getRepository(Fagkodeeksamen);

      // Bulk fetch existing fagkoder for this import batch
      const existingFagkoder = await fagkodeeksamensRepository
        .createQueryBuilder("f")
        .where("f.Eksamensperiode = :periode", {
          periode: eksamensplanData[0]?.eksamensperiode,
        })
        .getMany();

      // Create lookup map for faster access - use composite key that matches database constraints
      const existingFagkoderMap = new Map<string, Fagkodeeksamen>();
      const existingFagkoderByIdMap = new Map<string, Fagkodeeksamen>();

      existingFagkoder.forEach((f) => {
        // Create lookup key based on unique constraint: Fagkode + Variantkode + Eksamensperiode
        const compositeKey = `${f.Fagkode}_${f.Variantkode || "NULL"}_${
          f.Eksamensperiode
        }`;
        existingFagkoderMap.set(compositeKey, f);

        // Also create lookup by primary key ID for debugging
        existingFagkoderByIdMap.set(f.FagkodeeksamensID, f);
      });

      console.log(
        `Found ${existingFagkoder.length} existing fagkoder for periode ${eksamensplanData[0]?.eksamensperiode}`
      );

      // Prepare arrays for bulk operations
      const fagkoderToInsert: Fagkodeeksamen[] = [];
      const fagkoderToUpdate: Fagkodeeksamen[] = [];
      const insertedPrimaryKeys = new Set<string>(); // Track primary keys to prevent duplicates
      const eksamensdelerToProcess: Array<{
        fagkodeeksamen: Fagkodeeksamen;
        eksamensdeler: any[];
      }> = [];

      // Process each item and categorize for bulk operations
      for (const item of eksamensplanData) {
        try {
          const lookupKey = `${item.fagkode}_${item.variantkode || "NULL"}_${
            item.eksamensperiode
          }`;
          const eksisterende = existingFagkoderMap.get(lookupKey);

          // Generate expected primary key to check for conflicts (include variant)
          const variantPart = item.variantkode ? `_${item.variantkode}` : '';
          const expectedPrimaryKey = `${item.eksamensperiode}_${item.fagkode}${variantPart}`;
          const existingByPrimaryKey =
            existingFagkoderByIdMap.get(expectedPrimaryKey);

          let fagkodeeksamen: Fagkodeeksamen;

          if (eksisterende) {
            // Update existing record
            fagkodeeksamen = eksisterende;
            fagkodeeksamen.Fagnavn = item.fagnavn;
            fagkodeeksamen.Varighet = item.varighet || null;
            fagkodeeksamen.Eksamensdato = item.eksamensdato
              ? new Date(item.eksamensdato)
              : null;
            fagkodeeksamen.Eksamenstid = item.eksamenstid || null;
            fagkodeeksamen.ErTestFagkode = item.erTestFagkode || false;
            fagkoderToUpdate.push(fagkodeeksamen);
          } else if (existingByPrimaryKey) {
            // Found by primary key but not by composite key - this indicates a data inconsistency
            console.warn(`Data inconsistency detected for ${item.fagkode}:`, {
              lookupKey,
              expectedPrimaryKey,
              existingPrimaryKey: existingByPrimaryKey.FagkodeeksamensID,
              existingVariantkode: existingByPrimaryKey.Variantkode,
              incomingVariantkode: item.variantkode,
            });

            // Update the existing record found by primary key
            fagkodeeksamen = existingByPrimaryKey;
            fagkodeeksamen.Fagnavn = item.fagnavn;
            fagkodeeksamen.Varighet = item.varighet || null;
            fagkodeeksamen.Eksamensdato = item.eksamensdato
              ? new Date(item.eksamensdato)
              : null;
            fagkodeeksamen.Eksamenstid = item.eksamenstid || null;
            fagkodeeksamen.ErTestFagkode = item.erTestFagkode || false;
            fagkoderToUpdate.push(fagkodeeksamen);
          } else {
            // Check if we're already inserting this primary key in this batch
            if (insertedPrimaryKeys.has(expectedPrimaryKey)) {
              console.warn(
                `Duplicate primary key detected in batch: ${expectedPrimaryKey}. Skipping duplicate.`
              );
              errorCount++;
              continue;
            }

            // Create new record - the composite ID will be generated by @BeforeInsert
            console.log(
              `Creating new fagkode: ${item.fagkode} (${
                item.variantkode || "no variant"
              }) for ${item.eksamensperiode}`
            );

            fagkodeeksamen = fagkodeeksamensRepository.create({
              Fagkode: item.fagkode,
              Variantkode: item.variantkode || undefined,
              Eksamensperiode: item.eksamensperiode,
              Fagnavn: item.fagnavn,
              Varighet: item.varighet || undefined,
              Eksamensdato: item.eksamensdato
                ? new Date(item.eksamensdato)
                : undefined,
              Eksamenstid: item.eksamenstid || undefined,
              ErTestFagkode: item.erTestFagkode || false,
            });

            insertedPrimaryKeys.add(expectedPrimaryKey);
            fagkoderToInsert.push(fagkodeeksamen);
          }

          // Store eksamensdeler for later processing
          if (item.eksamensdeler && item.eksamensdeler.length > 0) {
            eksamensdelerToProcess.push({
              fagkodeeksamen,
              eksamensdeler: item.eksamensdeler,
            });
          }

          successCount++;
        } catch (error) {
          console.error(`Error processing fagkode ${item.fagkode}:`, {
            error: error instanceof Error ? error.message : String(error),
            fagkode: item.fagkode,
            eksamensperiode: item.eksamensperiode,
          });
          errorCount++;
        }
      }

      // Perform bulk operations with batching to avoid SQL Server parameter limit
      const batchPromises: Promise<void>[] = [];

      // Bulk insert new fagkoder in batches
      if (fagkoderToInsert.length > 0) {
        batchPromises.push(
          this.saveBatched(
            fagkodeeksamensRepository,
            fagkoderToInsert,
            "insert"
          )
        );
      }

      // Bulk update existing fagkoder in batches
      if (fagkoderToUpdate.length > 0) {
        batchPromises.push(
          this.saveBatched(
            fagkodeeksamensRepository,
            fagkoderToUpdate,
            "update"
          )
        );
      }

      // Wait for all batch operations to complete
      await Promise.all(batchPromises);

      // Process eksamensdeler efficiently after all fagkoder are saved
      await this.processEksamensdelerBulk(queryRunner, eksamensdelerToProcess);
      ("");

      await queryRunner.commitTransaction();
      console.log(
        `Database operation completed. Success: ${successCount}, Errors: ${errorCount}`
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error in bulk save operation:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    } finally {
      await queryRunner.release();
    }

    return { success: successCount, errors: errorCount };
  }

  /**
   * Optimalisert behandling av eksamensdeler med bulk operasjoner
   */
  private async processEksamensdelerBulk(
    queryRunner: any,
    eksamensdelerToProcess: Array<{
      fagkodeeksamen: Fagkodeeksamen;
      eksamensdeler: any[];
    }>
  ): Promise<void> {
    if (eksamensdelerToProcess.length === 0) return;

    const eksamensdelRepository =
      queryRunner.manager.getRepository(Eksamensdel);

    // Collect all fagkodeeksamen IDs for bulk fetch
    const fagkodeeksamensIds = eksamensdelerToProcess.map(
      (item) => item.fagkodeeksamen.FagkodeeksamensID
    );

    // Bulk fetch existing eksamensdeler
    const existingEksamensdeler = await eksamensdelRepository
      .createQueryBuilder("e")
      .where("e.FagkodeeksamensID IN (:...ids)", { ids: fagkodeeksamensIds })
      .getMany();

    // Create lookup map
    const existingEksamensdelerMap = new Map<string, Eksamensdel>();
    existingEksamensdeler.forEach((e: Eksamensdel) => {
      const key = `${e.FagkodeeksamensID}_${e.EksamensdelType}`;
      existingEksamensdelerMap.set(key, e);
    });

    const eksamensdelerToInsert: Eksamensdel[] = [];
    const eksamensdelerToUpdate: Eksamensdel[] = [];

    // Process all eksamensdeler
    for (const item of eksamensdelerToProcess) {
      for (const delData of item.eksamensdeler) {
        const lookupKey = `${item.fagkodeeksamen.FagkodeeksamensID}_${delData.eksamensdelType}`;
        const eksisterendeDel = existingEksamensdelerMap.get(lookupKey);

        let eksamensdel: Eksamensdel;

        if (eksisterendeDel) {
          // Update existing
          eksamensdel = eksisterendeDel;
          eksamensdel.GjennomforingStart = delData.gjennomforingStart
            ? new Date(delData.gjennomforingStart)
            : null;
          eksamensdel.GjennomforingStopp = delData.gjennomforingStopp
            ? new Date(delData.gjennomforingStopp)
            : null;
          eksamensdel.Gjennomforingsystem = delData.gjennomforingsystem || null;
          eksamensdel.Eksamensveiledning = delData.eksamensveiledning || null;
          eksamensdel.ErPlagiatkontroll = delData.erPlagiatkontroll || false;
          eksamensdelerToUpdate.push(eksamensdel);
        } else {
          // Create new - the GUID ID will be generated by @BeforeInsert
          eksamensdel = eksamensdelRepository.create({
            FagkodeeksamensID: item.fagkodeeksamen.FagkodeeksamensID,
            EksamensdelType: delData.eksamensdelType,
            GjennomforingStart: delData.gjennomforingStart
              ? new Date(delData.gjennomforingStart)
              : undefined,
            GjennomforingStopp: delData.gjennomforingStopp
              ? new Date(delData.gjennomforingStopp)
              : undefined,
            Gjennomforingsystem: delData.gjennomforingsystem || undefined,
            Eksamensveiledning: delData.eksamensveiledning || undefined,
            ErPlagiatkontroll: delData.erPlagiatkontroll || false,
          });
          eksamensdelerToInsert.push(eksamensdel);
        }
      }
    }

    // Bulk operations for eksamensdeler with batching
    if (eksamensdelerToInsert.length > 0) {
      await this.saveBatched(
        eksamensdelRepository,
        eksamensdelerToInsert,
        "insert"
      );
    }

    if (eksamensdelerToUpdate.length > 0) {
      await this.saveBatched(
        eksamensdelRepository,
        eksamensdelerToUpdate,
        "update"
      );
    }

    console.log(
      `Eksamensdeler processed: ${eksamensdelerToInsert.length} inserts, ${eksamensdelerToUpdate.length} updates`
    );
  }

  /**
   * Batch save method to avoid SQL Server parameter limit (2100 parameters)
   */
  private async saveBatched<T>(
    repository: any,
    entities: T[],
    operation: "insert" | "update",
    batchSize: number = 100
  ): Promise<void> {
    if (entities.length === 0) return;

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      try {
        await repository.save(batch);
      } catch (error) {
        console.error(`Error in batch ${Math.floor(i / batchSize) + 1}:`, {
          error: error instanceof Error ? error.message : String(error),
          batchSize: batch.length,
          operation,
        });
        throw error;
      }
    }
  }

  /**
   * Importerer eksamensplandata fra API og lagrer til database
   */
  async importEksamensplan(
    eksamensperiode: string
  ): Promise<{ success: number; errors: number; message: string }> {
    const startTime = Date.now();

    try {
      console.log(`Starting import for eksamensperiode: ${eksamensperiode}`);

      // Fetch data from external API
      const eksamensplanData = await this.fetchEksamensplanData(
        eksamensperiode
      );

      if (!eksamensplanData || eksamensplanData.length === 0) {
        console.warn(`No data found for eksamensperiode: ${eksamensperiode}`);
        return {
          success: 0,
          errors: 0,
          message: `Ingen data funnet for eksamensperiode ${eksamensperiode}`,
        };
      }

      console.log(`Processing ${eksamensplanData.length} records...`);

      // Save data to database
      const result = await this.saveEksamensplanData(eksamensplanData);

      const duration = Date.now() - startTime;
      const successMessage = `Import fullført på ${duration}ms. ${result.success} vellykket, ${result.errors} feil.`;

      return {
        ...result,
        message: successMessage,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = `Import feilet etter ${duration}ms: ${
        error instanceof Error ? error.message : "Ukjent feil"
      }`;

      console.error("Error during import:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        eksamensperiode,
        duration,
      });

      throw new Error(errorMessage);
    }
  }
}
