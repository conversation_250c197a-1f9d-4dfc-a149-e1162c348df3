"use client";

import React from "react";
import { getSession, signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import <PERSON><PERSON> from "js-cookie";

interface SessionWrapperProps {
  children: React.ReactNode;
}

export function SessionWrapper({ children }: SessionWrapperProps) {
  const router = useRouter();

  useSession({
    required: true,
    async onUnauthenticated() {
      await handleLogout();
    },
  });

  const handleLogout = async () => {
    const session: any = await getSession();
    const queryParams = {
      idtoken: session?.user?.idToken,
    };
    const queryString = new URLSearchParams(queryParams).toString();
    const response = await fetch(
      `${window.location.origin}/api/federatedlogout?${queryString}`
    );
    const data = await response.json();
    if (response.ok) {
      Cookie.remove("next-auth.csrf-token");
      sessionStorage.clear();
      localStorage.removeItem("selectedRole");
      await signOut({ redirect: false });
      window.location.href = data.url;
    }
  };

  return <>{children}</>;
}