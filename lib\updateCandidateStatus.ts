"use server";

import dayjs from "dayjs";

import utc from "dayjs/plugin/utc";
import { getAppInsightsServer } from "./appInsightsServer";
import { getAccessToken } from "./getAccessToken";
import { ICandidateStatus } from "@/interface/ICandidateStatus";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const updateStatusApiUrl = `${PgsaApiUrl}/api/Monitor/status`;
const telemetryClient = getAppInsightsServer();

const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

dayjs.extend(utc);

export async function updateCandidateStatus(
  currentStatus: number,
  newStatus: number,
  testPartID: number,
  candidateNumber: string,
  examGroupCode: string,
  username: string,
  userId: string,
  ipAddress: string
): Promise<Response> {
  let updateBody,
    baseUpdateBody: ICandidateStatus | null = null;
  let response: Response | null = null;

  try {
    const pgsaAccessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    baseUpdateBody = {
      CandidateNumber: candidateNumber,
      ExamGroupCode: examGroupCode,
      NewCandidateStatus: newStatus,
      CurrentCandidateStatus: currentStatus,
      TestPartId: testPartID,
      Username: username,
      Timestamp: dayjs().utc().toISOString(),
      UserId: userId,
    };

    updateBody = ipAddress?.trim()
      ? { ...baseUpdateBody, IpAddress: ipAddress }
      : baseUpdateBody;

    response = await fetch(updateStatusApiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${pgsaAccessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateBody),
      cache: "no-store",
    });

    return response;
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        component: "updateCandidateStatus",
        action: "UpdateStatus",
        updateBody: JSON.stringify(updateBody),
        responseStatus: response?.status,
        responseStatusText: response?.statusText,
      },
    });
    console.error("Error in updateCandidateStatus:", error);
    throw error;
  } finally {
  }
}
