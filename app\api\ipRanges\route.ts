// app/api/ipRanges/route.ts
import { addToSet, getSetMembers, removeSet } from "@/app/lib/redisHelper";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";

// Helper function to generate Redis key
const getRedisKey = (schoolId: string) => `IpRanges:${schoolId}`;

// POST route for saving IP ranges
export async function POST(req: Request) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const { schoolId, ranges } = await req.json();

    // Validate school ID
    if (!schoolId || typeof schoolId !== "string") {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid school ID",
        },
        { status: 400 }
      );
    }

    if (!Array.isArray(ranges) || ranges.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid input: must be a non-empty array of IP ranges",
        },
        { status: 400 }
      );
    }

    const validRanges = ranges
      .filter((range) => range && range.trim())
      .map((range) => range.trim());

    if (validRanges.length === 0) {
      return NextResponse.json(
        { success: false, message: "No valid IP ranges provided" },
        { status: 400 }
      );
    }

    const key = getRedisKey(schoolId);

    // Slett eksisterende set først
    await removeSet(key);

    // Legg til nye ranges
    const addedCount = await addToSet(key, validRanges);

    return NextResponse.json({
      success: true,
      message: `Successfully saved ${addedCount} IP ranges`,
      key,
      addedRanges: addedCount,
    });
  } catch (error) {
    console.error("Error saving IP ranges:", error);
    return NextResponse.json(
      { success: false, message: "Failed to save IP ranges" },
      { status: 500 }
    );
  }
}

// GET route for retrieving IP ranges
export async function GET(req: Request) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // Get schoolId from URL parameters
    const { searchParams } = new URL(req.url);
    const schoolId = searchParams.get("schoolId");

    if (!schoolId) {
      return NextResponse.json(
        {
          success: false,
          message: "School ID is required",
        },
        { status: 400 }
      );
    }

    const key = getRedisKey(schoolId);

    // Fetch ranges from Redis
    const ranges = await getSetMembers(key);

    return NextResponse.json({
      success: true,
      ranges: ranges || [],
    });
  } catch (error) {
    console.error("Error fetching IP ranges:", error);
    return NextResponse.json(
      { success: false, message: "Failed to fetch IP ranges" },
      { status: 500 }
    );
  }
}
