// lib/serviceBus.ts
import {
  ServiceBusClient,
  ServiceBusSender,
  ServiceBusReceiver,
  ServiceBusClientOptions,
  RetryMode,
} from "@azure/service-bus";
import { DefaultAzureCredential } from "@azure/identity";

const connectionString = process.env.SERVICE_BUS_CONNECTION_STRING;
const serviceBusNamespace = process.env.SERVICE_BUS_NAMESPACE; // e.g., "myservicebus.servicebus.windows.net"
const queueName = process.env.SERVICE_BUS_QUEUE_NAME!;

// Connection pooling and caching
let sbClient: ServiceBusClient | null = null;
const senderCache = new Map<string, ServiceBusSender>();
const receiverCache = new Map<string, ServiceBusReceiver>();

// Configuration for high traffic
const CLIENT_OPTIONS: ServiceBusClientOptions = {
  retryOptions: {
    maxRetries: 5,
    maxRetryDelayInMs: 30000,
    mode: RetryMode.Exponential,
  },
  // Using WebSocket transport with compression and large payload support
  webSocketOptions: {
    webSocketConstructorOptions: {
      perMessageDeflate: true,
      maxPayload: 100 * 1024 * 1024, // 100MB max payload
    },
  },
};

// Lazy initialization with error handling
export function getServiceBusClient(): ServiceBusClient {
  if (!sbClient) {
    try {
      if (connectionString) {
        sbClient = new ServiceBusClient(connectionString, CLIENT_OPTIONS);
      } else if (serviceBusNamespace) {
        const credential = new DefaultAzureCredential({
          managedIdentityClientId: process.env.AZURE_CLIENT_ID, // Optional: specify client ID
        });
        sbClient = new ServiceBusClient(
          serviceBusNamespace,
          credential,
          CLIENT_OPTIONS
        );
      } else {
        throw new Error(
          "Either SERVICE_BUS_CONNECTION_STRING (for localhost) or SERVICE_BUS_NAMESPACE (for Azure) must be provided"
        );
      }
    } catch (error) {
      console.error("Failed to initialize Service Bus client:", error);
      throw error;
    }
  }
  return sbClient;
}

// Cached sender with connection reuse
export function getSender(queueNameParam?: string): ServiceBusSender {
  const targetQueue = queueNameParam || queueName;

  if (!senderCache.has(targetQueue)) {
    const client = getServiceBusClient();
    const sender = client.createSender(targetQueue);
    senderCache.set(targetQueue, sender);
  }

  return senderCache.get(targetQueue)!;
}

// Cached receiver with connection reuse
export function getReceiver(
  queueNameParam?: string,
  receiveMode: "peekLock" | "receiveAndDelete" = "peekLock"
): ServiceBusReceiver {
  const targetQueue = queueNameParam || queueName;
  const cacheKey = `${targetQueue}_${receiveMode}`;

  if (!receiverCache.has(cacheKey)) {
    const client = getServiceBusClient();
    const receiver = client.createReceiver(targetQueue, { receiveMode });
    receiverCache.set(cacheKey, receiver);
  }

  return receiverCache.get(cacheKey)!;
}

// Batch sending for high throughput with improved error handling
export async function sendMessagesBatch(
  messages: ServiceBusMessage[],
  batchSize: number = 100,
  queueNameParam?: string
): Promise<Array<{ success: boolean; count: number; error?: any }>> {
  const targetQueue = queueNameParam || queueName;
  const batches = [];

  // Split messages into batches
  for (let i = 0; i < messages.length; i += batchSize) {
    batches.push(messages.slice(i, i + batchSize));
  }

  const results = await Promise.allSettled(
    batches.map(async (batch, batchIndex) => {
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        try {
          const sender = getSender(targetQueue);
          const messageBatch = await sender.createMessageBatch();

          for (const message of batch) {
            if (!messageBatch.tryAddMessage(message)) {
              // If message doesn't fit, send current batch and create new one
              if (messageBatch.count > 0) {
                await sender.sendMessages(messageBatch);
              }
              const newBatch = await sender.createMessageBatch();
              if (!newBatch.tryAddMessage(message)) {
                throw new Error(
                  `Message too large for batch: ${JSON.stringify(
                    message
                  ).substring(0, 200)}...`
                );
              }
              await sender.sendMessages(newBatch);
            }
          }

          if (messageBatch.count > 0) {
            await sender.sendMessages(messageBatch);
          }

          return { success: true, count: batch.length };
        } catch (error) {
          attempts++;
          console.error(`Batch ${batchIndex + 1} attempt ${attempts} failed:`, {
            error: error instanceof Error ? error.message : String(error),
            batchSize: batch.length,
          });

          // Clear cache on connection errors and retry
          if (isConnectionError(error) && attempts < maxAttempts) {
            senderCache.delete(targetQueue);
            await delay(Math.pow(2, attempts) * 1000); // Exponential backoff
          } else if (attempts >= maxAttempts) {
            return { success: false, error, count: batch.length };
          }
        }
      }

      return {
        success: false,
        error: new Error("Max attempts reached"),
        count: batch.length,
      };
    })
  );

  // Convert PromiseSettledResult to our return type
  return results.map((result) =>
    result.status === "fulfilled"
      ? result.value
      : { success: false, error: result.reason, count: 0 }
  );
}

// Health check function
export async function healthCheck(): Promise<{
  healthy: boolean;
  details: any;
}> {
  try {
    // Basic health check - if client initializes, we consider it healthy
    getServiceBusClient();

    return {
      healthy: true,
      details: {
        clientInitialized: !!sbClient,
        sendersActive: senderCache.size,
        receiversActive: receiverCache.size,
        queueName: queueName,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      healthy: false,
      details: {
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
    };
  }
}

// Graceful shutdown with cleanup
export async function gracefulShutdown(): Promise<void> {
  try {
    // Close all senders
    const senderPromises = Array.from(senderCache.values()).map(
      async (sender) => {
        try {
          await sender.close();
        } catch (error) {
          console.error("Error closing sender:", error);
        }
      }
    );

    // Close all receivers
    const receiverPromises = Array.from(receiverCache.values()).map(
      async (receiver) => {
        try {
          await receiver.close();
        } catch (error) {
          console.error("Error closing receiver:", error);
        }
      }
    );

    await Promise.all([...senderPromises, ...receiverPromises]);

    // Clear caches
    senderCache.clear();
    receiverCache.clear();

    // Close client
    if (sbClient) {
      await sbClient.close();
      sbClient = null;
    }
  } catch (error) {
    console.error("Error during graceful shutdown:", error);
    throw error;
  }
}

// Utility function for circuit breaker pattern
class CircuitBreaker {
  private failures = 0;
  private lastFailTime = 0;
  private state: "CLOSED" | "OPEN" | "HALF_OPEN" = "CLOSED";

  constructor(
    private readonly failureThreshold = 5,
    private readonly timeout = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === "OPEN") {
      if (Date.now() - this.lastFailTime > this.timeout) {
        this.state = "HALF_OPEN";
      } else {
        throw new Error("Circuit breaker is OPEN");
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = "CLOSED";
  }

  private onFailure() {
    this.failures++;
    this.lastFailTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = "OPEN";
    }
  }
}

const circuitBreaker = new CircuitBreaker();

// Enhanced send with circuit breaker and error handling
export async function sendMessageWithCircuitBreaker(
  message: ServiceBusMessage,
  queueNameParam?: string
): Promise<void> {
  return circuitBreaker.execute(async () => {
    // Use the robust sendMessage API which already has retry logic
    await sendMessage(message, queueNameParam);
  });
}

// Type definitions for better type safety
export interface ServiceBusMessage {
  body: string;
  contentType?: string;
  messageId?: string;
  correlationId?: string;
  sessionId?: string;
  timeToLive?: number;
  subject?: string;
  applicationProperties?: Record<string, any>;
}

// Send message with automatic retry and cache invalidation
export async function sendMessage(
  message: ServiceBusMessage,
  queueNameParam?: string
): Promise<void> {
  const targetQueue = queueNameParam || queueName;
  let attempts = 0;
  const maxAttempts = 3;

  while (attempts < maxAttempts) {
    try {
      const sender = getSender(targetQueue);
      await sender.sendMessages(message);

      return; // Success
    } catch (error) {
      attempts++;
      console.error(
        `Send attempt ${attempts} failed for queue ${targetQueue}:`,
        {
          error: error instanceof Error ? error.message : String(error),
          attempt: attempts,
          maxAttempts,
        }
      );

      // Clear cache on connection errors
      if (isConnectionError(error)) {
        senderCache.delete(targetQueue);
        if (attempts < maxAttempts) {
          await delay(Math.pow(2, attempts) * 1000); // Exponential backoff
        }
      } else {
        throw error; // Non-connection errors shouldn't retry
      }
    }
  }

  throw new Error(
    `Failed to send message to queue ${targetQueue} after ${maxAttempts} attempts`
  );
}

// Helper function to identify connection errors
function isConnectionError(error: any): boolean {
  if (!error) return false;
  const errorMessage = error.message?.toLowerCase() || "";
  const errorCode = error.code || "";

  return (
    errorMessage.includes("connection") ||
    errorMessage.includes("socket") ||
    errorMessage.includes("timeout") ||
    errorCode === "ECONNRESET" ||
    errorCode === "ENOTFOUND"
  );
}

// Helper delay function
function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Resource cleanup for graceful shutdown
export async function cleanup(): Promise<void> {
  try {
    // Close all senders
    const senderPromises = Array.from(senderCache.values()).map(
      async (sender) => {
        try {
          await sender.close();
        } catch (error) {
          console.error("Error closing sender:", error);
        }
      }
    );
    await Promise.allSettled(senderPromises);
    senderCache.clear();

    // Close all receivers
    const receiverPromises = Array.from(receiverCache.values()).map(
      async (receiver) => {
        try {
          await receiver.close();
        } catch (error) {
          console.error("Error closing receiver:", error);
        }
      }
    );
    await Promise.allSettled(receiverPromises);
    receiverCache.clear();

    // Close client
    if (sbClient) {
      await sbClient.close();
      sbClient = null;
    }
  } catch (error) {
    console.error("Error during ServiceBus cleanup:", error);
    throw error;
  }
}

// Graceful shutdown handlers
let shutdownHandlersRegistered = false;

function registerShutdownHandlers(): void {
  if (shutdownHandlersRegistered) return;

  const gracefulShutdown = async (signal: string) => {
    try {
      await cleanup();
      process.exit(0);
    } catch (error) {
      console.error("Error during graceful shutdown:", error);
      process.exit(1);
    }
  };

  process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
  process.on("SIGINT", () => gracefulShutdown("SIGINT"));
  process.on("SIGUSR2", () => gracefulShutdown("SIGUSR2")); // For nodemon

  shutdownHandlersRegistered = true;
}

// Process exit handler for cleanup
if (typeof process !== "undefined") {
  registerShutdownHandlers();
}

// Export all functions
export default {
  getServiceBusClient,
  getSender,
  getReceiver,
  sendMessage,
  sendMessagesBatch,
  sendMessageWithCircuitBreaker,
  healthCheck,
  cleanup,
};
