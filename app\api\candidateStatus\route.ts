import { NextResponse } from "next/server";
import { getAppInsightsServer } from "@/lib/appInsightsServer";
import { getAccessToken } from "@/lib/getAccessToken";
import { getServerSession } from "next-auth";
import { ISession } from "@/interface/ISession";
import { authOptions } from "../auth/authOptions";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET(request: Request) {
  const session: ISession | null = await getServerSession(authOptions);
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const date = searchParams.get("date");

  if (!date) {
    return NextResponse.json(
      { error: "Date parameter is required" },
      { status: 400 }
    );
  }

  try {
    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    if (!accessToken) {
      throw new Error("Failed to obtain access token");
    }

    const apiUrl = `${PgsaApiUrl}/api/Reports/CandidatesPerStatus/${date}`;

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      telemetryClient?.trackException({
        exception: new Error("Failed to fetch candidate status"),
        properties: {
          action: "fetchCandidateStatus",
          statuscode: response.status,
          response: await response.text(),
        },
      });

      throw new Error("Failed to fetch candidate status");
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching candidate status:", error);
    return NextResponse.json(
      { error: "Failed to fetch candidate status" },
      { status: 500 }
    );
  }
}
