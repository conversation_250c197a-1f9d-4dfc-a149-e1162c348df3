"use client";

import React from "react";
import { Clock, Globe, Monitor, Laptop, Smartphone } from "lucide-react";
import dayjs from "dayjs";
import "dayjs/locale/nb";

dayjs.locale("nb");

interface SessionDetailsProps {
  sessions: ICandidateSessionMetaData[];
}

const SessionDetails: React.FC<SessionDetailsProps> = ({ sessions }) => {
  const formatDateTime = (dateStr: string) => {
    return dayjs(dateStr).format("DD.MM.YYYY HH:mm");
  };

  const InfoCard = ({
    icon: Icon,
    title,
    value,
  }: {
    icon: any;
    title: string;
    value: string;
  }) => (
    <div className="p-2 bg-white rounded-lg border border-gray-200 flex items-start space-x-2">
      <div className="flex-shrink-0">
        <Icon className="h-5 w-5 text-gray-500" />
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <p className="mt-1 text-sm text-gray-900">{value}</p>
      </div>
    </div>
  );

  return (
    <div className="space-y-4 p-2">
      {sessions.map((session, index) => (
        <div
          key={`${session.SessionId}-${index}`}
          className="bg-gray-50 px-6 py-4 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-100 transition-colors duration-200"
        >
          <div className="flex items-center space-x-2 mb-4 pb-3 border-b border-gray-200">
            <Globe className="h-5 w-5 text-gray-500" />
            <div className="font-semibold">IP-adresse: {session.IpAddress}</div>
            <div className="text-sm text-gray-500 ml-auto">
              Økt #{session.Entry}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3">
            <InfoCard
              icon={Clock}
              title="Starttidspunkt"
              value={formatDateTime(session.StartTime)}
            />
            <InfoCard
              icon={Clock}
              title="Sist aktiv"
              value={formatDateTime(session.LastActive)}
            />
            <InfoCard
              icon={Monitor}
              title="Nettleser"
              value={session.Browser || "-"}
            />
            <InfoCard
              icon={Laptop}
              title="Operativsystem"
              value={session.OS || "-"}
            />
            <InfoCard
              icon={Smartphone}
              title="Enhet"
              value={session.Device || "-"}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default SessionDetails;
