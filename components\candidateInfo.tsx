"use client";

import { IActivityLogColumnDef } from "@/interface/IActivityLogColumnDef";
import dayjs from "dayjs";

interface CandidateInfoProps {
  activityLogData: IActivityLogColumnDef[];
  searchTerm: string;
}

const getUniqueDate = (data: IActivityLogColumnDef[]): string | null => {
  if (data.length === 0) return null;
  const firstTimestamp = data[0].Timestamp;
  return dayjs(firstTimestamp).format("DD.MM.YYYY");
};

export default function CandidateInfo({
  activityLogData,
  searchTerm,
}: CandidateInfoProps) {
  const date = getUniqueDate(activityLogData);

  return (
    <div className="w-fit bg-gray-100 p-4 rounded-md mb-4">
      <div className="flex flex-col text-sm gap-1 sm:gap-0">
        <div className="text-lg text-semibold mb-1">
          {activityLogData[0].KandidatFornavn}{" "}
          {activityLogData[0].KandidatEtternavn}
        </div>
        <div className="flex flex-col sm:flex-row sm:items-center gap-0 sm:gap-2">
          <span>Kandidatnummer:</span>
          <span className="font-semibold">{activityLogData[0].KandidatNr}</span>
        </div>
        <div className="flex flex-col sm:flex-row sm:items-center gap-0 sm:gap-2">
          <span>Påmeldingsid:</span>
          <span className="font-semibold">
            {activityLogData[0].KandidatpaameldingID}
          </span>
        </div>
        <div className="flex flex-col sm:flex-row sm:items-center gap-0 sm:gap-2">
          <span>Dato:</span>
          <span className="font-semibold">{date}</span>
        </div>
      </div>
    </div>
  );
}
