"use client";

import { useState, useEffect, useMemo } from "react";
import { ChevronsUpDown, Check, X, ChevronDown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useRouter, useSearchParams } from "next/navigation";
import { useCandidate } from "@/context/CandidateMonitorContext";

interface GroupData {
  groupName: string;
  groupCode: string;
}

interface Props {
  handleFilterChange: (key: string, value: string) => void;
}

const GroupPopover: React.FC<Props> = ({ handleFilterChange }) => {
  const [openGroup, setOpenGroup] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const groupFilterLabel = "groupCodes";

  // Use the CandidateContext to get examPaper
  const { examPaper } = useCandidate();

  // Extract all groups from examPaper
  const allGroups: GroupData[] = useMemo(() => {
    if (!examPaper) return [];
    return examPaper.map((paper) => ({
      groupCode: paper.groupCode,
      groupName: paper.groupName,
    }));
  }, [examPaper]);

  // Memoize unique groups to avoid re-computation on every render
  const uniqueGroups = useMemo(() => {
    const unique = new Map<string, GroupData>();
    allGroups.forEach((group) => {
      unique.set(group.groupCode, group);
    });
    return Array.from(unique.values());
  }, [allGroups]);

  // Get selected groups from URL
  const getSelectedGroups = (): GroupData[] => {
    const groupCodes = searchParams.get(groupFilterLabel)?.split(",") || [];
    return groupCodes
      .map((code) => uniqueGroups.find((g) => g.groupCode === code))
      .filter((group): group is GroupData => group !== undefined);
  };

  // Update URL with selected groups
  const updateQueryString = (groups: GroupData[]) => {
    const params = new URLSearchParams(searchParams.toString());

    if (groups.length > 0) {
      const groupCodes = groups.map((g) => g.groupCode).join(",");
      params.set(groupFilterLabel, groupCodes);
    } else {
      params.delete(groupFilterLabel);
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleCheckboxChange = (data: GroupData, checked: boolean) => {
    const currentGroups = getSelectedGroups();
    const newGroups = checked
      ? [...currentGroups, data]
      : currentGroups.filter((g) => g.groupCode !== data.groupCode);

    updateQueryString(newGroups);
    handleFilterChange(groupFilterLabel, checked ? data.groupCode : "");
  };

  const clearAllGroups = () => {
    updateQueryString([]);
    handleFilterChange(groupFilterLabel, "");
    setOpenGroup(false);
  };

  const filteredGroups = uniqueGroups.filter((group) =>
    group.groupName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedGroups = getSelectedGroups();

  return (
    <div className="w-full">
      <Popover open={openGroup} onOpenChange={setOpenGroup}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-between transition-colors duration-200 ${
              openGroup ? "bg-udirLightAzure-100" : ""
            }`}
            id="gruppe"
            aria-controls="gruppe"
          >
            <div className="flex items-center truncate">
              {selectedGroups.length > 0 ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Gruppe</span>
                  <Badge variant="secondary" className="bg-mint">
                    {selectedGroups.length}
                  </Badge>
                </div>
              ) : (
                "Gruppe"
              )}
            </div>
            <ChevronDown
              role="img"
              aria-label="Chevron"
              className="ml-2 h-5 w-5 shrink-0 opacity-70 transition-transform duration-200"
              style={{
                transform: openGroup ? "rotate(180deg)" : "rotate(0deg)",
              }}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0 shadow-lg"
          align="start"
          sideOffset={4}
          style={{
            width:
              typeof window !== "undefined" && window.innerWidth < 768
                ? "var(--radix-popover-trigger-width)"
                : "auto",
          }}
        >
          <Command
            shouldFilter={false}
            className="rounded-sm border border-gray-200 bg-udirLightAzure-100"
          >
            <div className="flex items-center border-b border-udirLightAzure-300 gap-2 text-gray-600">
              <CommandInput
                placeholder="Søk gruppe..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="h-9 flex-1"
              />
              {selectedGroups.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllGroups}
                  className="h-8 px-2 text-sm hover:text-gray-900"
                >
                  <X className="h-4 w-4 mr-1" />
                  Fjern alle
                </Button>
              )}
            </div>
            <CommandList className="max-h-64 overflow-auto">
              <CommandEmpty className="py-6 text-center text-sm text-gray-500">
                Ingen resultater funnet
              </CommandEmpty>
              <CommandGroup className="p-1">
                {filteredGroups.map((data) => (
                  <CommandItem
                    key={data.groupCode}
                    value={data.groupCode}
                    onSelect={() => {
                      const isSelected = selectedGroups.some(
                        (g) => g.groupCode === data.groupCode
                      );
                      handleCheckboxChange(data, !isSelected);
                    }}
                    className="flex items-center gap-2 px-2 py-1.5 hover:bg-udirLightAzure-300 rounded-md cursor-pointer transition-colors duration-150"
                  >
                    <div className="flex items-center flex-1 gap-2">
                      <Checkbox
                        checked={selectedGroups.some(
                          (g) => g.groupCode === data.groupCode
                        )}
                        onCheckedChange={(checked) => {
                          handleCheckboxChange(data, checked as boolean);
                        }}
                        className="border-udirLightAzure-500"
                        onClick={(e) => e.stopPropagation()}
                      />
                      <div className="flex flex-col">
                        <span className="text-sm">{data.groupName}</span>
                      </div>
                    </div>
                    {selectedGroups.some(
                      (g) => g.groupCode === data.groupCode
                    ) && <Check className="h-4 w-4 text-mint flex-shrink-0" />}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default GroupPopover;
