import { But<PERSON> } from "@/components/ui/button";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { useAccessRequests } from "@/hooks/accessRequestProvider";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { useCandidate } from "@/context/CandidateMonitorContext";
import { useToast } from "./ui/use-toast";
import { IRoleObject } from "@/interface/IRoleObject";
import { ActivityLogger } from "@/lib/services/ActivityLogger";
import { OperationEnum } from "@/enums/OperationEnum";

interface GrantAccessButtonProps {
  candidate: ICandidateMonitor;
  selectedRole?: IRoleObject;
}

export const GrantAccessButton = ({
  candidate,
  selectedRole,
}: GrantAccessButtonProps) => {
  // Hent isSubmitting fra useAccessRequests
  const { grantAccessRequest, accessRequests, isSubmitting } =
    useAccessRequests();
  const {
    removeBlockedUser,
    blockedUsers,
    refreshRedisData,
    authorizedCandidates,
  } = useCandidate();
  const { toast } = useToast();

  // Status beregning
  const status =
    candidate.deliveryStatusPart2 !== -1
      ? candidate.deliveryStatusPart2
      : candidate.deliveryStatusPart1;

  // Statuskontroller
  const hasNeverBeenAuthorized = [
    CandidateStatusEnum.IkkeInnlogget,
    CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
  ].includes(status);

  const isHidden =
    [
      CandidateStatusEnum.DokumentertFravaer,
      CandidateStatusEnum.IkkeDokumentertFravaer,
      CandidateStatusEnum.Levert,
      CandidateStatusEnum.LevertManuelt,
    ].includes(status) || blockedUsers[candidate.candidateNumber];

  // Sjekk om kandidaten allerede har tilgang
  const isAuthorized = authorizedCandidates[candidate.candidateNumber];

  // Finn tilgangsforespørsel
  const accessRequest = accessRequests.find(
    (req) => req.candidateNumber === candidate.candidateNumber
  );

  // Skjul knappen hvis nødvendig
  if (!accessRequest || isHidden || isAuthorized) {
    return null;
  }

  // Håndter tilgangsforespørsel
  const handleGrantAccess = async (shouldGrant: boolean) => {
    if (
      !candidate.candidateNumber ||
      !candidate.groupCode ||
      !candidate.userId
    ) {
      console.warn("Missing required parameters for granting access");
      return;
    }

    try {
      if (shouldGrant) {
        await fetch("/api/deauthorizeUserObjects", {
          method: "POST",
          headers: { "Content-Type": "application/json" }, // Legg til header
          body: JSON.stringify({
            candidateNumber: candidate.candidateNumber,
            excludeSessionId: accessRequest.id, // Exclude the requesting session
          }),
        });
      }

      const success = await grantAccessRequest(
        {
          candidateNumber: candidate.candidateNumber,
          examGroupCode: candidate.groupCode,
          userId: candidate.userId,
          sessionId: accessRequest.id,
        },
        shouldGrant
      );

      if (success) {
        // Fjern fra blokkerte brukere hvis tilgang ble gitt
        if (shouldGrant) {
          await removeBlockedUser(candidate.userId, candidate.candidateNumber);
        }

        await refreshRedisData();

        // Log activity with error handling
        try {
          const logResult = await ActivityLogger.logActivity(
            shouldGrant
              ? OperationEnum.StatusAccessGranted
              : OperationEnum.StatusAccessRefused,
            {
              userId: candidate.userId,
              candidateName: candidate.candidateName || "",
              candidateNumber: candidate.candidateNumber || "",
              candidateRegistrationId: candidate.candidateRegistrationId || candidate.userId,
            },
            {
              roleName: `${selectedRole?.displayRoleName}`,
            }
          );

          if (!logResult.success) {
            console.warn('Audit log failed for access grant/refuse action:', logResult.error);
          }
        } catch (error) {
          console.error('Critical error in audit logging for access grant/refuse:', error);
          // Don't block the main operation, but log the error
        }

        // Vis bekreftelse
        toast({
          title: `${candidate.candidateName} (${candidate.candidateNumber})`,
          description: shouldGrant
            ? `Har fått tilgang`
            : `Tilgangsforespørsel avslått`,
          variant: "default",
          className: shouldGrant ? "bg-green-50 border-green-200" : "",
        });
      } else {
        console.error(
          "grantAccessRequest failed for",
          candidate.candidateNumber
        );
      }
    } catch (error) {
      toast({
        title: "Uventet feil",
        description: "En feil oppstod under prosesseringen. Prøv igjen.",
        variant: "destructive",
      });
    }
  };

  const buttonText = hasNeverBeenAuthorized
    ? "Gi tilgang"
    : "Gi tilgang på nytt";

  return (
    <div className="flex items-center">
      {/* Hovedknapp - bruk isSubmitting */}
      <Button
        variant="outline"
        onClick={() => handleGrantAccess(true)}
        className="h-8 rounded-r-none w-28 text-xs bg-udirGreen-300 border border-udirGreen-400 text-gray-700 hover:bg-[#A8D5BE] disabled:opacity-50"
        disabled={isSubmitting}
      >
        {buttonText}
      </Button>

      {/* Dropdown - bruk isSubmitting */}
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="h-8 rounded-l-none px-2 bg-udirGreen-300 border border-l-0 border-udirGreen-400 text-gray-700 hover:bg-[#A8D5BE] focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
            disabled={isSubmitting}
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="bg-udirGreen-100 rounded-sm"
        >
          <DropdownMenuItem
            className="text-xs"
            onClick={() => handleGrantAccess(false)}
            disabled={isSubmitting}
          >
            Ikke gi tilgang
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
