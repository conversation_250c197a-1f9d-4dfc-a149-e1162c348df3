import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";

const connectionString = process.env.CUSTOMCONNSTR_APPINSIGHTS_CONNECTIONSTRING;

export const dynamic = "force-dynamic";

export async function GET() {
  const session: ISession | null = await getServerSession(authOptions);

  if (!session || !session.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  return NextResponse.json({ connectionString: connectionString });
}
