"use client";
import React, { useEffect, useState } from "react";
import { ICandidate } from "@/interface/ICandidate";
import { DataTable } from "@/components/tables/kandidatmonitor/userActivity/data-table";
import columns from "@/components/tables/kandidatmonitor/userActivity/columns";
import { toast } from "@/components/ui/use-toast";
import { ActivityLogSkeleton } from "./ActivityLogSkeleton";

interface CandidateInfoModalProp {
  redisObject: IRedisObject[];
  candidate: ICandidate;
}

export default function ActivityLog({
  redisObject,
  candidate,
}: CandidateInfoModalProp) {
  const [activityLogState, setActivityLogState] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchActivityLog() {
      setIsLoading(true); // Ensure loading is true at the start of fetch
      const body = { userId: candidate.userId };
      try {
        const response = await fetch(
          `${window.location.origin}/api/getActivityLog`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(body),
          }
        );

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const result = await response.json();
        setActivityLogState(result);
      } catch (error) {
        console.log(error);
        toast({
          title: "Feil ved henting av aktivitetslogg",
          description: "Noe gikk galt, prøv igjen senere.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }
    fetchActivityLog();
  }, [candidate.userId]); // Added candidate.userId to dependency array

  if (isLoading) {
    return <ActivityLogSkeleton />;
  }

  return (
    <div className="mt-8">
      <h2 id="candidate-info-title" className="text-xl mb-5">
        Aktivitetslogg for {candidate.candidateName} (
        {candidate.candidateNumber})
      </h2>
      <div className="overflow-x-auto">
        <DataTable columns={columns} data={activityLogState} />
      </div>
      <div className="flex flex-col gap-8">
        <div role="region" aria-label="Kandidatens besvarelser"></div>
        <p></p>
      </div>
    </div>
  );
}
