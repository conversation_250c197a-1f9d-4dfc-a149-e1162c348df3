import React from "react";

export default function Home() {
  return (
    <>
      <div className="prose">
        <section>
          <h2 id="innledning">Innledning</h2>
          <p>
            Denne brukerveiledningen er laget for skoleadministratorer som skal
            laste ned tidligere eksamensbesvarelser i PGS. Merk at brukerstøtte
            går
            <a
              className="ml-1"
              href="https://www.udir.no/om-udir/tjenestevei-skole-barnehage/"
            >
              tjenestevei
            </a>
            . For eksempel skal skoleadministratorer kontakte sin skoleeier.
          </p>
          <p>
            Funksjonaliteten for nedlastning av tidligere eksamensoppgaver er
            tilgjengelig for alle som er skoleadministrator og for de som
            representerer skoleeier eller statsforvalteren. Dvs. brukere som har
            en av følgende roller i PAS-eksamen: Skoleadministrator,
            Skoleadministrator+, <PERSON>mmune, Fylkeskommune, Vigo
            eksamensadministrator og Statsforvalter.
          </p>
          <p>
            Det er flere brukerveiledninger for PGS. Disse finner du på siden
            for
            <a
              className="ml-1 mr-1"
              href="/brukerveiledning"
            >
              brukerveiledning
            </a>{" "}
            i PGS-admin. Merk at alle persondata som vises i skjermbilder i våre
            veiledninger er fiktive data.
          </p>

          <p>
            På udir.no finner du praktisk informasjon om organisering og
            gjennomføring av sentralt gitt skriftlig eksamen:
            <a
              className="ml-1"
              href="https://www.udir.no/eksamen-og-prover/eksamen/"
            >
              https://www.udir.no/eksamen-og-prover/eksamen/
            </a>
          </p>
        </section>
        <section>
          <h2 id="hurtigguide">Hurtigguide</h2>

          <ul className="list-decimal">
            <li>
              Innlogging: Logg inn i{" "}
              <a className="ml-1" href="https://pgs-admin.udir.no">
                pgs-admin.udir.no
              </a>
              , velg{" "}
              <a
                className="ml-1 mr-1"
                href="/nedlasting"
              >
                Nedlasting
              </a>{" "}
              i menyen.
            </li>
            <li>
              Skole velges automatisk basert på din rolle. Dersom du har flere
              roller kan du velge rolle øverst til høyre i skjermbildet.
            </li>
            <li>Angi eksamensperiode i feltet "periode".</li>
            <li>
              PGS viser da alle kandidatgrupper for den aktuelle perioden for
              din skole.
            </li>
            <li>
              Velg en av kandidatgruppene ved å klikke på gruppenavnet. Du
              kommer da til en side som viser kandidatene i gruppen.
            </li>
            <li>
              Klikk "Last ned besvarelser" for å laste ned alle besvarelsene til
              kandidatene i gruppen.
            </li>
          </ul>
          <p>
            Merk at eventuelle besvarelser som er sendes i posten og sendt i
            posten, og derfor ikke innskannet, ikke vil være tilgjengelig i PGS.
            Kandidatstatus for disse kandidatene skal være "Sendes i posten".
          </p>
        </section>
        <section className="font-normal">
          <h2 id="Naviger-til-nedlasting">
            Naviger til nedlasting av tidligere besvarelser
          </h2>
          <p>
            Logg inn i
            <a className="ml-1" href="https://pgs-admin.udir.no">
              pgs-admin.udir.no
            </a>
            , velg
            <a
              className="ml-1 mr-1"
              href="/nedlasting"
            >
              Nedlasting
            </a>
            i menyen.
          </p>
        </section>
        <section>
          <h2 id="Sok-opp-aktuelle-kandidatgrupper">
            Søk opp aktuelle kandidatgrupper
          </h2>
          <p>
            Skolen angis automatisk basert på din rolle og vises øverst på
            siden. Din aktive rolle vises øverst til høyre i skjermbildet.
            Dersom du har flere roller kan du velge rolle øverst til høyre i
            skjermbildet ved å klikke på pil ned.
          </p>
          <img src="/images/nedlasting.png" aria-label="nedlasting" />
          <p>
            Du må velge hvilken eksamensperiode du ønsker å laste ned
            eksamensbesvarelser for. Søket starter automatisk når du har valgt
            eksamensperiode. Søkeresultatet viser kandidatgruppene for perioden
            du valgte sammen med informasjon om fagkode, fagnavn og antall
            besvarelser i gruppen. Du kan filtere søkeresultatet på flere
            verdier, for eksempel på dato og fagkode. Dersom du ønsker å
            filtrere søkeresultatet klikker du på "Åpne filter". Merk at du kun
            får søkt opp kandidatgrupper som har gjennomført eksamen. Dvs. at du
            ikke får søkt opp kandidatgrupper som gjennomfører med dagens dato.
          </p>
          <img
            src="/images/nedlasting-innhold.png"
            aria-label="nedlasting tabell"
          />
          <p>
            Det er mulig å sortere søkeresultatet ved å klikke på en av
            kolonneoverskriftene.
          </p>
          <p>
            Når du har funnet den aktuelle kandidatgruppen klikker du på
            kandidatgruppenavnet. Du kommer så til siden for nedlastning av
            besvarelser for den valgte gruppen.
          </p>
        </section>
        <section>
          <h2 id="Last-ned-besvarelsene-for-kandidatgruppen">
            Last ned besvarelsene for kandidatgruppen
          </h2>
          <p>
            På siden for nedlastning av besvarelser for den valgte gruppen ser
            du kandidatgruppenavnet og fagkoden. Videre ser du kandidatene i
            gruppen samt deres kandidatnummer og kandidatstatus. For todelte
            eksamener vises status for både del 1 og del 2. Merk at statusene
            vises slik de var på eksamensdagen i PGS. Dersom en kandidatstatus
            er endret i PAS-eksamen etter eksamensdagen vil ikke denne endringen
            vises i PGS.
          </p>
          <p>
            Merk at eventuelle besvarelser som er sendes i posten og sendt i
            posten, og derfor ikke innskannet, ikke vil være tilgjengelig i PGS.
            Kandidatstatus for disse kandidatene skal da være "Sendes i posten".
          </p>

          <p>
            Ved å klikke på "Last ned besvarelsene" vil du laste ned komprimert
            (zippet) mappe med alle besvarelsene. Den komprimerte mappen vil
            være navngitt med kandidatgruppe, fagkode, fagnavn og
            eksamensperiode, og vil inneholde en mappe per kandidat. Den enkelte
            kandidatmappen vil igjen inneholde alle besvarelsesfilene til en
            kandidat. Merk at det er kandidatenes originalfiler du laster ned,
            slik de ble lastet opp. Disse vil derfor være i det originale
            filformatet og ikke i det konverterte pdf-formatet som sensor og
            kandidater ofte ser i PAS-eksamen og kandidat.udir.no. Unntaket er
            filnavnet som er standardisert for å gjøre filnavnene unike og
            enklere å identifisere. Filnavnet består av kandidatnummer,
            eksamenperiode, fagkode og en unik kode.
          </p>
          <img
            src="/images/nedlasting-kandidater.png"
            aria-label="kandidatliste"
          />
        </section>
        <section>
          <h2 id="Last-ned-besvarelsen-til-en-enkelt-kandidat">
            Last ned besvarelsen til én enkelt kandidat
          </h2>
          <p>
            Dersom du ønsker å laste ned besvarelsen til en spesifikk kandidat,
            kan du klikke på kandidatens navn. Du får da opp en modalside som
            viser kandidatens besvarelsesfiler med info om filnavn, størrelse,
            eksamensdel og tidspunktet som filen ble lastet opp. Det vises også
            om det var kandidaten eller en eksamensvakt som lastet opp filen.
            Ved å klikke "Last ned filene" laster du ned alle filene for den
            spesifikke kandidaten i en komprimert (zippet) mappe.
          </p>
          <p>
            Dersom du ønsker å kun laste ned en spesifikk fil kan du klikke på
            filnavnet til filen. Denne vil ikke være komprimert (zippet).
          </p>
        </section>
      </div>
    </>
  );
}
