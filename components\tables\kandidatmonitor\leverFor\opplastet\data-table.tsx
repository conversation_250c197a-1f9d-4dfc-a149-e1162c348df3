"use client";
import React, { useState } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IoIosArrowDown } from "react-icons/io";
import { TestPartsEnum } from "@/enums/TestPart";
import { toast } from "@/components/ui/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  IUploadedFile,
  IUploadedFile as IUploadedFileOriginal,
} from "@/interface/IUploadedFile";
import { getColumns } from "./columns";
import useFileHandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import { GoDotFill, GoTrash } from "react-icons/go";

interface DataTableProps<TData> {
  columns: ColumnDef<TData>[];
  data: TData[];
  isUploading: boolean;
}

function formatTestPartId(testpartId: TestPartsEnum): string {
  switch (testpartId) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og 2";
    default:
      return "Angi eksamensdel";
  }
}

function reverseFormatTestPartId(testpartId: string): TestPartsEnum {
  switch (testpartId) {
    case "Eksamen":
      return TestPartsEnum.Eksamen;
    case "Eksamen del 1":
      return TestPartsEnum.EksamenDel1;
    case "Eksamen del 2":
      return TestPartsEnum.EksamenDel2;
    case "Eksamen del 1 og 2":
      return TestPartsEnum.EksamenDel1ogDel2;
    default:
      return TestPartsEnum.IkkeAngitt;
  }
}

export function DataTable<TData extends IUploadedFile>({
  data,
  isUploading,
}: DataTableProps<TData>) {
  const [ddTestPart, setDdTestPart] = useState<string>("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<IUploadedFile | null>(null);
  const [dialogOpenSingleFile, setDialogOpenSingleFile] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [filesToDelete, setFilesToDelete] = useState<Set<string>>(new Set());
  const { deleteFile, setTestPart, deliverFile } = useFileHandlerForMonitor();

  async function handleFileDelete(file: IUploadedFile): Promise<void> {
    setFileToDelete(file);
    setDialogOpenSingleFile(true);
  }

  const columns = getColumns({ handleFileDelete, filesToDelete, isUploading });

  const table = useReactTable({
    data,
    columns: columns as ColumnDef<TData, any>[],
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  const deliverSelectedFiles = async (
    uploadedFile: IUploadedFile
  ): Promise<boolean> => {
    let fileMissingExamPart = false;
    if (!uploadedFile.IsRejected && !uploadedFile.Delivered) {
      if (uploadedFile.TestPartId === TestPartsEnum.IkkeAngitt) {
        fileMissingExamPart = true;
      } else {
        await deliverFile(uploadedFile as IUploadedFileOriginal);
      }
    }
    return fileMissingExamPart;
  };

  async function submitRows(): Promise<void> {
    const selectedRows = table
      .getRowModel()
      .rows.filter((row) => row.getIsSelected());
    let filesMissingExamPart: string[] = [];

    for (const row of selectedRows) {
      const fileMissingExamPart = await deliverSelectedFiles(
        row.original as IUploadedFile
      );
      if (fileMissingExamPart) {
        filesMissingExamPart.push(row.original.Name);
      }
    }

    if (filesMissingExamPart.length > 0) {
      setIsOpen(true);
    }
    table.resetRowSelection();
  }

  async function deleteRows() {
    const selectedFiles = new Set(
      table.getSelectedRowModel().rows.map((row) => row.original.FileGuid)
    );
    setFilesToDelete(selectedFiles);

    const filesToDeleteArray = Array.from(selectedFiles);

    for (const fileGuid of filesToDeleteArray) {
      try {
        await deleteFile(fileGuid);
      } catch (error) {
        console.error(`Feil ved sletting av fil ${fileGuid}:`, error);
        toast({
          variant: "destructive",
          title: "Feil ved sletting",
          description: `Kunne ikke slette en eller flere filer. Prøv igjen senere.`,
        });
      }
    }

    // Ensure filesToDelete is empty after all deletions (successful or not)
    setFilesToDelete(new Set());
    table.resetRowSelection();
  }

  async function handleSelectionForTestPart(
    selectedValue: string
  ): Promise<void> {
    const partOneExam: string[] = [];
    setDdTestPart(selectedValue);

    const selectedRows = table
      .getRowModel()
      .rows.filter((row) => row.getIsSelected());

    for (const row of selectedRows) {
      if (row.original.TestPartId === TestPartsEnum.Eksamen) {
        partOneExam.push(row.original.Name);
      } else {
        try {
          await setTestPart(
            row.original.FileGuid,
            reverseFormatTestPartId(selectedValue)
          );
          table.resetRowSelection();
        } catch (error) {
          console.error(
            `Error setting test part for file ${row.original.Name}:`,
            error
          );
          toast({
            variant: "destructive",
            title: "Feil ved endring av eksamensdel",
            description: `Kunne ikke endre eksamensdel for filen: ${row.original.Name}`,
          });
        }
      }
    }

    if (partOneExam.length > 0) {
      toast({
        variant: "destructive",
        title: `Kan ikke endre eksamensdel for filer tilknyttet eksamen med én del.`,
        description: (
          <ul className="list-disc list-inside">
            {partOneExam.map((file) => (
              <li key={file}>{file}</li>
            ))}
          </ul>
        ),
      });
    }
  }

  const selectedRowsCount = table.getSelectedRowModel().rows.length;

  return (
    <div className="overflow-x-auto">
      <div className="flex sm:flex-row justify-end sm:items-center mb-4 gap-4">
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <Button
            variant="default"
            className="h-12 rounded-sm normal-case font-normal w-full sm:w-auto"
            onClick={() => submitRows()}
            disabled={selectedRowsCount === 0 || isUploading}
            aria-label={`Lever markerte ${selectedRowsCount}`}
          >
            Lever markerte ({selectedRowsCount})
          </Button>
          <Button
            variant="outline"
            className="border-2 h-12 rounded-sm normal-case font-normal w-full sm:w-auto"
            onClick={() => {
              setDialogOpen(true);
            }}
            disabled={
              table.getSelectedRowModel().rows.length === 0 || isUploading
            }
            aria-label={`Slett markerte ${
              table.getSelectedRowModel().rows.length
            }`}
          >
            <div className="flex items-center gap-2">
              <GoTrash role="img" aria-label="slett fil" className="text-lg" />
              <span>
                Slett markerte ({table.getSelectedRowModel().rows.length})
              </span>
            </div>
          </Button>
        </div>
      </div>
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id} className="text-left font-bold">
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
                className={`hover:text-slate-600 ${
                  filesToDelete.has(row.original.FileGuid) ? "opacity-50" : ""
                }`}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} className="text-left">
                    {filesToDelete.has(row.original.FileGuid) &&
                    cell.column.id === "select" ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                    ) : (
                      flexRender(cell.column.columnDef.cell, cell.getContext())
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                Ingen opplastede filer
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <AlertDialog open={dialogOpen}>
        <AlertDialogContent className="max-h-[80vh] flex flex-col">
          <div className="flex-grow overflow-y-auto">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-lg font-semibold mb-2">
                Er du sikker på at du vil slette markerte filer fra listen?
              </AlertDialogTitle>
              <AlertDialogDescription className="flex flex-col gap-1">
                {table.getSelectedRowModel().rows.map((row) => {
                  return (
                    <span
                      key={row.original.FileGuid}
                      className="text-black flex flex-row items-center gap-2"
                    >
                      <GoDotFill className="text-xs" />
                      <span>{row.original.Name}</span>
                    </span>
                  );
                })}
              </AlertDialogDescription>
            </AlertDialogHeader>
          </div>
          <AlertDialogFooter className="mt-4 flex-shrink-0">
            <AlertDialogCancel
              onClick={() => {
                setDialogOpen(false);
                setFilesToDelete(new Set());
              }}
            >
              Avbryt
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setDialogOpen(false);
                deleteRows();
              }}
            >
              Slett filer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={dialogOpenSingleFile}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Slett fil</AlertDialogTitle>
            <AlertDialogDescription>
              Er du sikker på at du vil slette filen{" "}
              <span className="font-semibold">{fileToDelete?.Name}</span>?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDialogOpenSingleFile(false)}>
              Avbryt
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                if (fileToDelete) {
                  setFilesToDelete((prev) =>
                    new Set(prev).add(fileToDelete.FileGuid)
                  );
                  setDialogOpenSingleFile(false);

                  try {
                    await deleteFile(fileToDelete.FileGuid);
                    table.resetRowSelection();
                  } catch (error) {
                    console.error(
                      `Feil ved sletting av fil ${fileToDelete.Name}:`,
                      error
                    );
                    toast({
                      variant: "destructive",
                      title: "Feil ved sletting",
                      description: `Kunne ikke slette filen ${fileToDelete.Name}. Prøv igjen senere.`,
                    });
                  } finally {
                    setFilesToDelete((prev) => {
                      const newSet = new Set(prev);
                      newSet.delete(fileToDelete.FileGuid);
                      return newSet;
                    });
                  }
                }
              }}
            >
              Slett filen
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Noen filer mangler eksamensdel</AlertDialogTitle>
            <AlertDialogDescription>
              Filer som mangler eksamensdel kan ikke leveres. Filer med gyldig
              eksamensdel har blitt levert.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Ok</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
