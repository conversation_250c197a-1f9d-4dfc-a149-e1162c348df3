import { DefaultAzureCredential } from "@azure/identity";
import { getValueFromRedis, setValueInRedis } from "@/app/lib/redisHelper";

const credential = new DefaultAzureCredential();
const DEFAULT_MARGIN_MS = 5 * 60 * 1000; // 5 minutes margin for regular use
const CONNECTION_SAFE_MARGIN_MS = 2 * 60 * 60 * 1000; // 2 hours for database connections (token valid 24h)
const REDIS_TOKEN_KEY = "PGS:AzureSqlToken";

interface CachedTokenData {
  token: string;
  expiresOnTimestamp: number;
}

export const getAzureSqlToken = async (
  minLifetimeMs: number = DEFAULT_MARGIN_MS
): Promise<string> => {
  const now = Date.now();

  // Try to get cached token from Redis
  try {
    const cachedTokenJson = await getValueFromRedis(REDIS_TOKEN_KEY);

    if (cachedTokenJson) {
      const cachedToken: CachedTokenData = JSON.parse(cachedTokenJson);

      // Check if token has enough lifetime remaining
      if (cachedToken.expiresOnTimestamp - now > minLifetimeMs) {
        const minutesLeft = Math.floor((cachedToken.expiresOnTimestamp - now) / 60000);
        console.log(`Using cached Azure SQL token (${minutesLeft} min left)`);
        return cachedToken.token;
      }
    }
  } catch (error) {
    console.warn("Failed to retrieve cached token, fetching new:", error);
  }

  // Get new token from Azure with retry logic
  let attempts = 0;
  const maxAttempts = 3;

  while (attempts < maxAttempts) {
    try {
      const newToken = await credential.getToken(
        "https://database.windows.net/.default"
      );

      if (!newToken?.token || !newToken.expiresOnTimestamp) {
        throw new Error("Invalid token response from Azure");
      }

      const tokenLifetime = newToken.expiresOnTimestamp - now;

      // Verify that new token has sufficient lifetime
      if (tokenLifetime < minLifetimeMs) {
        const requiredMinutes = Math.floor(minLifetimeMs / 60000);
        const actualMinutes = Math.floor(tokenLifetime / 60000);
        console.warn(`New token lifetime (${actualMinutes} min) is less than required (${requiredMinutes} min)`);
        // Continue anyway - better to have a short token than no token
      }

      // Cache the new token in Redis
      try {
        const tokenData: CachedTokenData = {
          token: newToken.token,
          expiresOnTimestamp: newToken.expiresOnTimestamp,
        };

        const ttlSeconds = Math.floor((tokenLifetime - DEFAULT_MARGIN_MS) / 1000);

        if (ttlSeconds > 0) {
          await setValueInRedis(
            REDIS_TOKEN_KEY,
            JSON.stringify(tokenData),
            ttlSeconds
          );
          const lifetimeMinutes = Math.floor(tokenLifetime / 60000);
          console.log(`New token cached with TTL: ${ttlSeconds}s (${lifetimeMinutes} min lifetime)`);
        }
      } catch (cacheError) {
        console.warn("Failed to cache token (continuing anyway):", cacheError);
      }

      return newToken.token;

    } catch (error) {
      attempts++;
      console.error(`Token fetch attempt ${attempts}/${maxAttempts} failed:`, error);

      if (attempts >= maxAttempts) {
        throw new Error(`Failed to get Azure SQL token after ${maxAttempts} attempts: ${error}`);
      }

      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
    }
  }

  throw new Error("Unexpected error in token acquisition");
};

// Export the safe margin constant for use in connection.ts
export { CONNECTION_SAFE_MARGIN_MS };
