import React from "react";
import Image from "next/image";
import dayjs from "dayjs";
//import * as pdfjs from "pdfjs-dist";
//pdfjs.GlobalWorkerOptions.workerSrc = "/pdf.worker.js";

const metadata = {
  title: "PGSE - Startside",
  description: "Admin for PGS",
};

export default function Home() {
  const today = dayjs();
  //const may13 = dayjs("2024-05-13");

  let examText;

  examText = (
    <div className="flex flex-col gap-3">
      <p>
        Første eksamendag i PGS for høsteksamen 2025 er <b>17. november</b>.
      </p>
      <p>
        I
        <a
          href="https://eksamensplan.udir.no"
          className="text-blue-600 underline ml-1 mr-1"
          target="_blank"
          rel="noopener noreferrer"
        >
          eksamensplanen
        </a>
        ser du hvilke fagkoder som gjennomføres i PGS.
      </p>
    </div>
  );

  return (
    <div className="flex flex-col ">
      <div className="bg-header">
        <div className="py-6">
          <div className="container-wrapper">
            <h1 className="text-3xl md:text-4xl mb-4">PGS-admin</h1>
            <p className="mb-4">
              Velkommen til administrasjonsmodulen til PGS.
            </p>
            <p className="mb-4">
              Vi tar gjerne imot tilbakemeldinger om hvordan vi kan forbedre
              disse sidene. Se tilbakemeldingsfunksjonen nederst.
            </p>
          </div>
        </div>
      </div>

      <div className="container-wrapper">
        <div className="flex-grow py-6">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-8 md:gap-12">
            <div className="w-full md:w-1/2">
              <div className="text-lg mb-4 md:mb-0">{examText}</div>
            </div>
            <div className="w-full md:w-1/2">
              <Image
                src="/images/illustrasjonUtenBakgrunn.png"
                alt="Illustrasjon"
                width={500}
                height={400}
                className=" mx-auto"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
