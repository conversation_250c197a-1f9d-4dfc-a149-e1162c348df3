"use client";

import { But<PERSON> } from "@/components/ui/button";
import { getSession, signOut } from "next-auth/react";
import <PERSON><PERSON> from "js-cookie";
import { useState } from "react";

// Forenklet styling med inline styles
const styles = {
  container: {
    margin: "80px",
    fontFamily: "Arial, Helvetica, sans-serif",
  },
  title: {
    fontSize: "3.5rem",
    marginBottom: "1rem",
  },
  content: {
    fontSize: "1.2rem",
    marginBottom: "1.5rem",
  },
  button: {
    backgroundColor: "#000000",
    color: "white",
    padding: "0.5rem 1rem",
    borderRadius: "0.375rem",
    border: "none",
    cursor: "pointer",
    fontSize: "1rem",
    fontWeight: 500,
    boxShadow: "0 2px 5px rgba(0, 0, 0, 0.1)",
    transition: "background-color 0.2s ease, box-shadow 0.2s ease",
  },
  buttonHover: {
    backgroundColor: "#333333",
    boxShadow: "0 4px 10px rgba(0, 0, 0, 0.2)",
  },
};

const Error = () => {
  const [isHovered, setIsHovered] = useState(false);

  const clearSessionAndRedirect = async (url: string) => {
    Cookie.remove("next-auth.csrf-token");
    sessionStorage.clear();
    localStorage.removeItem("selectedRole");
    await signOut({ redirect: false });
    window.location.href = url;
  };

  const handleLogout = async () => {
    const session = await getSession();

    if (!session) {
      const hostname = window.location.hostname;
      const env = hostname.includes("pgs-admin-dev")
        ? "dev"
        : hostname.includes("pgs-admin-qa")
        ? "qa"
        : hostname.includes("pgs-admin-test")
        ? "test"
        : hostname === "pgs-admin.udir.no"
        ? "prod"
        : hostname.includes("localhost")
        ? "dev"
        : "default";

      const redirectUrl =
        env === "prod"
          ? "https://uidp.udir.no/connect/endsession"
          : env === "test"
          ? "https://uidp-tst.udir.no/connect/endsession"
          : `https://uidp-${env}.udir.no/connect/endsession`;

      await clearSessionAndRedirect(redirectUrl);
      return;
    }

    const queryParams = {
      idtoken: (session?.user as { idToken: string }).idToken,
    };
    const queryString = new URLSearchParams(queryParams).toString();
    const response = await fetch(
      `${window.location.origin}/api/federatedlogout?${queryString}`
    );
    const data = await response.json();

    if (response.ok) {
      await clearSessionAndRedirect(data.url);
    }
  };

  return (
    <div style={styles.container}>
      <h1 style={styles.title}>Ingen tilgang</h1>
      <div style={styles.content}>
        <p>
          Du har ikke tilgang til PGS-admin. Ta kontakt{" "}
          <a href="https://www.udir.no/om-udir/tjenestevei-skole-barnehage/">
            tjenestevei
          </a>
          . For eksempel skal eksamensvakter kontakte sin skoleleder eller
          eksamensansvarlig på skolen.
        </p>
      </div>

      <Button
        variant="secondary"
        style={
          isHovered
            ? { ...styles.button, ...styles.buttonHover }
            : styles.button
        }
        onMouseOver={() => setIsHovered(true)}
        onMouseOut={() => setIsHovered(false)}
        onClick={handleLogout}
      >
        Logg ut
      </Button>
    </div>
  );
};

export default Error;
