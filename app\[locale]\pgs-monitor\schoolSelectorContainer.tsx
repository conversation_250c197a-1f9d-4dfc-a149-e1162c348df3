"use client";
import SchoolSelector from "@/components/schoolSelector";
import { useRole } from "@/context/RoleContext";
import { useMemo } from "react";

export default function SchoolSelectorContainer() {
  const { selectedRole } = useRole();
  const isSingleSchoolRole = useMemo(
    () =>
      selectedRole?.role.startsWith("urn:udir:eksamen:sa") ||
      selectedRole?.role.startsWith("urn:udir:eksamen:sa+") ||
      selectedRole?.role.startsWith("urn:udir:eksamen:ev"),
    [selectedRole?.role]
  );

  return (
    <div className="flex flex-col gap-1">
      {!isSingleSchoolRole && (
        <span className="text-sm font-medium">Velg skole</span>
      )}
      <SchoolSelector />
    </div>
  );
}
