import { DataSource, Repository } from "typeorm";
import <PERSON><PERSON> from "bowser";
import { AuditLog } from "../models/auditLog";
import { getDbConnection } from "../connection";
import { headers } from "next/headers";
import { getAzureSqlToken } from "@/lib/getAzureSqlToken";
import { AuditLogColumns } from "@/enums/AuditLogColumns";
import { IActivityLogV2 } from "@/interface/IActivityLogV2";
import { OperationEnum } from "@/enums/OperationEnum";
import { Operation } from "../models/operation";

export class AuditLogService {
  private static instance: AuditLogService;

  public constructor() {
    // The constructor is now empty as connection is managed by getDbConnection
  }

  public static getInstance(): AuditLogService {
    if (!AuditLogService.instance) {
      AuditLogService.instance = new AuditLogService();
    }
    return AuditLogService.instance;
  }

  private async getRepositories(): Promise<{
    auditLogRepository: Repository<AuditLog>;
    operationRepository: Repository<Operation>;
  }> {
    try {
      const dataSource = await getDbConnection();
      return {
        auditLogRepository: dataSource.getRepository(AuditLog),
        operationRepository: dataSource.getRepository(Operation),
      };
    } catch (error) {
      console.error(`Failed to get repositories:`, error);
      throw error;
    }
  }

  /**
   * Build audit log data with template-based description - High performance (no database access)
   */
  public buildAuditLogData(
    baseData: Partial<IActivityLogV2>,
    operationId: number,
    parameters?: Record<string, any>
  ): Partial<IActivityLogV2> | null {
    try {
      const template = OperationEnum[operationId];
      if (!template) {
        console.error(`Template not found for operation ID: ${operationId}`);
        return null;
      }

      return {
        ...baseData,
        OperationId: operationId,
        Parameters: parameters,
      };
    } catch (error) {
      console.error("Failed to build audit log data:", error);
      return null;
    }
  }

  /**
   * Get all audit logs for a specific kandidat
   */
  public async getAuditLogsForCandidate(
    kandidatpaameldingID: string
  ): Promise<any[]> {
    try {
      const { auditLogRepository } = await this.getRepositories();

      const result = await auditLogRepository
        .createQueryBuilder("auditLog")
        .select([
          "auditLog.Timestamp AS Timestamp",
          "CONVERT(varchar, auditLog.Timestamp, 120) AS TimestampString", // Convert to string format
          "auditLog.Parameters AS Parameters",
          "auditLog.Rolle AS Rolle",
          "auditLog.Filnavn AS Filnavn",
          "auditLog.Eksamensdel AS Eksamensdel",
          "auditLog.KandidatNr AS KandidatNr",
          "auditLog.KandidatFornavn AS KandidatFornavn",
          "auditLog.KandidatEtternavn AS KandidatEtternavn",
          "auditLog.KandidatpaameldingID AS KandidatpaameldingID",
          "operation.BeskrivelseMal AS operation_BeskrivelseMal",
          "operation.Operasjonstype AS operation_Operasjonstype",
          "operation.OperationID AS operation_OperationID",
        ])
        .leftJoin(
          Operation,
          "operation",
          "auditLog.OperationID = operation.OperationID"
        )
        .where("auditLog.KandidatpaameldingID = :kandidatpaameldingID", {
          kandidatpaameldingID,
        })
        .orderBy("auditLog.Timestamp", "DESC")
        .getRawMany();

      // Transform the results to replace placeholders
      for (const log of result) {
        if (log.operation_BeskrivelseMal && log.Parameters) {
          try {
            const params = JSON.parse(log.Parameters);

            // Dynamic replacement using regex that matches any {key}
            log.operation_BeskrivelseMal = log.operation_BeskrivelseMal.replace(
              /\{(\w+)\}/g,
              (match: string, key: string) => {
                return params[key]?.toString() ?? match;
              }
            );
          } catch (error) {
            console.error("Error parsing Parameters:", error);
          }
        }
      }

      return result;
    } catch (error) {
      console.error("Failed to get AuditLogs with Operations:", error);
      throw error;
    }
  }

  public async getAuditLogsForSession(sesjonId: string): Promise<AuditLog[]> {
    try {
      const { auditLogRepository } = await this.getRepositories();

      const result = await auditLogRepository
        .createQueryBuilder("auditLog")
        .leftJoinAndSelect("auditLog.operation", "operation")
        .where("auditLog.KandidatpaameldingID = :sesjonId", {
          sesjonId,
        })
        .orderBy("auditLog.Timestamp", "DESC")
        .getMany();

      return result;
    } catch (error) {
      console.error("Failed to get AuditLogs:", error);
      throw error;
    }
  }
}

export default AuditLogService;
