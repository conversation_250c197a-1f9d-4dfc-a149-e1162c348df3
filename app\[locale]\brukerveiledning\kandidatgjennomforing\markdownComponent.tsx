import React from "react";

export default function Home() {
  return (
    <>
      <div className="prose">
        <div>
          <h2 id="innledning">Innledning</h2>
          <p>
            Denne brukerveiledningen er ment for eksamensvakter som skal bistå
            kandidater ved gjennomføring i PGS. PGS er så enkel å bruke at
            kandidatene ikke skal trenge en tekstveiledning. Hvis de likevel
            trenger hjelp er det bedre at de får det muntlig av en eksamensvakt.
          </p>
          <p>
            Merk at brukerstøtte går{" "}
            <a href="https://www.udir.no/om-udir/tjenestevei-skole-barnehage/">
              tjenestevei
            </a>
            . For eksempel skal eksamensvakter kontakte sin skoleleder eller
            eksamensansvarlig på skolen.
          </p>
          <p>
            Kandidater som har eksamen i PGS kan velge å levere besvarelsen på
            papir. Dersom kandidaten velger å levere besvarelsen på papir kan
            skolen skanne papirbesvarelsen og levere digitalt på vegne av
            kandidaten. Kandidater som velger å levere på papir trenger ikke å
            logge inn i PGS.
          </p>
          <p>
            De fleste eksamenene for våreksamen 2025 (V-2025) skjer i EPS,
            gjennomføringssystemet for heldigital eksamen. Majoriteten av
            kandidatene vil altså for V-2025 gjennomføre eksamen i EPS. Denne
            veiledningen gjelder ikke for dem. Det er mulig å se hvordan
            gjennomføring vil oppleves i EPS ved å{" "}
            <a href="https://sokeresultat.udir.no/eksamensoppgaver.html">
              søke opp eksamensoppgaver{" "}
            </a>
            fra V-2023, H-2023 og V-2024 på udir.no. I{" "}
            <a href="https://eksamensplan.udir.no" target="_blank">
              eksamensplanen
            </a>{" "}
            ser du hvilket gjennomføringssystem som gjelder for de ulike
            fagkodene.
          </p>
          <p>Merk at alle todelte eksamener skjer i PGS.</p>
          <p>
            Det er flere brukerveiledninger for PGS. Disse finner du på siden
            for{" "}
            <a
              className="mx-1"
              href="/brukerveiledning"
            >
              brukerveiledning
            </a>{" "}
            i PGS-admin. Merk at alle persondata som vises i skjermbildene i
            våre veiledninger er fiktive data.
          </p>
          <p>
            På udir.no finner du praktisk informasjon om organisering og
            gjennomføring av sentralt gitt skriftlig eksamen:
            <a
              className="mx-1"
              href="https://www.udir.no/eksamen-og-prover/eksamen/"
            >
              https://www.udir.no/eksamen-og-prover/eksamen/
            </a>
          </p>
        </div>
        <div>
          <h2 id="hurtigguide">Hurtigguide</h2>
          <p className="font-bold">Innlogging og autorisering</p>
          <ul className="list-decimal">
            <li>
              Kandidaten skal gå til{" "}
              <a href="https://kandidat.udir.no" target="_blank">
                kandidat.udir.no
              </a>
              .
            </li>
            <li> Velg «Jeg har eksamen i dag».</li>
            <li>
              Bruk enten Feide eller kandidatnummer og kandidatpassord for å
              logge inn.
            </li>
            <li>
              For at kandidaten skal få digital tilgang til eksamensoppgaven, og
              kunne levere digitalt må kandidaten gis tilgang i PGS-monitor (se
              <a
                href="/brukerveiledning/PGS-monitor"
                className="ml-1"
              >
                {" "}
                brukerveiledningen for PGS-monitor
              </a>
              ).
            </li>
          </ul>
          <p>
            Merk at kandidaten med fordel kan få tilgang i PGS i god tid før
            eksamensstart. Kandidaten får uansett ikke tilgang til
            eksamensoppgaven i PGS før eksamen offisielt starter. Dette gjelder
            også for todelt eksamen.
          </p>
          <p className="font-bold">Levere besvarelse</p>
          <ul className="list-decimal">
            <li>Trykk «Last opp besvarelsen»</li>
            <li>
              Last opp en eller flere filer. Laster man opp feil fil, trykker
              man «Slett» (med ikon av papirkurv) til høyre for filnavnet for å
              fjerne filen.
            </li>
            <li>Kontroller besvarelsen ved å trykke «Last ned».</li>
            <li>
              Kandidaten skal åpne den nedlastede filen og verifisere at
              innholdet er riktig. Kandidaten vil få en bekreftelse i PGS på at
              filen er sjekket til venstre for filnavnet. Dersom kandidaten
              oppdager at han/hun har lastet opp feil fil, må opplastet fil
              slettes i PGS og deretter må riktig fil lastes opp og sjekkes. Om
              det skal gjøres endringer i filen etter å ha sjekket den, må filen
              endres lokalt på kandidatens PC. Deretter må den gamle versjonen
              slettes i PGS, og oppdatert fil må lastes opp og sjekkes.
            </li>
            <li>
              Når kandidaten har sjekket alle filene kan hen levere ved å trykke
              på «Lever filene».
            </li>
            <li>
              Bekreft levering. Etter dette kan ikke kandidaten levere på nytt,
              med mindre eksamensvakten åpner for dette i PGS-monitor. Se egen
              brukerveiledning for PGS-monitor.
            </li>
            <li>
              Når kandidaten har levert vil kandidaten komme til
              kvitteringssiden. Kandidaten skal ikke logge ut eller lukke
              nettleseren før en eksamensvakt har kontrollert kvitteringssiden.
            </li>
          </ul>
          <p>
            Dagen etter eksamen kan kandidaten logge på{" "}
            <a href="https://kandidat.udir.no" target="_blank">
              kandidat.udir.no
            </a>{" "}
            for å se sin egen besvarelse. Kandidaten kan da logge inn enten med
            Feide eller med ID-porten.
          </p>
          <p>
            Kandidaten kan i enkelte tilfeller oppleve feil som skyldes forhold
            med nettleseren sin. Forsøk da å oppdatere siden i nettleseren til
            kandidaten. Hvis feilen vedvarer, kan kandidaten forsøke med en
            annen type nettleser.
          </p>
        </div>
        <div className="font-normal">
          <h2 id="innloggingssiden">Innloggingssiden </h2>
          <blockquote className="not-italic font-normal">
            <h3 id="infoboks">Infoboks</h3>
            <p className="">
              Med <i>autentisering</i> mener vi her prosessen med å bekrefte en
              identitet. Dette skjer ved at kandidaten logger på med{" "}
              <a className="ml-1 mr-1" href="https://www.feide.no/om-feide">
                Feide
              </a>
              eller bruker kandidatnummer og passord. Autentiseringen foregår i
              Udir sin løsning for identitets- og tilgangskontroll:
              <a
                className="ml-1"
                href="https://uidp-admin.udir.no/info/om-uidp"
                target="_blank"
              >
                UIDP
              </a>
              .
            </p>
            <p>
              Med <i>autorisering</i> mener vi her prosessen med å avgjøre om en
              kandidat skal få tilgang til PGS. Kandidaten autoriseres ved at
              eksamensvakten gir tilgang i PGS-monitor eller at dagspassord
              benyttes.
            </p>
          </blockquote>
          <p>
            Kandidaten går til
            <a href="https://kandidat.udir.no" target="_blank" className="ml-1">
              kandidat.udir.no
            </a>
            , og velger «Jeg har eksamen i dag». Hen kommer så til
            innloggingssiden. Under vises et skjermbilde av innloggingssiden for
            kandidater.
          </p>
          <p>
            Kandidaten autentiserer seg med sin Feide-bruker, eller med
            kandidatnummer og kandidatpassord på innloggingssiden i UIDP.
            Skoleadministrator har tilgang på kandidatnummer og kandidatpassord
            i PAS-eksamen. I PAS finner man også dagspassordet for de
            forskjellige kandidatgruppene. Under vises et skjermbilde av
            innloggingssiden for kandidater.
          </p>

          <img src="/images/signinnew.gif" alt="logg inn bilde" />
        </div>
        <div>
          <h2 id="autoriseringssiden">Autoriseringssiden</h2>
          <p>
            Når kandidaten har autentisert seg og logget inn i PGS vil hen komme
            til autoriseringssiden. Kandidatens navn vil vises i overskriften på
            siden. Når kandidaten kommer til autoriseringssiden vil det
            automatisk sendes en tilgangsforespørsel til PGS-monitor, og knappen
            "Gi tilgang" vil vises i PGS-monitor, se separat
            <a
              href="/brukerveiledning/pgs-monitor"
              className="ml-1"
            >
              brukerveiledning for PGS-monitor
            </a>
            .
          </p>
          <p>
            For at kandidaten skal få tilgang til eksamensoppgaven, og kunne
            levere eksamen digitalt må kandidaten altså gis digital tilgang
            (autoriseres) av eksamensvakten i PGS-monitor. Eksamensvakten bør
            påse at kandidaten er til stede i eksamenslokalet og er logget på
            med sin egen bruker før det gis digital tilgang til PGS.
          </p>
          <p>
            Kandidaten vil sendes automatisk videre til ventesiden eller
            oppgavesiden i kandidatmodulen når eksamensvakten har gitt tilgang.
            Dersom eksamen ikke har startet ennå vil kandidaten få
            tilgangsstatus «Venter på eksamensstart» og komme til ventesiden.
            Dersom eksamen har startet vil kandidaten få status «Har tilgang» og
            komme til oppgavesiden.
          </p>
          <p>
            Dersom kandidaten mot formodning ikke blir sendt videre i løpet av
            kort tid, så kan kandidaten oppdatere autoriseringssiden. En ny
            tilgangsforespørsel vil da sendes til PGS-monitor og eksamensvakten
            kan gi tilgang på nytt.
          </p>
          <p>
            Et alternativ til å gi tilgang via PGS-monitor er at eksamensvakten
            skriver inn dagspassordet i nettleseren til kandidaten.
            Dagspassordet skal være hemmelig og skal ikke deles med kandidaten.
            Dagspassord er tilgjengelig for skoleadministrator i PAS-eksamen.
            Merk at autorisering med dagspassord er mindre sikkert enn å gi
            tilgang via PGS-monitor.
          </p>
        </div>
        <div>
          <h2 id="ventesiden">Ventesiden</h2>
          <p>
            Når kandidaten har fått tilgang av eksamensvakten vil hen komme til
            ventesiden. Ventesiden vil vise nedtelling til eksamensstart. Når
            eksamen starter, vil kandidaten automatisk sendes videre til
            eksamensoppgaven (oppgavesiden).
          </p>
          <p>
            Dersom kandidaten logger seg inn og autoriseres etter eksamensstart
            vil hen sendes direkte til oppgavesiden uten å komme til ventesiden.
          </p>
          <p>
            Det er uproblematisk at kandidaten får digital tilgang til PGS før
            tidspunktet for eksamensstart. Kandidaten får uansett ikke tilgang
            til eksamensoppgaven i PGS før den digitale delen offisielt starter.
            Dette gjelder også for todelt eksamen – kandidaten vil ikke få
            tilgang eksamensoppgaven for del 2 før denne eksamensdelen offisielt
            starter. Starttidspunktet for del 2 er 10:00, 10:45 eller 11:00
            avhengig av fagkoden.
          </p>
          <p>
            Da del 1 for todelt eksamen kun skal gjennomføres på papir, er ikke
            del 1 tilgjengelig i PGS for kandidatene.
          </p>
          <img src="/images/ventesiden.png" alt="ventesiden" />
        </div>
        <div>
          <h2 id="oppgavesiden">Oppgavesiden</h2>

          <p>
            På oppgavesiden i PGS kan kandidaten laste ned eksamensoppgaven.
            Dersom eksamen har en forberedelsesdel, vil kandidaten også kunne
            laste ned forberedelsesmateriellet. For todelt eksamen vil ikke del
            1 være tilgjengelig for nedlastning.
          </p>
          <p>
            Kandidaten vil fra oppgavesiden også kunne gå videre til levering av
            eksamen (leveringssiden).
          </p>
          <img src="/images/oppgavesiden.gif" alt="oppgavesiden" />
        </div>

        <div>
          <h2 id="leveringssiden-last-opp">Leveringssiden – last opp</h2>
          <p>
            På leveringssiden skal kandidaten laste opp besvarelsesfilen(e)
            sin(e). For å laste opp filene kan kandidaten velge å dra filene
            over et angitt område eller hen kan benytte filutforskeren til å
            velge filene.
          </p>
          <img src="/images/lastopp.gif" alt="leveringssideLastopp" />
          <p>Det er flere begrensninger:</p>
          <ul>
            <li>
              Maksimal filstørrelse er 40MB – maksgrensen vises på siden. Kun
              godkjente filtyper kan lastes opp.
            </li>
            <li>Duplikat filnavn tillates ikke.</li>
            <li>Kandidaten kan maksimalt laste opp 20 filer om gangen.</li>
            <li>Minimum filstørrelse er 1kB.</li>
          </ul>
          <p>
            Kandidaten vil få feilmelding ved opplasting dersom en fil ikke
            validerer. Feilede filer må slettes.
          </p>{" "}
          <img src="/images/leveringssideFeil.png" alt="leveringssideFeil" />
          <p className="font-bold">
            Godkjente filtyper er:
            <span className="ml-1 font-normal">
              7z, avi, bmp, c, css, doc, docm, docx, dot, dotm, dotx, emf, flv,
              ggb, gif, gz, h, hlp, htm, html, jpe, jpeg, jpg, js, m4a, mdb,
              mdi, mht, mm, mov, movie, mp3, mp4, mpeg, mpp, odb, odp, ods, odt,
              ott, pdf, png, ppsm, ppsx, ppt, pptm, pptx, qt, rap, rar, rm, rtf,
              sib, stw, svg, swf, sxc, sxw, tar, tex, tif, tiff, tii, txt, vsd,
              wav, wmf, wmv, wri, xls, xlsb, xlsm, xlsx, xml, xps, z, zip, zipx
            </span>
          </p>
        </div>
        <div>
          <h2 id="leveringssiden-last-ned-og-sjekk-filene">
            Leveringssiden – last ned og sjekk filene
          </h2>
          <p>
            Før kandidaten kan levere må kandidaten laste ned og sjekke filene
            for å sikre at de er korrekte. Dette gjøres ved å klikke «Last ned»,
            for så å åpne og se gjennom den nedlastede filen. Dette må gjøres
            for alle filer som kandidaten har lastet opp til PGS.
          </p>
          <p>
            Dersom kandidaten oppdager at hen har lastet opp feil fil, må
            opplastet fil slettes i PGS. Deretter må riktig fil lastes opp og
            sjekkes.
          </p>
          <p>
            Dersom kandidaten ser behov for å gjøre endringer i filen etter å ha
            sjekket den, må filen endres på kandidatens PC. Deretter må den
            gamle versjonen slettes i PGS, og oppdatert fil må lastes opp og
            sjekkes.
          </p>
          <p>
            Dersom kandidaten mot formodning skulle få en feilmelding ifb.
            nedlastning, er anbefalingen at kandidaten oppdaterer nettsiden i
            nettleseren.
          </p>
          <p>
            Merk at kandidaten ikke vil se filer som er lastet opp av
            eksamensvaktene på leveringssiden. Imidlertid vil kandidaten se
            samtlige leverte filer på kvitteringssiden, uavhengig av hvem som
            har levert filen.
          </p>
          <img src="/images/LastNedOgSjekk.png" alt="LastNedOgSjekk" />
        </div>
        <div>
          <h2 id="leveringssiden-lever-eksamensbesvarelse">
            Leveringssiden – lever eksamensbesvarelse
          </h2>
          <p>
            Når kandidaten har sjekket alle filene kan hen levere sin
            eksamensbesvarelse ved å klikke «Lever filene».
          </p>
        </div>
        <div>
          <h2 id="siste-bekreftelse"> Siste bekreftelse</h2>
          <p>
            For å endelig levere eksamensbesvarelsen må kandidaten gi en siste
            bekreftelse. Kandidaten må da klikke på knappen «Bekreft levering».
          </p>
          <p>
            Etter at kandidaten har gitt siste bekreftelse vil ikke kandidaten
            kunne gjøre endringer på sine besvarelsesfiler eller angre
            leveringen. Eksamensvakten kan imidlertid ved behov åpne for at
            kandidaten skal få levere på nytt. Se brukerveiledningen for
            PGS-monitor for mer om dette.
          </p>
          <img src="/images/sisteBekreftelse.png" alt="sisteBekreftelse" />
        </div>
        <div>
          <h2 id="kvitteringssiden">Kvitteringssiden</h2>
          <p>
            Når kandidaten har bekreftet levering vil hen sendes automatisk til
            kvitteringssiden. På kvitteringssiden vil kandidaten se en liste
            over leverte filer, og hen vil ha muligheten til å laste ned en
            kvittering for levert eksamen. For todelt eksamen vil eksamensdelen
            spesifiseres i oversikten over leverte filer, og filer for del 1 vil
            vises dersom en eksamensvakt har levert digitalt for del 1.
          </p>
          <p>
            Kandidaten skal ikke lukke siden eller logge ut, uten at hen har
            fått tillatelse fra en eksamensvakt. Dette er for å sikre at
            kandidaten får levert eksamen korrekt.
          </p>
          <p>
            Dagen etter eksamen kan kandidaten logge inn på{" "}
            <a
              href="https://kandidat.udir.no"
              target="_blank"
              className="ml-1 mr-1"
            >
              kandidat.udir.no
            </a>
            for å se sin egen besvarelse. Kandidaten kan da logge inn enten med
            Feide eller med ID-porten.
          </p>
          <p>
            Under vises et skjermbilde av kvitteringssiden når kandidaten har
            levert del 2 digitalt:
          </p>
          <img src="/images/kvittering.png" alt="kvitteringssiden" />
          <p>
            Under vises et skjermbilde av kvitteringssiden når kandidaten har
            sendes i posten:
          </p>
          <img src="/images/kvitteringPapir.png" alt="kvittering på papir" />
        </div>
        <div>
          <h3 id="fravrssiden">Fraværssiden</h3>

          <p>
            En kandidat som er markert med «Dokumentert fravær» eller
            «Ikke-dokumentert fravær» i PGS, vil komme til fraværssiden dersom
            hen forsøker å logge på PGS. På fraværssiden får kandidaten beskjed
            om å kontakte en eksamensvakt hvis dette er feil.
          </p>
          <img src="/images/fravar.png" alt="fravær" />
        </div>
        <div>
          <h2 id="Ikke-tilgang-siden">Ikke-tilgang-siden</h2>
          <p>
            Dersom eksamensvakten har sperret digitalt tilgang vil kandidaten få
            opp ikke-tilgang-siden som forklarer at kandidaten ikke har digital
            tilgang til eksamen og at kandidaten kan ta kontakt med
            eksamensvakten hvis dette er feil.
          </p>
          <p>
            Under vises et skjermbilde av Ikke-tilgang-siden når kandidaten har
            tilgangsstatus "Digitalt tilgang sperret" i PGS-monitor:
          </p>
          <img src="/images/ikke-tilgang.png" alt="Ikke-tilgang-siden" />
        </div>
      </div>
    </>
  );
}
