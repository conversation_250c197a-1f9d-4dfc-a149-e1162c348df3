import { ColumnDef, Column } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import dayjs from "dayjs";

export const createColumns = (
  handleRemoveAccess: (
    candidateNumber: string,
    sessionId: string,
    userId: string
  ) => Promise<void>,
  redisResult: IRedisObject[],
  sessionData: ICandidateSessionMetaData[] | undefined
): ColumnDef<IRedisObject>[] => {
  // Lag et map for oppslag av autorisasjonsstatus
  const authMap = new Map(
    redisResult.map((obj) => [obj.userSessionId, obj.isAuthorized])
  );

  const getLatestSession = (
    userSessionId: string,
    sessionData: ICandidateSessionMetaData[] | undefined
  ) => {
    if (!sessionData) return null;

    // Filtrer sessionData for gjeldende session ID
    const sessions = sessionData.filter((s) => s.SessionId === userSessionId);

    // Sorter synkende etter LastActive
    const sortedSessions = [...sessions].sort((a, b) =>
      dayjs(b.LastActive).isAfter(dayjs(a.LastActive)) ? 1 : -1
    );

    // Returner den siste aktive sesjonen
    return sortedSessions[0] || null;
  };

  const getSortButton = (text: string) => {
    return ({ column }: { column: Column<IRedisObject> }) => (
      <Button
        variant="ghost"
        className="p-0 hover:bg-transparent"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        {text}
        <ArrowUpDown
          className="ml-2 h-4 w-4"
          role="img"
          aria-label="opp-ned pil ikon"
        />
      </Button>
    );
  };

  return [
    {
      accessorKey: "userGuid",
      header: getSortButton("Innloggingssesjon"),
      cell: ({ row }) => (
        <div className="capitalize text-wrap">{row.original.userSessionId}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "timeForSession",
      header: getSortButton("Startet"),
      cell: ({ row }) => (
        <div>{dayjs(row.original.timeForSession).format("HH:mm")}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "ipAddress",
      header: getSortButton("IP"),
      cell: ({ row }) => {
        const latestSession = getLatestSession(
          row.original.userSessionId,
          sessionData
        );
        const ip = latestSession?.IpAddress || row.original.ipAddress;
        return <div className="text-wrap">{ip}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "activeStatus",
      header: "Aktiv",
      cell: ({ row }) => {
        const latestSession = getLatestSession(
          row.original.userSessionId,
          sessionData
        );
        const isAuthorized = authMap.get(row.original.userSessionId);

        // Vis blankt hvis brukeren ikke er autorisert
        if (!isAuthorized) {
          return <div></div>;
        }

        if (!latestSession) {
          return <div className="text-gray-500">Ingen data</div>;
        }

        // Aktiv hvis siste aktivitet er mindre enn 5 minutter gammel
        const lastActiveTime = dayjs(latestSession.LastActive);
        const currentTime = dayjs();
        const isActive = currentTime.diff(lastActiveTime, "minute") < 5;

        return (
          <div className="flex items-center gap-2">
            <div
              className={`min-w-[12px] min-h-[12px] rounded-full ${
                isActive ? "bg-green-500" : "bg-gray-400"
              }`}
            />
            <span className="text-gray-600">
              {isActive ? "Aktiv" : "Ikke aktiv"}
            </span>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "isAuthorized",
      header: "Tilgang",
      cell: ({ row }) => (
        <div>{authMap.get(row.original.userSessionId) ? "Ja" : "Nei"}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "actions",
      header: "",
      cell: ({ row }) => {
        const isAuthorized = authMap.get(row.original.userSessionId);

        if (!isAuthorized) {
          return null;
        }

        return (
          <div>
            <Button
              onClick={() =>
                handleRemoveAccess(
                  row.original.candidateNumber,
                  row.original.userSessionId,
                  row.original.userId
                )
              }
            >
              Fjern tilgang
            </Button>
          </div>
        );
      },
      enableSorting: false,
    },
  ];
};

export default createColumns;
