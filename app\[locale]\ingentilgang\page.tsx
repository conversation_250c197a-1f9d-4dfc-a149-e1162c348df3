// app/ingen-tilgang/page.tsx

import { Metadata } from "next";
import Link from "next/link";
import { FaExclamationCircle } from "react-icons/fa";

export const metadata: Metadata = {
  title: "Ingen tilgang - PGS-admin",
 
};
export default function IngenTilgang() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-6 bg-gray-100">
      <div className="text-9xl">
        <FaExclamationCircle />
      </div>
      <h1 className="text-4xl font-bold">Ingen tilgang</h1>
      <p className="text-xl text-gray-700">
        <PERSON><PERSON><PERSON>, rollen du har valgt gir deg ikke tilgang til denne siden.
      </p>
      <p className="text-md text-gray-600">
        Hvis du mener dette er en feil, vennligst kontakt systemadministrator.
      </p>
      <Link
        href="/"
        className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
      >
        <PERSON><PERSON> til forsiden
      </Link>
    </div>
  );
}
