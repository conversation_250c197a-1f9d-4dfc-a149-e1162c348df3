"use client";

import {
  createContext,
  useContext,
  useCallback,
  useState,
  ReactNode,
  useEffect,
} from "react";
import useSWR from "swr";
import { useRole } from "@/context/RoleContext";
import { useSession } from "next-auth/react";
import { SignalRMessageEnum } from "@/enums/SignalRMessageEnum";
import { useSignalRConnection } from "./signalRProvider";
import { useToast } from "@/components/ui/use-toast";

export interface IAccessRequestUpdate {
  id: string;
  schoolId: string;
  candidateNumber: string;
  hasRequestedAccess: boolean;
  timestamp?: string;
}

export interface IGrantAccessRequest {
  candidateNumber: string;
  examGroupCode: string;
  userId: string;
  sessionId: string;
}

interface AccessRequestsContextValue {
  accessRequests: IAccessRequestUpdate[];
  grantAccessRequest: (
    update: IGrantAccessRequest,
    shouldGrant: boolean
  ) => Promise<boolean>;
  setAccessRequests: React.Dispatch<
    React.SetStateAction<IAccessRequestUpdate[]>
  >;
  isLoading: boolean;
  error: Error | null;
  isSubmitting: boolean;
}

const AccessRequestsContext = createContext<
  AccessRequestsContextValue | undefined
>(undefined);

const fetcher = async (url: string) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ message: "Unknown error" }));
      throw new Error(
        errorData.message || `Failed to fetch: ${response.status}`
      );
    }
    return response.json();
  } catch (error) {
    console.error("Error fetching access requests:", error);
    throw error;
  }
};

export function AccessRequestsProvider({ children }: { children: ReactNode }) {
  const { selectedRole } = useRole();
  const { connection } = useSignalRConnection();
  const { toast } = useToast();
  const { status } = useSession();

  const schoolId = selectedRole?.selectedSchoolId;
  const [accessRequests, setAccessRequests] = useState<IAccessRequestUpdate[]>(
    []
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  // SWR for polling access requests - only runs when examPaper has data
  const {
    data: accessRequestsData,
    error,
    isLoading,
  } = useSWR(
    status === "authenticated" && schoolId
      ? `/api/accessRequestInRedis?schoolId=${schoolId}`
      : null,
    fetcher,
    {
      refreshInterval: 30000,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      onError: (err) => {
        console.error("Error fetching access requests:", err);
      },
      retry: 2,
    }
  );

  useEffect(() => {
    if (accessRequestsData) {
      try {
        const requests = Object.values(
          accessRequestsData
        ) as IAccessRequestUpdate[];
        setAccessRequests(requests);
      } catch (err) {
        console.error("Error processing access request data:", err);
      }
    }
  }, [accessRequestsData]);

  const handleAccessRequest = useCallback((update: IAccessRequestUpdate) => {
    try {
      setAccessRequests((prev) => {
        if (update.hasRequestedAccess) {
          const filtered = prev.filter(
            (req) => req.candidateNumber !== update.candidateNumber
          );
          return [
            { ...update, timestamp: new Date().toISOString() },
            ...filtered,
          ];
        }
        return prev.filter(
          (req) => req.candidateNumber !== update.candidateNumber
        );
      });
    } catch (err) {
      console.error("Error handling access request update:", err);
    }
  }, []);

  // Set up access request event listener
  useEffect(() => {
    if (connection) {
      try {
        connection.on(SignalRMessageEnum.AccessRequest, handleAccessRequest);

        return () => {
          connection.off(SignalRMessageEnum.AccessRequest);
        };
      } catch (err) {
        console.error("Error setting up SignalR connection:", err);
      }
    }
  }, [connection, handleAccessRequest]);

  const grantAccessRequest = useCallback(
    async (
      update: IGrantAccessRequest,
      shouldGrant: boolean = true
    ): Promise<boolean> => {
      if (!schoolId) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Ingen skole er valgt",
        });
        return false;
      }

      setIsSubmitting(true);

      try {
        if (shouldGrant) {
          // Grant access
          const response = await fetch("/api/grantAccessRequest", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              ...update,
              schoolId,
            }),
          });

          if (!response.ok) {
            const errorData = await response
              .json()
              .catch(() => ({ error: "Unknown error" }));
            throw new Error(
              errorData.error || `Failed to grant access: ${response.status}`
            );
          }
        } else {
          // Cancel access request
          const response = await fetch("/api/grantAccessRequest", {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              candidateNumber: update.candidateNumber,
              schoolId,
              userId: update.userId,
              sessionId: update.sessionId,
            }),
          });

          if (!response.ok) {
            const errorData = await response
              .json()
              .catch(() => ({ error: "Unknown error" }));
            throw new Error(
              errorData.error ||
                `Klarte ikke å kansellere tilgangsforespørsel ${response.status}`
            );
          }
        }

        // Immediately update local state after successful API call to ensure filter updates
        setAccessRequests((prev) =>
          prev.filter((req) => req.candidateNumber !== update.candidateNumber)
        );

        return true;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        console.error("Error processing access request:", errorMessage);

        return false;
      } finally {
        setIsSubmitting(false);
      }
    },
    [schoolId, status, toast]
  );

  return (
    <AccessRequestsContext.Provider
      value={{
        accessRequests,
        grantAccessRequest,
        setAccessRequests,
        isLoading,
        error,
        isSubmitting,
      }}
    >
      {children}
    </AccessRequestsContext.Provider>
  );
}

export function useAccessRequests() {
  const context = useContext(AccessRequestsContext);
  if (!context) {
    throw new Error(
      "useAccessRequests must be used within an AccessRequestsProvider"
    );
  }
  return context;
}
