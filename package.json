{"name": "pgs-next-admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start -p 4000", "lint": "next lint"}, "dependencies": {"@azure/identity": "^4.9.1", "@azure/service-bus": "^7.9.5", "@azure/storage-blob": "^12.17.0", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.0.1", "@mdx-js/react": "^3.0.1", "@microsoft/applicationinsights-web": "^3.3.2", "@microsoft/signalr": "^8.0.7", "@next/mdx": "^14.2.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-table": "^8.20.6", "@tanstack/table-core": "^8.20.5", "@types/mdx": "^2.0.13", "@types/xml2js": "^0.4.14", "add": "^2.0.6", "applicationinsights": "^3.3.0", "archiver": "^7.0.1", "bowser": "^2.11.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "cookie": "^1.0.2", "cookie-signature": "^1.2.1", "cookies": "^0.9.1", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "extract-md-headings": "^0.2.7", "framer-motion": "^12.18.1", "ioredis": "^5.4.1", "ip-cidr": "^4.0.2", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "mssql": "^11.0.1", "next": "^15.3.5", "next-auth": "^4.24.7", "next-cookies": "^1.1.3", "next-intl": "^3.11.1", "pdfjs-dist": "^4.3.136", "react": "^19.1.0", "react-countdown": "^2.3.6", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.54.2", "react-icons": "^5.3.0", "react-to-print": "^3.1.1", "recharts": "^2.15.1", "reflect-metadata": "^0.2.2", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.0", "request-ip": "^3.3.0", "shadcn": "^2.4.0-canary.14", "sharp": "^0.33.5", "swr": "^2.2.5", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "toggle": "^0.1.1", "typeorm": "^0.3.22", "uuid": "^9.0.1", "xlsx": "^0.18.5", "xml2js": "^0.6.2", "zod": "^3.24.1"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/cookie-signature": "^1.1.2", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/request-ip": "^0.0.41", "@types/uuid": "^9.0.8", "autoprefixer": "^10.0.1", "daisyui": "^3.9.4", "eslint": "^8", "eslint-config-next": "^15.3.5", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}, "description": "This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).", "exports": "./postcss.config.cjs", "author": "", "license": "ISC"}