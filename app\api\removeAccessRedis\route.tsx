"use server";

import { NextRequest, NextResponse } from "next/server";
import { getAppInsightsServer } from "@/lib/appInsightsServer";
import { hashKeyExists, setHashFieldInRedis } from "@/app/lib/redisHelper";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";

const telemetryClient = getAppInsightsServer();

interface IRemoveAccessPayload {
  candidateNumber: string;
  userSessionId: string;
}

async function removeAccess(userSessionId: string, candidateNumber: string) {
  const key = `candidate:${candidateNumber}:${userSessionId}`;

  try {
    // Check if the hash key exists
    const exists = await hashKeyExists(key);

    if (!exists) {
      throw new Error("User not found in redis");
    }

    // Update only the isAuthorized field in the hash
    await setHashFieldInRedis(key, "isAuthorized", "false");
  } catch (error) {
    console.error("Error in removeAccess", error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    let body: IRemoveAccessPayload = await request.json();
    if (!body.userSessionId) {
      console.log("Sesjonsid mangler");
    }
    removeAccess(body.userSessionId, body.candidateNumber);

    return NextResponse.json({
      message: "Access removed successfully",
    });
  } catch (error) {
    // Logg feil
    console.error("Error removing access status in Redis", error);
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        operation: "RemoveAccessRedis",
        errorMessage: (error as Error).message,
      },
    });

    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
