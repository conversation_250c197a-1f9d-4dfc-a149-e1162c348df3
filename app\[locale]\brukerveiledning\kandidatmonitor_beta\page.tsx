import React from "react";
import MarkdownComponent from "./markdownComponent";
import TableOfContents from "@/components/toc";
import { extractHeadings } from "extract-md-headings";

export default async function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="p-6 bg-stalbla">
        <div className="container-wrapper">
          <h1 className="text-4xl">Brukerveiledning betaversjon av PGS-monitor</h1>
          <p className="mt-4">
            Veiledning for høsten 2024 – sist oppdatert 11.11.2024
          </p>
        </div>
      </div>
      <div className="container-wrapper flex justify-between md:gap-8 p-10">
        <div className="">
          <MarkdownComponent />
        </div>
        <div className="hidden md:block md:w-[300px] lg:w-[350px] xl:w-[400px] ml-20 ">
          <div className="sticky top-40 bottom-4 w-[280px] lg:w-[330px] xl:w-[380px] overflow-y-auto">
            <TableOfContents />
          </div>
        </div>
      </div>
    </div>
  );
}
