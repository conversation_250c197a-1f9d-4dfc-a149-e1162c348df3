"use client";
import React, { useEffect, useState, useCallback } from "react";
import { DataTable } from "@/components/tables/nedlasting/candidateInfo/data-table";
import { createColumns } from "@/components/tables/nedlasting/candidateInfo/columns";
import { But<PERSON> } from "@/components/ui/button";
import { ICandidate } from "@/interface/ICandidate";
import { ImSpinner2 } from "react-icons/im";
import { IoMdDownload } from "react-icons/io";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { usePathname } from "next/navigation";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";
import { MdArrowForward } from "react-icons/md";
import useFileHandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import { useRouter } from "next/navigation";
import { useExamPaper } from "@/hooks/useExamPaper";

interface CandidateInfoModalProp {
  candidateInfo: ICandidate;
  subjectCode: string;
  testPeriod: string;
  subjectName: string;
}

export default function CandidateInfoModal({
  candidateInfo,
  subjectCode,
  testPeriod,
  subjectName,
}: CandidateInfoModalProp): JSX.Element {
  const pathname = usePathname();
  const router = useRouter();
  const [showMonitorStatus, setShowMonitorStatus] = useState<boolean>(false);
  const { setCandidateNumber } = useFileHandlerForMonitor();

  // Use the hook for download functionality
  const { download } = useExamPaper();

  useEffect(() => {
    if (pathname === "/pgs-monitor") {
      setShowMonitorStatus(true);
    }
  });

  const downloadZippedFiles = useCallback(async () => {
    await download.downloadCandidate(
      candidateInfo,
      subjectCode,
      subjectName,
      testPeriod
    );
  }, [candidateInfo, subjectCode, subjectName, testPeriod, download]);

  if (!candidateInfo) {
    return <div role="alert">Ingen kandidatinformasjon tilgjengelig.</div>;
  }
  const showTestPartId = candidateInfo.deliveryStatusPart2 !== -1;

  const handleDeliverForCandidate = () => {
    sessionStorage.setItem("selectedCandidate", JSON.stringify(candidateInfo));
    setCandidateNumber(candidateInfo.candidateNumber);

    const queryParams = new URLSearchParams({
      part1: candidateInfo.deliveryStatusPart1.toString(),
      part2: candidateInfo.deliveryStatusPart2.toString(),
    });

    router.push(
      `/pgs-monitor/${
        candidateInfo.candidateNumber
      }?${queryParams.toString()}`
    );
  };
  return (
    <>
      <DialogHeader className="mb-4">
        <DialogTitle>
          Besvarelsesfiler for {candidateInfo.candidateName} (
          {candidateInfo.candidateNumber})
        </DialogTitle>
        <DialogDescription />
      </DialogHeader>

      {candidateInfo.documents.length > 0 ? (
        <div className="flex flex-col gap-4">
          <div
            role="region"
            aria-label="Kandidatens besvarelser"
            className="overflow-x-auto"
          >
            <DataTable
              columns={createColumns(showMonitorStatus, showTestPartId)}
              data={candidateInfo.documents}
            />
          </div>

          <DownloadButton
            onClick={downloadZippedFiles}
            downloading={download.state.downloading}
            progress={download.state.progress}
            totalSize={candidateInfo.documents.reduce(
              (sum, doc) => sum + parseInt(doc.fileSize),
              0
            )}
          />
          {pathname === "/pgs-monitor" && (
            <Alert className="md:w-[600px] w-full bg-stalbla border border-black">
              <Info className="h-4 w-4" role="img" aria-label="info ikon" />
              <AlertDescription>
                <div className="flex justify-between">
                  <span>Ønsker du å slette en fil?</span>
                  <div
                    className="underline-offset-4 underline decoration-2 flex gap-2 cursor-pointer"
                    onClick={handleDeliverForCandidate}
                  >
                    {" "}
                    Gå til administrering av filer{" "}
                    <MdArrowForward
                      role="img"
                      aria-label="pil ikon"
                      size={20}
                    />
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>
      ) : (
        <div className="h-12 flex items-center justify-center text-gray-500">
          <span>Kandidaten har ikke lastet opp noen filer.</span>
        </div>
      )}
    </>
  );
}

// Helper Components
const DownloadButton = ({
  onClick,
  downloading,
  progress,
  totalSize,
}: {
  onClick: () => void;
  downloading: boolean;
  progress: number;
  totalSize: number;
}) => {
  const formatSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "kB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = bytes / Math.pow(k, i);
    
    // For filer under 1 MB (kB), rund opp til nærmeste hele kB
    if (i === 1) {
      return Math.ceil(size) + " " + sizes[i];
    }
    
    return parseFloat(size.toFixed(2)) + " " + sizes[i];
  };

  const progressText =
    totalSize > 0
      ? `${formatSize(progress)} / ${formatSize(totalSize)} (${Math.round(
          (progress / totalSize) * 100
        )}%)`
      : "";

  return (
    <div className="flex items-center space-x-4">
      <Button
        onClick={onClick}
        variant="default"
        className="flex items-center justify-center"
        disabled={downloading}
        aria-busy={downloading}
      >
        {downloading ? (
          <>
            <ImSpinner2
              className="animate-spin mr-2 text-lg"
              role="img"
              aria-label="spinner"
            />
            <span>Laster ned</span>
          </>
        ) : (
          <>
            <IoMdDownload
              className="text-lg mr-2"
              role="img"
              aria-label="last ned ikon"
            />
            <span>
              Last ned filene
              {totalSize > 0 && ` (${formatSize(totalSize)})`}
            </span>
          </>
        )}
      </Button>
      {downloading && (
        <span className="text-sm text-gray-600" aria-live="polite">
          {progressText}
        </span>
      )}
    </div>
  );
};
