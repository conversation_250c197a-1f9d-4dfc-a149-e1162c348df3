import { ColumnDef } from "@tanstack/react-table";
import { CandidateStatusEnum } from "@/enums/CandidateStatusEnum";
import { ICandidate } from "@/interface/ICandidate";
import {
  HiOutlineArrowNarrowDown,
  HiOutlineArrowNarrowUp,
} from "react-icons/hi";
import { Button } from "@/components/ui/button";

function formatCandidateStatus(candidatestatus: CandidateStatusEnum) {
  switch (candidatestatus) {
    case CandidateStatusEnum.Levert:
      return "Levert digitalt";
    case CandidateStatusEnum.IkkeInnlogget:
    case CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert:
      case CandidateStatusEnum.InnloggetAutentisert:
      return "Ikke lastet opp";
    case CandidateStatusEnum.DokumentertFravaer:
      return "Dokumentert fravær";
    case CandidateStatusEnum.IkkeDokumentertFravaer:
      return "Ikke dokumentert fravær";
    case CandidateStatusEnum.LastetOpp:
      return "Laster opp";
    case CandidateStatusEnum.LevertManuelt:
      return "Sendes i posten";
    default:
      return "";
  }
}

export const createColumns = (showPart2: boolean): ColumnDef<ICandidate>[] => [
  {
    accessorKey: "candidateName",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Kandidatnavn
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="font-medium">{row.original.candidateName}</div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "candidateNumber",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="text-center">
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Kandidatnummer{" "}
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        </div>
      );
    },
    cell: ({ row }) => (
      <div className="text-center">{row.original.candidateNumber}</div>
    ),
    enableSorting: true,
  },
  {
    accessorFn: (row) => row.documents?.length || 0,
    id: "deliveredFiles",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <div className="text-center">
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(isSorted === "asc")}
          >
            Antall filer
            <div className="ml-2 flex items-center -space-x-[6px]">
              <HiOutlineArrowNarrowUp
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "asc" ? 4 : 2}
              />
              <HiOutlineArrowNarrowDown
                className="h-[14px] w-[14px]"
                strokeWidth={isSorted === "desc" ? 4 : 2}
              />
            </div>
          </Button>
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-center">{row.original.documents?.length || 0}</div>
      );
    },
  },
  {
    accessorKey: "deliveryStatusPart1",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          {showPart2 ? "Del 1" : "Eksamen"}

          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    cell: ({ row }) => (
      <div>{formatCandidateStatus(row.original.deliveryStatusPart1)}</div>
    ),
    enableSorting: true,
  },
  ...(showPart2
    ? [
        {
          accessorKey: "deliveryStatusPart2",
          header: ({ column }: { column: any }) => {
            const isSorted = column.getIsSorted();
            return (
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent"
                onClick={() => column.toggleSorting(isSorted === "asc")}
              >
                Del 2
                <div className="ml-2 flex items-center -space-x-[6px]">
                  <HiOutlineArrowNarrowUp
                    className="h-[14px] w-[14px]"
                    strokeWidth={isSorted === "asc" ? 4 : 2}
                  />
                  <HiOutlineArrowNarrowDown
                    className="h-[14px] w-[14px]"
                    strokeWidth={isSorted === "desc" ? 4 : 2}
                  />
                </div>
              </Button>
            );
          },
          cell: ({ row }: any) => (
            <div>
              {" "}
              {formatCandidateStatus(row.original.deliveryStatusPart2)}
            </div>
          ),
          enableSorting: true,
        },
      ]
    : []),
];

export default createColumns;
