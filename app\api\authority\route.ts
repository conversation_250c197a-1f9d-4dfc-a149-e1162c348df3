import { getValueFromRedis, setValueInRedis } from "@/app/lib/redisHelper";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { ISession } from "@/interface/ISession";
import { getAppInsightsServer } from "@/lib/appInsightsServer";
import { generateAccessToken } from "@/lib/getAccessTokenForEksamen";
import { downloadJsonFromAzure } from "@/lib/downloadJsonFromAzure";

const telemetryClient = getAppInsightsServer();
const cacheKey = "PGS:EnhetsService";

export const dynamic = "force-dynamic";

export async function GET() {
  let response = null;

  try {
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const cachedData = await getValueFromRedis(cacheKey);

    if (cachedData) {
      return NextResponse.json(JSON.parse(cachedData));
    } else {
      const accessToken = await generateAccessToken();

      try {
        response = await fetch(
          `${process.env.PASX_ENHETSSERVICE_URL}/api/fylkesmann/dato?system=pasx`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${accessToken}`,
              "Content-Type": "application/json",
            },
            cache: "no-store",
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        await setValueInRedis(cacheKey, JSON.stringify(data), 3600);
        return NextResponse.json(data);
      } catch (fetchError) {
        console.error(
          "Fetch failed, falling back to blob storage:",
          fetchError
        );

        // Fallback to blob storage
        const blobData = await downloadJsonFromAzure("enhetsservice.json");
        await setValueInRedis(cacheKey, JSON.stringify(blobData), 3600);
        return NextResponse.json(blobData);
      }
    }
  } catch (error) {
    console.error("Error:", error);

    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "GET-EnhetsService",
        statuscode: response ? (response as Response).status : 0,
        response: response
          ? await (response as Response).text()
          : "Tom respons",
      },
    });

    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
