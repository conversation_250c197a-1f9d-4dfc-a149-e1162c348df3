"use client";

import { RefreshCw } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AiOutlineInfoCircle } from "react-icons/ai";
import { useRouter } from "next/navigation";
import { useSignalRConnection } from "@/hooks/signalRProvider";

export function SignalRConnectionStatusBanner() {
  const { error } = useSignalRConnection();
  const router = useRouter();

  if (!error) return null;

  const handleRefresh = async (e: React.MouseEvent) => {
    window.location.reload();
  };

  return (
    <Alert variant="info" className="flex items-center justify-between">
      <div className="flex gap-2 items-center">
        <AiOutlineInfoCircle className="h-4 w-4" />
        <AlertDescription className="m-0">
          Vi fikk ikke automatisk oppdatert monitoren. Oppdater siden for å se
          de siste endringene.
        </AlertDescription>
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleRefresh}
        className="flex items-center gap-2"
      >
        <RefreshCw className="h-4 w-4" />
        <span className="underline-offset-4 underline decoration-2">
          Oppdater monitor
        </span>
      </Button>
    </Alert>
  );
}
