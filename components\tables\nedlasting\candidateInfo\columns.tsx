"use client";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { TestPartsEnum } from "@/enums/TestPart";
import { IExamDocument } from "@/interface/IExamDocument";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DocumentStatusEnum } from "@/enums/DocumentStatusEnum";
import { HiOutlineArrowNarrowUp, HiOutlineArrowNarrowDown } from "react-icons/hi";

function formatFileSize(bytes: number) {
  const KB = 1024;
  const MB = 1024 * KB;
  if (bytes < MB) {
    return `${(bytes / KB).toFixed(0)} kB`;
  } else {
    return `${(bytes / MB).toFixed(2)} MB`;
  }
}

function formatDateTime(dateString: string) {
  return dayjs(dateString).format("DD/MM/YYYY [kl.] HH:mm");
}

function formatTestPartId(testpartId: TestPartsEnum) {
  switch (testpartId) {
    case TestPartsEnum.Eksamen:
      return "Eksamen";
    case TestPartsEnum.EksamenDel1:
      return "Eksamen del 1";
    case TestPartsEnum.EksamenDel2:
      return "Eksamen del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "Eksamen del 1 og 2";
    default:
      return "Angi eksamensdel";
  }
}
function minimizeSuffix(filename: string) {
  const parts = filename.split(".");
  if (parts.length > 1) {
    const suffix = parts.pop();
    return parts.join(".") + "." + suffix?.toLowerCase();
  }

  return filename;
}
export const createColumns = (
  showMonitorStatus: boolean,
  showTestPartId: boolean
): ColumnDef<IExamDocument>[] => [
  {
    accessorKey: "fileName",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Anonymisert filnavn
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div tabIndex={0} className="">
        {minimizeSuffix(row.getValue("fileName"))}
      </div>
    ),
  },
  {
    accessorKey: "originalFileName",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Originalt filnavn
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div tabIndex={0} className="">
        {minimizeSuffix(row.getValue("originalFileName") || "")}
      </div>
    ),
  },
  {
    accessorKey: "fileSize",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(isSorted === "asc")}
        >
          Størrelse{" "}
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div className="">{formatFileSize(row.getValue("fileSize"))}</div>
    ),
  },
  ...(showTestPartId
    ? [
        {
          accessorKey: "testPartId",
          header: ({ column }: any) => {
            const isSorted = column.getIsSorted();
            return (
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent"
                onClick={() =>
                  column.toggleSorting(column.getIsSorted() === "asc")
                }
              >
                Eksamensdel{" "}
                <div className="ml-2 flex items-center -space-x-[6px]">
                  <HiOutlineArrowNarrowUp
                    className="h-[14px] w-[14px]"
                    strokeWidth={isSorted === "asc" ? 4 : 2}
                  />
                  <HiOutlineArrowNarrowDown
                    className="h-[14px] w-[14px]"
                    strokeWidth={isSorted === "desc" ? 4 : 2}
                  />
                </div>
              </Button>
            );
          },
          enableSorting: true,
          cell: ({ row }: any) => (
            <div className="capitalize">
              {formatTestPartId(row.getValue("testPartId"))}
            </div>
          ),
        },
      ]
    : []),

  {
    accessorKey: "timestamp",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Lastet opp
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
    cell: ({ row }) => (
      <div className="">{formatDateTime(row.getValue("timestamp"))}</div>
    ),
  },
  {
    accessorKey: "uploadedBy",
    header: ({ column }) => {
      const isSorted = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Opplastet av{" "}
          <div className="ml-2 flex items-center -space-x-[6px]">
            <HiOutlineArrowNarrowUp
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "asc" ? 4 : 2}
            />
            <HiOutlineArrowNarrowDown
              className="h-[14px] w-[14px]"
              strokeWidth={isSorted === "desc" ? 4 : 2}
            />
          </div>
        </Button>
      );
    },
    enableSorting: true,
  },
  ...(showMonitorStatus
    ? [
        {
          accessorKey: "status",
          header: ({ column }: { column: any }) => {
            const isSorted = column.getIsSorted();
            return (
              <Button
                variant="ghost"
                className="p-0 hover:bg-transparent"
                onClick={() =>
                  column.toggleSorting(column.getIsSorted() === "asc")
                }
              >
                Status
                <div className="ml-2 flex items-center -space-x-[6px]">
                  <HiOutlineArrowNarrowUp
                    className="h-[14px] w-[14px]"
                    strokeWidth={isSorted === "asc" ? 4 : 2}
                  />
                  <HiOutlineArrowNarrowDown
                    className="h-[14px] w-[14px]"
                    strokeWidth={isSorted === "desc" ? 4 : 2}
                  />
                </div>
              </Button>
            );
          },
          cell: ({ row }: any) => {
            const status = row.getValue("status");

            return (
              <div className="capitalize">
                {status === DocumentStatusEnum.Uploaded
                  ? "Opplastet"
                  : "Innlevert"}
              </div>
            );
          },
          enableSorting: true,
        },
      ]
    : []),
];

export default createColumns;
