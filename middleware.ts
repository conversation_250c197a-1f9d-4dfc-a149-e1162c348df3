import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import createIntlMiddleware from "next-intl/middleware";
import { hasAccessToPath } from "./app/lib/accessControl";
import { cookies } from "next/headers";

export const locales = ["nb", "nn"];

const intlMiddleware = createIntlMiddleware({
  locales,
  defaultLocale: "nb",
  localePrefix: "never",
});

export async function middleware(request: NextRequest) {
  // Apply internationalization middleware
  const intlResponse = intlMiddleware(request);

  // Check authentication
  const token = await getToken({ req: request });

  if (!token) {
    // Create a response redirecting to sign in with state parameter
    const signInUrl = new URL("/api/auth/signin", request.url);
    signInUrl.searchParams.set(
      "state",
      encodeURIComponent(request.nextUrl.pathname)
    );
    return NextResponse.redirect(signInUrl);
  }

  // Check authorization
  if (Array.isArray(token.role)) {
    let userRole = (await cookies()).get("userRole");
    let selectedRole: string | string[];

    userRole
      ? (selectedRole = decodeURIComponent(userRole.value))
      : (selectedRole = token.role);

    const isAuthorized = hasAccessToPath(
      selectedRole,
      request.nextUrl.pathname
    );
    if (!isAuthorized) {
      console.log("Unauthorized access attempt");
      return NextResponse.redirect(new URL("/ingentilgang", request.url));
    }
  }

  // Apply any headers or cookies from the intl middleware
  const response = NextResponse.next();
  intlResponse.headers.forEach((value, key) => {
    response.headers.set(key, value);
  });

  return response;
}

export const config = {
  matcher: ["/((?!api|_next|.*\\..*).*)"],
};
