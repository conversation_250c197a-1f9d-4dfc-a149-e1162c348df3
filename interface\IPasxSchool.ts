interface Address {
  gateadresse: string;
  postnummer: string;
  poststed: string;
  land: string;
}

interface IPasxSchool {
  navn: string;
  organisasjonsnummer: string;
  //karakteristikk: string;
  //kommunenummer: string;
  erGrunnskole: boolean;
  erVideregaendeskole: boolean;
  erPrivatSkole: boolean;
  //epost: string;
  //telefon: string;
  //webadresse: string;
  //postadresse: Address;
  besoksadresse: Address;
  skoleansvarligOrganisasjonsnummer: string;
  ansvarligFylkesmannOrganisasjonsnummer: string;
  //skoletrinnFra: number;
  //skoletrinnTil: number;
  erAktiv: boolean;
}
