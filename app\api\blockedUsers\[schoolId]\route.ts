// app/api/schools/[schoolId]/blocked-users/route.ts
import { getSetMembers } from "@/app/lib/redisHelper";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../../auth/authOptions";

// Hjelpefunksjon for å generere Redis-nøkkel
const getRedisKey = (schoolId: string) => `BlockedUsers:${schoolId}`;

// GET - Hent alle blokkerte brukere
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ schoolId: string }> }
) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { schoolId } = await params;

    if (!schoolId) {
      return NextResponse.json(
        { error: "schoolId er påkrevd" },
        { status: 400 }
      );
    }

    const key = getRedisKey(schoolId);
    const blockedUsers = await getSetMembers(key);

    return NextResponse.json(
      {
        schoolId,
        users: blockedUsers,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Feil ved henting av blokkerte brukere:", error);
    return NextResponse.json({ error: "Intern serverfeil" }, { status: 500 });
  }
}
