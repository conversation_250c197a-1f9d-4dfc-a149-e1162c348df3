"use client";
import React from "react";
import decodeQR from "jsqr";
import * as pdfjs from "pdfjs-dist";
pdfjs.GlobalWorkerOptions.workerSrc = "/pdf.worker.js";

export default function Home() {
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    const reader = new FileReader();

    reader.onload = (e) => {
      if (e.target) {
        const pdfData = e.target.result;

        const pdfjsLib = require("pdfjs-dist/build/pdf");

        /* // Load PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/node_modules/pdfjs-dist/build/pdf.worker.min.js';*/

        // Load PDF document
        pdfjsLib
          .getDocument({ data: pdfData })
          .promise.then((pdf: any) => {
            const getPagePromises = [];

            // Loop through each page of the PDF
            for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
              getPagePromises.push(pdf.getPage(pageNum));
            }
            Promise.all(getPagePromises).then((pages) => {
              pages.forEach((page, pageNum) => {
                const viewport = page.getViewport({ scale: 1 });
                const canvas = document.createElement("canvas");
                const context = canvas.getContext("2d");
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                if (context) {
                  page
                    .render({ canvasContext: context, viewport })
                    .promise.then(() => {
                      const imageData = context.getImageData(
                        0,
                        0,
                        canvas.width,
                        canvas.height
                      );
                      const code = decodeQR(
                        imageData.data,
                        canvas.width,
                        canvas.height
                      );
                    });
                }
              });
            });
          })
          .catch((error: any) => {
            console.error("Error loading PDF:", error);
          });
      }
    };

    if (file) {
      reader.readAsArrayBuffer(file);
    }
  };

  return (
    <>
      <div className="p-10 bg-stalbla">
        <h1 className="text-4xl">QR code</h1>
        <p className="mt-4">Test av QR kode scanning.</p>
      </div>
      <div className="m-6 p-5">
        <input type="file" accept=".pdf" onChange={handleFileUpload} />
      </div>
      <div></div>
    </>
  );
}
