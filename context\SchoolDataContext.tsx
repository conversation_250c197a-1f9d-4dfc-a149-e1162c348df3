"use client";

import React, {
  createContext,
  useContext,
  ReactNode,
  useMemo,
  useState,
  useEffect,
} from "react";
import { getSession } from "next-auth/react";
import { logException } from "@/lib/appInsightsClient";
import { ISchools } from "@/interface/ISchools";
import { ISession } from "@/interface/ISession";

// Define the shape of the context data
interface SchoolDataContextType {
  schools: ISchools[];
  pasxSchools: IPasxSchool[];
  enhetsservice: IAuthority[];
  skoleansvarlig: IMunicipality[];
  combinedSchoolData: IPasxSchool[];
  isLoading: boolean;
  error: Error | null;
}

// Create the context with undefined as the default value
const SchoolDataContext = createContext<SchoolDataContextType | undefined>(
  undefined
);

// Custom hook to use the SchoolDataContext
export const useSchoolData = () => {
  const context = useContext(SchoolDataContext);
  if (context === undefined) {
    throw new Error("useSchoolData must be used within a SchoolDataProvider");
  }
  return context;
};

// Generic fetcher function for data retrieval
const fetcher = async (url: string) => {
  const res = await fetch(url, { cache: "no-store" });
  if (!res.ok) {
    const error = new Error("An error occurred while fetching the data.");
    error.message = await res.text();
    throw error;
  }
  return res.json();
};

interface SchoolDataProviderProps {
  children: ReactNode;
}

// Main SchoolDataProvider component
export const SchoolDataProvider: React.FC<SchoolDataProviderProps> = ({
  children,
}) => {
  // State for storing fetched data and loading/error states
  const [session, setSession] = useState<ISession | null>(null);
  const [schools, setSchools] = useState<ISchools[]>([]);
  const [pasxSchools, setPasxSchools] = useState<IPasxSchool[]>([]);
  const [enhetsservice, setEnhetsservice] = useState<IAuthority[]>([]);
  const [skoleansvarlig, setSkoleansvarlig] = useState<IMunicipality[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Determine if PASX data should be fetched based on user role
  const shouldFetchPasxData = useMemo(() => {
    if (!session?.user.role) return false;

    //TODO: Update with correct role values
    return session.user.role.some(
      (role: string) =>
        role.startsWith("urn:udir:eksamen:fk") ||
        role.startsWith("urn:udir:eksamen:sf") ||
        role.startsWith("urn:udir:eksamen:ko") ||
        role.startsWith("urn:udir:eksamen:fm") ||
        role.startsWith("urn:udir:pgsa:administrator") ||
        role.startsWith("urn:udir:eksamen:vea") ||
        role.startsWith("urn:udir:eksamen:sa")
    );
  }, [session]);

  // Effect for fetching data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch session data
        const sessionData = (await getSession()) as ISession | null;
        setSession(sessionData);

        // Prepare fetch promises
        const fetchPromises = [fetcher("/api/mySchools")];

        // Add additional fetch promises if PASX data should be fetched
        if (sessionData && shouldFetchPasxData) {
          fetchPromises.push(fetcher("/api/pasxSchools"));
          fetchPromises.push(fetcher("/api/authority"));
          fetchPromises.push(fetcher("/api/municipality"));
        }

        // Fetch all data concurrently
        const [schoolsData, ...otherData] = await Promise.all(fetchPromises);

        // Set fetched data to state
        setSchools(schoolsData);

        if (otherData.length === 3) {
          setPasxSchools(otherData[0]);
          setEnhetsservice(otherData[1]);
          setSkoleansvarlig(otherData[2]);
        }
      } catch (err) {
        // Handle and log any errors
        setError(err as Error);
        logException(err as Error, { action: "SchoolDataContext-fetchData" });
      } finally {
        // Set loading to false regardless of success or failure
        setIsLoading(false);
      }
    };

    fetchData();
  }, [shouldFetchPasxData]);

  // Combine school data from different sources
  const combinedSchoolData = useMemo(() => {
    if (!schools || !pasxSchools) return [];

    const schoolMap = new Map<string, IPasxSchool>();

    // Populate the map with PASX school data
    pasxSchools.forEach((school) => {
      schoolMap.set(school.organisasjonsnummer, {
        navn: school.navn,
        organisasjonsnummer: school.organisasjonsnummer,
        besoksadresse: {
          postnummer: school.besoksadresse.postnummer,
          poststed: school.besoksadresse.poststed,
          gateadresse: school.besoksadresse.gateadresse,
          land: school.besoksadresse.land,
        },
        erVideregaendeskole: school.erVideregaendeskole,
        skoleansvarligOrganisasjonsnummer:
          school.skoleansvarligOrganisasjonsnummer,
        ansvarligFylkesmannOrganisasjonsnummer:
          school.ansvarligFylkesmannOrganisasjonsnummer,
        erGrunnskole: school.erGrunnskole,
        erPrivatSkole: school.erPrivatSkole,
        erAktiv: school.erAktiv,
      });
    });

    // Update or add schools from the main school list
    schools.forEach((school) => {
      if (!schoolMap.has(school.schoolId)) {
        schoolMap.set(school.schoolId, {
          navn: school.schoolName,
          organisasjonsnummer: school.schoolId,
          besoksadresse: {
            postnummer: "",
            poststed: "",
            gateadresse: "",
            land: "",
          },
          erVideregaendeskole: false,
          skoleansvarligOrganisasjonsnummer: "",
          ansvarligFylkesmannOrganisasjonsnummer: "",
          erGrunnskole: false,
          erPrivatSkole: false,
          erAktiv: true,
        });
      }
    });

    // Convert map to array and sort using Norwegian locale
    const sortedSchools = Array.from(schoolMap.values()).sort((a, b) => {
      const collator = new Intl.Collator("nb-NO", { sensitivity: "base" });
      return collator.compare(a.navn, b.navn);
    });

    return sortedSchools;
  }, [schools, pasxSchools]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo<SchoolDataContextType>(
    () => ({
      schools,
      pasxSchools,
      enhetsservice,
      skoleansvarlig,
      combinedSchoolData,
      isLoading,
      error,
    }),
    [
      schools,
      pasxSchools,
      enhetsservice,
      skoleansvarlig,
      combinedSchoolData,
      isLoading,
      error,
    ]
  );

  // Provide the context value to children components
  return (
    <SchoolDataContext.Provider value={contextValue}>
      {children}
    </SchoolDataContext.Provider>
  );
};
