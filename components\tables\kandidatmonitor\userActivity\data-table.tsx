"use client";

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { useEffect, useState } from "react";

import { ICandidate } from "@/interface/ICandidate";
import React from "react";

interface DataTableProps<IActivityLog, TValue> {
  columns: ColumnDef<IActivityLog, TValue>[];
  data: IActivityLog[];
}

export function DataTable<IRedisObject, TValue>({
  columns,
  data,
}: DataTableProps<IRedisObject, TValue>) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [candidateInfo, setCandidateInfo] = useState<ICandidate>();
  const [sorting, setSorting] = useState<SortingState>([]);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  const handleRowClick = (rowData: IRedisObject) => {
    setCandidateInfo(rowData as ICandidate);
    setDialogOpen(true);
  };

  const handleRowKeyDown = (e: React.KeyboardEvent, rowData: IRedisObject) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleRowClick(rowData);
    }
  };

  return (
    <>
      {/* Main wrapper with width constraints and forced overflow behavior */}
      <div className="w-full max-w-full overflow-x-auto block">
        {/* Table wrapper with a minimum width to ensure scrolling */}
        <div className="min-w-[1000px]">
          <Table className="w-full table-fixed">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id} className="whitespace-nowrap">
                      {header.isPlaceholder ? null : (
                        <div
                          {...{
                            "aria-sort": header.column.getIsSorted()
                              ? header.column.getIsSorted() === "desc"
                                ? "descending"
                                : "ascending"
                              : undefined,
                          }}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                        </div>
                      )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    tabIndex={0}
                    role="row"
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className="hover:bg-slate-100"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="whitespace-nowrap">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Ingen brukersesjoner
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  );
}
