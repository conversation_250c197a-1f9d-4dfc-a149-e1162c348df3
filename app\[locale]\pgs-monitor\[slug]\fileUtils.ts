import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";
import { IBulkUploadResponse } from "@/interface/IBulkUploadResponse";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { IPgsaValidateFilePayload } from "@/interface/IPgsaValidateFilePayload";
import { IRoleObject } from "@/interface/IRoleObject";

// Constants
export const MAX_SINGLE_FILE_SIZE = 40 * 1024 * 1024; // 40 MB in bytes
export const MIN_SINGLE_FILE_SIZE = 1 * 1024; // 1 kB in bytes

export const handleAcceptedFiles = async (
  files: File[],
  existingFiles: IUploadedFile[],
  addFile: (file: IUploadedFile) => Promise<void>,
  mimeTypes: IAllowedMimeTypes[],
  selectedRoleSchoolId: string,
  candidateNumber: string
): Promise<boolean> => {
  let { totalSize, totalFiles } = updateTotalSizeAndFiles(existingFiles);
  const fileNames = new Set(
    existingFiles
      .filter((file) => !file.IsRejected)
      .map((file) => file.Name.replace(/\.[^.]+$/, "").toLowerCase())
  );

  let allFilesValid = true;

  for (const file of files) {
    const validationResult = await validateFile(
      file,
      fileNames,
      totalFiles,
      totalSize,
      mimeTypes,
      selectedRoleSchoolId,
      candidateNumber
    );

    if (validationResult.isValid) {
      await addFile(validationResult.file);
      totalSize += file.size;
      totalFiles += 1;
      fileNames.add(file.name.replace(/\.[^.]+$/, "").toLowerCase());
    } else {
      await addFile(validationResult.file);
      allFilesValid = false;
    }
  }

  return allFilesValid;
};

export const validateFile = async (
  file: File,
  existingFileNames: Set<string>,
  totalFiles: number,
  totalSize: number,
  mimeTypes: IAllowedMimeTypes[],
  selectedRoleSchoolId: string,
  candidateNumber: string
): Promise<{ isValid: boolean; file: IUploadedFile }> => {
  const fileName = file.name.replace(/\.[^.]+$/, "").toLowerCase();
  const errors: string[] = [];

  // Check for duplicate file names
  if (existingFileNames.has(fileName)) {
    errors.push("En fil med dette navnet er allerede lastet opp.");
  }

  // Check file size
  if (file.size > MAX_SINGLE_FILE_SIZE) {
    errors.push("Filen overstiger maksimal filstørrelse på 40 MB.");
  }
  if (file.size < MIN_SINGLE_FILE_SIZE) {
    errors.push("Filen er for liten. Minimum filstørrelse er 1 kB.");
  }

  // Check file extension
  const fileExtension = `.${file.name.split(".").pop()}`;
  if (
    !mimeTypes.some(
      (type) => type.FileExtension.toLowerCase() === fileExtension.toLowerCase()
    )
  ) {
    errors.push("Filen har en ugyldig filtype");
  }

  if (errors.length > 0) {
    return {
      isValid: false,
      file: await createRejectedFile(file, errors),
    };
  }

  // If no errors so far, proceed with PGSA validation
  return pgsaFileValidator(
    file,
    mimeTypes,
    selectedRoleSchoolId,
    candidateNumber
  );
};

const pgsaFileValidator = async (
  file: File,
  mimeTypes: IAllowedMimeTypes[],
  selectedRoleSchoolId: string,
  candidateNumber: string
): Promise<{ isValid: boolean; file: IUploadedFile }> => {
  const body: IPgsaValidateFilePayload = {
    CandidateNumber: candidateNumber,
    FileName: file.name,
    SchoolId: selectedRoleSchoolId,
    MimeType: getMimeTypeFromFile(file, mimeTypes) || "",
    Size: file.size,
  };

  try {
    const response = await fetch(`${window.location.origin}/api/uploadMonitor`, {
      method: "POST",
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text() || "Ukjent feil under validering av fil";
      return {
        isValid: false,
        file: await createRejectedFile(file, [errorText?.replace(/"/g, "")]),
      };
    } else {
      const result: IBulkUploadResponse = await response.json();
      return {
        isValid: true,
        file: {
          File: file,
          TestPartId: result.testParts === 1 ? 1 : 0,
          UploadedDate: dayjs(),
          Name: file.name,
          SubjectCode: result.subjectCode,
          Size: file.size,
          FileGuid: result.documentCode,
          IsRejected: false,
          IsLoading: true,
          UploadFinished: false,
          Groupcode: result.groupCode,
          Candididate: candidateNumber,
          CandidateName: result.candidateName,
          candidateRegistrationId: result.candiateRegistrationId, // Store for logActivity calls
          Delivered: false,
          IsSubmitting: false,
          IsDeleting: false,
          SchoolId: selectedRoleSchoolId,
          UploadedBy: "Skoleadmin/eksamensvakt",
        },
      };
    }
  } catch (error) {
    console.error("Ikke validert", error);
    return {
      isValid: false,
      file: await createRejectedFile(file, ["Feil under validering av fil"]),
    };
  }
};

export const handleRejectedFile = async (
  rejection: { file: File; errors: any[] },
  addFile: (file: IUploadedFile) => Promise<void>
): Promise<void> => {
  await addRejectedFile(
    rejection.file,
    rejection.errors.map((error) => error.message),
    addFile
  );
};

export const addRejectedFile = async (
  file: File,
  errors: string[],
  addFile: (file: IUploadedFile) => Promise<void>
): Promise<void> => {
  const rejectedFile = await createRejectedFile(file, errors);
  await addFile(rejectedFile);
};

export const createRejectedFile = async (
  file: File,
  errors: string[]
): Promise<IUploadedFile> => {
  return {
    File: file,
    TestPartId: 0,
    UploadedDate: dayjs(),
    Name: file.name,
    Size: file.size,
    FileGuid: uuidv4(),
    Errors: errors,
    IsRejected: true,
    IsLoading: false,
    UploadFinished: true,
    Groupcode: "",
    Candididate: "",
    Delivered: false,
    IsSubmitting: false,
    IsDeleting: false,
    SchoolId: "",
    UploadedBy: "Skoleadmin/eksamensvakt",
  };
};

export const fileValidator = (
  file: File,
  mimeTypes: IAllowedMimeTypes[],
  uploadedFiles: IUploadedFile[],
  selectedRole: IRoleObject | undefined
): any[] | null => {
  // Check if file is already in error state
  const isAlreadyRejected = uploadedFiles.some(
    (uploadedFile) => uploadedFile.Name === file.name && uploadedFile.IsRejected
  );

  if (isAlreadyRejected) {
    return null;
  }

  const errors: { message: string; code: string }[] = [];

  // Check if file is defined and has a name
  if (!file || typeof file.name !== "string") {
    errors.push({
      message: "Ugyldig fil: Filen mangler navn eller er ikke definert.",
      code: "INVALID_FILE",
    });
    return errors;
  }

  const fileName = file.name.replace(/\.[^.]+$/, "");
  const regex = /^\d{3}[A-Za-z]{3}-[vVhH]-\d{2,3}$/;
  const fileExtension = `.${file.name.split(".").pop()}`;

  if (!selectedRole?.selectedSchoolId) {
    errors.push({
      message: "Rollen du har valgt har ikke tilknyttet en skole",
      code: "ROLE_MISSING_SCHOOL_ID",
    });
  }

  if (
    !mimeTypes.some(
      (type) => type.FileExtension.toLowerCase() === fileExtension.toLowerCase()
    )
  ) {
    errors.push({
      message: "Filen har en ugyldig filtype",
      code: "FILE_TYPE_NOT_SUPPORTED",
    });
  }

  if (typeof file.size !== "number") {
    errors.push({
      message: "Ugyldig fil: Filstørrelsen kan ikke bestemmes.",
      code: "INVALID_FILE_SIZE",
    });
  } else {
    if (file.size > MAX_SINGLE_FILE_SIZE) {
      errors.push({
        message: "Filen overstiger maksimal filstørrelse på 40 MB.",
        code: "FILE_TOO_LARGE",
      });
    }

    if (file.size < MIN_SINGLE_FILE_SIZE) {
      errors.push({
        message: "Filen er for liten. Minimum filstørrelse er 1 kB.",
        code: "FILE_TOO_SMALL",
      });
    }
  }

  return errors.length > 0 ? errors : null;
};
export const deliverFiles = async (
  uploadedFiles: IUploadedFile[],
  deliverFile: (file: IUploadedFile) => Promise<void>,
  selectedSchoolId: string | undefined
): Promise<{ fileMissingExamPart: boolean; fileNotChecked: boolean }> => {
  let fileError = {
    fileMissingExamPart: false,
    fileNotChecked: false,
  };

  for (const file of uploadedFiles) {
    if (
      !file.IsRejected &&
      !file.Delivered &&
      file.SchoolId === selectedSchoolId
    ) {
      if (file.TestPartId === 0) {
        fileError.fileMissingExamPart = true;
      } else if (!file.Checked) {
        fileError.fileNotChecked = true;
      } else {
        try {
          await deliverFile(file);
        } catch (error) {
          console.error(`Error delivering file ${file.Name}:`, error);
        }
      }
    }
  }

  return fileError;
};


export const getCandidateNumber = (file: File): string => {
  const hyphenIndex = file.name.lastIndexOf("-");
  return hyphenIndex !== -1 ? file.name.substring(0, hyphenIndex) : "";
};

export const getMimeTypeFromFile = (
  file: File,
  mimeTypes: IAllowedMimeTypes[]
): string | undefined => {
  const fileExtension = `.${file.name.split(".").pop()}`;
  return mimeTypes.find(
    (type) => type.FileExtension.toLowerCase() === fileExtension.toLowerCase()
  )?.MimeType;
};

export const updateTotalSizeAndFiles = (
  uploadedFiles: IUploadedFile[]
): { totalSize: number; totalFiles: number } => {
  const filteredFiles = uploadedFiles.filter(
    (file) => !file.IsRejected && !file.Delivered
  );
  const totalSize = filteredFiles.reduce((sum, file) => sum + file.Size, 0);
  const totalFiles = filteredFiles.length;
  return { totalSize, totalFiles };
};

export const getFileCount = (
  uploadedFiles: IUploadedFile[],
  tab: string
): string => {
  const count = uploadedFiles.filter(
    (file) =>
      (tab === "opplastedeFiler" ? !file.Delivered : file.Delivered) &&
      !file.IsRejected
  ).length;
  return count > 0 ? ` (${count})` : "";
};
