import React from "react";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ErrorDisplayProps {
  errors: {
    message: string;
    type: string;
  }[];
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ errors }) => {
  if (errors.length === 0) return null;

  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Feil ved henting av data:</AlertTitle>
      <AlertDescription>
        <span className="font-semibold"></span>
        <ul className="list-disc list-inside">
          {errors.map((error, index) => (
            <li key={index} className="ml-2">
              {error.type}: {error.message}
            </li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
};

export default ErrorDisplay;
