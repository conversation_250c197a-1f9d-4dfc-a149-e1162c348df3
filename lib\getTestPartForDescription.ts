import { TestPartsEnum } from "@/enums/TestPart";

export function getTestPartForDescription(testPartID: number) {
  switch (testPartID) {
    case TestPartsEnum.Eksamen:
      return "eksamen";
    case TestPartsEnum.EksamenDel1:
      return "del 1";
    case TestPartsEnum.EksamenDel2:
      return "del 2";
    case TestPartsEnum.EksamenDel1ogDel2:
      return "del 1 og del 2";
    default:
      return "Ikke angitt";
  }
}
