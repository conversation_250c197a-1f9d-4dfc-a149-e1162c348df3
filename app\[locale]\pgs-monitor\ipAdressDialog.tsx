"use client";
import React, { useEffect, useState, useCallback } from "react";

import { Button } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { ICandidate } from "@/interface/ICandidate";
import { IExamPaperInternal } from "@/interface/IExamPaperInternal";
import { useToast } from "@/components/ui/use-toast";
import { ImSpinner2 } from "react-icons/im";
import { IoMdDownload } from "react-icons/io";
import { Dialog } from "@/components/ui/dialog";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { DataTable } from "@/components/tables/kandidatmonitor/userSessionHistory/data-table";
import columns from "@/components/tables/kandidatmonitor/userSessionHistory/columns";

interface CandidateInfoModalProp {
  redisObject: IRedisObject[];
  candidate: ICandidate;
}

export default function IpAdressDialog({
  redisObject,
  candidate,
}: CandidateInfoModalProp) {
  return (
    <div className="mt-8">
      <h2 id="candidate-info-title" className="text-xl mb-5">
        Innloggingssesjoner og IP-adresser for {candidate.candidateName} (
        {candidate.candidateNumber})
      </h2>
      <div className="flex flex-col gap-8">
        <div
          role="region"
          aria-label="Kandidatens besvarelser"
          className="overflow-x-auto"
        >
          <DataTable
            data={redisObject}
            candidate={{
              candidateName: candidate.candidateName,
              candidateNumber: candidate.candidateNumber,
              candidateRegistrationId: candidate.candidateRegistrationId,
              userId: candidate.userId
            }}
          />
        </div>
        <p>
          Tabellen over viser alle innloggingssesjoner som kandidaten har hatt i
          PGS så langt i dag.
        </p>
      </div>
    </div>
  );
}
