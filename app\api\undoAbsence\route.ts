"use server";

import { getServerSession } from "next-auth/next";
import { getAccessToken } from "@/lib/getAccessToken";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { NextRequest, NextResponse } from "next/server";
import { getClientIp } from "@/lib/getClientIp";
import { getAppInsightsServer } from "@/lib/appInsightsServer";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const REDIS_TTL = 60 * 60 * 10; // 10 timer
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

const telemetryClient = getAppInsightsServer();

interface IUndoAbsencePayload {
  userId: string;
  username: string;
  ipAddress: string;
  schoolId: string;
  candidateNumber: string;
}

export async function POST(request: NextRequest) {
  let body: IUndoAbsencePayload = await request.json();
  let response = null;

  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const payload = {
      username: session.user.userInfo.userId,
      ipAddress: await getClientIp(request),
    };

    // Validering av input
    if (!body.userId || !body.schoolId || !body.candidateNumber) {
      return NextResponse.json(
        { message: "Mangler påkrevde felt i forespørselen" },
        { status: 400 }
      );
    }

    // Hent access token
    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    if (!accessToken) {
      return NextResponse.json(
        { error: "Failed to obtain access token" },
        { status: 500 }
      );
    }

    // Gjør API-kallet
    const apiUrl = `${PgsaApiUrl}/api/Monitor/${body.userId}/undo-absence`;

    response = await fetch(apiUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error("Error in undoing absence to backend:", errorData);
      return NextResponse.json(errorData, { status: response.status });
    }

    telemetryClient?.trackEvent({
      name: "UndoAbsenceSuccess",
      properties: {
        candidateNumber: body.candidateNumber,
        schoolId: body.schoolId,
      },
    });

    const responseData = await response.json();
    return NextResponse.json({
      ...responseData,
      message: "Fravær fjernet og attendance status oppdatert",
    });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "undo absence",
        payload: body,
        statuscode: response ? response.status : 0,
        response: response ? await response.text() : "Tom respons",
      },
    });
    console.error("Error in undo absence:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
