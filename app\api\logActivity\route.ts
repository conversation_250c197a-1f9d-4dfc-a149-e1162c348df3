// app/api/signalr/route.ts

import AuditLogService from "@/db/services/auditLogService";
import { NextRequest, NextResponse } from "next/server";
import { getUserLoggingInfo } from "@/lib/getUserLoggingInfo";
import { IActivityLogV2 } from "@/interface/IActivityLogV2";
import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/authOptions";
import { sendMessage, type ServiceBusMessage } from "@/lib/serviceBusClient";
import { headers } from "next/headers";
import Bowser from "bowser";
import { AuditLogColumns } from "@/enums/AuditLogColumns";

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc); // Extend dayjs with UTC plugin
dayjs.locale("nb"); // Set locale to Norwegian

async function sendAuditLogToServiceBus(
  data: any,
  userAgent: string
): Promise<void> {
  const browser = Bowser.getParser(userAgent);
  const auditLog: Record<string, any> = {};

  Object.assign(auditLog, {
    [AuditLogColumns.KANDIDAT_PAMELDING_ID]: data.candidateRegistrationId,
    [AuditLogColumns.TIMESTAMP]: dayjs.utc().toISOString(),
    [AuditLogColumns.ROLLE]: data.Role,
    [AuditLogColumns.KANDIDAT_NR]: data.candidateNumber,
    [AuditLogColumns.KANDIDAT_FORNAVN]: data.candidateFirstName,
    [AuditLogColumns.KANDIDAT_ETTERNAVN]: data.candidateLastName,
    [AuditLogColumns.IP]: data.ip,
    [AuditLogColumns.DEVICE]: browser.getPlatformType(),
    [AuditLogColumns.OS]: browser.getOSName(),
    [AuditLogColumns.BROWSER]: browser.getBrowserName(),
    [AuditLogColumns.BROWSER_EDITION]: browser.getBrowserVersion(),
    [AuditLogColumns.OPERATION_ID]: data.OperationId || null,
    [AuditLogColumns.EKSAMENSDEL]: data.TestPartId || null,
    [AuditLogColumns.FILNAVN]: data.FileName || null,
    [AuditLogColumns.PARAMETERS]: data.Parameters || null,
  });

  const message = JSON.stringify(auditLog);

  // Use robust sendMessage API with retry logic and circuit breaker
  const serviceBusMessage: ServiceBusMessage = {
    body: message,
    contentType: "application/json",
    messageId: `audit-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    subject: "AuditLog",
    applicationProperties: {
      operationId: auditLog[AuditLogColumns.OPERATION_ID],
      kandidatNr: auditLog[AuditLogColumns.KANDIDAT_NR],
      timestamp: auditLog[AuditLogColumns.TIMESTAMP]
    }
  };

  await sendMessage(serviceBusMessage);
}

export async function POST(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const payload: IActivityLogV2 = await request.json();

    const baseData = await getUserLoggingInfo(
      payload.userId,
      payload.TestPartId ? payload.TestPartId : "",
      payload.FileName ? payload.FileName : "",
      payload.Role,
      session.user.userInfo.name,
      payload.candidateRegistrationId,
      payload.candidateNumber,
      payload.candidateFirstName,
      payload.candidateLastName
    );

    const logActivityService = AuditLogService.getInstance();

    var data = logActivityService.buildAuditLogData(
      baseData,
      payload.OperationId,
      payload.Parameters
    );

    // Send to Service Bus - wait for confirmation
    const userAgent = (await headers()).get("user-agent") || "Unknown User Agent";
    await sendAuditLogToServiceBus(data, userAgent);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error logging activity:", error);

    // Return error if logging fails - this ensures we know about logging failures
    return NextResponse.json(
      {
        success: false,
        error: "Failed to log activity",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
