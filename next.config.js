import rehypeSlug from "rehype-slug";
import withMD<PERSON> from "@next/mdx";
import remarkGfm from "remark-gfm";

const withMDXConfigured = withMDX({
  extension: /\.(md|mdx)$/,
  options: {
    rehypePlugins: [rehypeSlug],
    remarkPlugins: [remarkGfm],
  },
});

const nextConfig = {
  reactStrictMode: true,
  // Removed distDir to use default .next for better Azure App Service compatibility
  output: "standalone",
  pageExtensions: ["js", "jsx", "md", "mdx", "ts", "tsx"],
  
  // Optimizations for Azure App Service
  poweredByHeader: false,
  generateEtags: false,
  compress: false, // Let Azure Front Door handle compression
  
  webpack: (config) => {
    config.resolve.alias.canvas = false;
    return config;
  },
  
  
  
  // Headers for better static file serving
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  
  env: {
    // APP_VERSION: process.env.APP_VERSION,
    CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING:
      process.env.CUSTOMCONNSTR_PGS_BLOB_CONNECTIONSTRING,
  },
};

export default withMDXConfigured(nextConfig);
