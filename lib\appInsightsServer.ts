import * as appInsights from "applicationinsights";

// Bruk en global variabel for å sikre at Application Insights kun initieres én gang
declare global {
  // For å forhindre TypeScript-feil
  var appInsightsClient: appInsights.TelemetryClient | undefined;
}

export function getAppInsightsServer(): appInsights.TelemetryClient {
  if (!global.appInsightsClient) {
    const connectionString =
      process.env.CUSTOMCONNSTR_APPINSIGHTS_CONNECTIONSTRING || "";

    try {
      if (!appInsights.defaultClient) {
        appInsights
          .setup(connectionString)
          .setAutoDependencyCorrelation(true)
          .setAutoCollectRequests(true)
          .setAutoCollectExceptions(true)
          .setAutoCollectDependencies(true)
          .setAutoCollectConsole(true)
          .setUseDiskRetryCaching(true);

        // Start Application Insights bare én gang
        if (!appInsights.defaultClient) {
          appInsights.start();
        }
      }

      global.appInsightsClient = appInsights.defaultClient;

      if (!global.appInsightsClient) {
        throw new Error("Failed to initialize Application Insights client.");
      }
    } catch (error) {
      console.error("Error initializing Application Insights:", error);
      throw error;
    }
  }

  return global.appInsightsClient;
}
