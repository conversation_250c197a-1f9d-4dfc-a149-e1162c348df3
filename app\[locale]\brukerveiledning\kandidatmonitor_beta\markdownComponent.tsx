"use client";

import React from "react";

export default function Home() {
  return (
    <>
      <div className="prose">
        <section>
          <h2 id="innledning">Innledning</h2>
          <p>
            Takk for at du prøver ut betaversjonen av PGS-monitor. Med
            betaversjon mener vi her en prøveversjon med begrenset
            funksjonalitet. Denne veiledningen beskriver hvordan betaversjonen
            fungerer og hva som foreløpig ikke er mulig å gjøre i den nye
            PGS-monitor.
          </p>
          <p>
            Tilbakemeldingene fra utprøvingen vil vi ta med oss i videre
            utvikling med mål om å lansere ny PGS-monitor før våreksamen
            2025. Dere kan gjerne sende tilbakemeldingene direkte til
            utviklingsteamet på e-post
            <a className="ml-1" href="mailto:<EMAIL>">
              <EMAIL>
            </a>
            . Utprøving er kun avtalt for et fåtall privatistkontorer og skoler.
            Dersom man ønsker assistanse eller har spørsmål til betaversjonen
            oppfordrer vi til å kontakte utviklingsteamet på e-post.
          </p>
          <p>
            Generelt sett er ambisjonen for ny PGS-monitor å ivareta
            funksjonaliteten og informasjonen i gamle monitoren samtidig som vi
            ser hen til EPS-monitior i PAS-eksamen når det kommer til design og
            uttrykk. PGS-monitor for PGS er imidlertid noe mer komplisert
            enn EPS-monitoren pga. todelt eksamen og fordi det er flere valg.
            Dette er mye info å vise per kandidat, noe som gjør det utfordrende
            med tanke på antall kolonner og hva man har plass til på en vanlig
            skjerm på en bærbar pc.
          </p>
        </section>
        <section>
          <h2 id="funksjonalitet-som-mangler-i-betaversjonen">
            Funksjonalitet som mangler i betaversjonen{" "}
          </h2>
          <p>
            Det antas at leseren er kjent med virkemåten til den{" "}
            <a
              className="ml-1"
              href="https://pgs-admin.udir.no/brukerveiledning/pgs-monitor"
            >
              amle kandidatmontioren.
            </a>
            g
          </p>
          <p>
            Per primo november mangler følgende funksjonalitet i betaversjonen:
          </p>
          <ul>
            <li>mulighet for å registrere oppmøte, inkludert fravær</li>
            <li>mulighet for å gi kandidatene digitalt tilgang til PGS</li>
            <li>
              mulighet for å angi at en kandidat skal gjennomføre på papir for
              én-del eksamen og del 2 for todelt
            </li>
            <li>mulighet for å levere på vegne av kandidat</li>
            <li>
              mulighet for å åpne for ny levering slik at en kandidat kan levere
              på nytt
            </li>
            <li>
              mulighet for å sette kandidatstatus "Sends i posten"
            </li>
            <li>mulighet for å vise kandidaten sin aktivitetslogg</li>
            <li>varsling ved avvikende IP-adresse </li>
            <li>
              mulighet for å se totaloversikt for alle kandidatene på skolen
            </li>
          </ul>
          <p>
            I tillegg er det begrensninger på hvordan IP-adresser logges. IP
            logges foreløpig kun ved innloggingstidspunktet.
          </p>
        </section>
        <section>
          <h2 id="funksjonalitet-i-betaversjonen">
            Funksjonalitet i betaversjonen
          </h2>
          <p>
            Betaversjonen oppdaterer seg foreløpig hvert 30. sekund, men
            ambisjonen er at dette skal skje fortløpende. Når PGS-monitor
            oppdaterer seg hentes status på alle kandidater i PGS-monitor
            samt info om online-status, kandidatens tilgang og eventuelle filer
            som kandidaten har lastet opp. Dersom det for eksempel settes
            Ikke-dokumentert fravær eller Sendes i posten i
            gamle PGS-monitor, vil dette vises i betaversjonen etter
            oppdateringen.
          </p>
          <p>
            Når en kandidat logger inn og kommer til autoriseringssiden vil
            online-status lyse grønt for å vise at kandidaten har en aktiv
            tilkobling til PGS. Kandidatstatus vil også oppdateres. PGS vil også
            registrere kandidatens innloggingssesjon og IP ved pålogging.
            Innloggingssesjoner og tilhørende IP-adresser kan vises ved å trykke
            på "vis ip". Når eksamensvakten gir kandidaten tilgang i gamle
            PGS-monitor vil hengelås-ikonet åpnes og lyse grønt,. Dette
            indikerer at kandidaten har fått tilgang til PGS. Så snart en
            kandidat laster opp en fil, vil denne vises i monitoren ved at et
            ark-ikon vises i kolonnen Besvarelse. Når kandidaten har levert
            eksamen vil kandidatstatus endres til "Levert digitalt" og
            hengelås-ikonet vil oppdateres til å være et rødt låst ikon. Dette
            indikerer at digitalt tilgang er stengt for kandidaten pga.
            kandidatstatus. Digital tilgang vil være stengt dersom
            kandidatstatus er Dokumentert fravær, Ikke-dokumentert fravær,
            Levert digitalt og Sendes i posten. Svart hengelås
            angir at kandidaten ikke har fått digital tilgang ennå, men at det
            er mulig å gi dette via gamle PGS-monitor ("Skal levere
            digitalt").
          </p>
          <p>
            Det er mulig å filtrere på kandidatnummer eller kandidatens navn.
            Det er også mulig å filtere på én eller flere kandidatgrupper eller
            på én eller flere kandidatstatuser. Når man filtrer på
            kandidatstatus vil man for todelte eksamener få treff både mot del 1
            og del 2.
          </p>
          <p>
            I tabellen over kandidater er det mulig å filtrere på de ulike
            kolonneoverskriftene. Dersom man sorterer på "Online" vil sortere på
            status offline og online . Dersom man filtrerer på "Gruppe/Fag" vil
            man sortere ut fra navnet på kandidatgruppen. Dette begynner i de
            aller fleste tilfeller på fagkoden som kandidaten har eksamen i.
            Dersom man sorterer på "Besvarelse" vil man sortere på kandidater
            som har en eller flere filer lastet opp og/eller levert.
          </p>
          <p>
            Merk at det går flere sekunder mellom hver gang PGS sjekker om
            kandidaten er online. Dersom kandidaten lukker nettleseren hvor hen
            er innlogget, eller setter pc-en sin i dvalemodus vil online-status
            etter hvert settes til Offline. Det tar noen minutter før PGS setter
            online-status til Offline fordi PGS sjekker flere ganger for å være
            sikker på at det ikke var snakk om et enkeltstående kortvarig
            utfall.
          </p>
          <p>
            I betaversjonen er enkelte statustyper navngitt forskjellig. I
            tabellen under ser du en mapping mellom statusene i gamle
            PGS-monitor opp mot statusene slik de vises i betaversjonen.
          </p>
          <table className="table-auto border-collapse w-full">
            <thead>
              <tr>
                <th className="border px-4 py-2">Gamle PGS-monitor</th>
                <th className="border px-4 py-2">Betaversjonen</th>
                <th className="border px-4 py-2">Forskjell</th>
                <th className="border px-4 py-2">Kommentar</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border px-4 py-2">Gjennomføres på papir</td>
                <td className="border px-4 py-2">Kun på papir</td>
                <td className="border px-4 py-2">✔️</td>
                <td className="border px-4 py-2">
                  Ønsker å gjøre statusnavnene mer kortfattet og spare
                  horisontal plass. Benyttes kun for del 1 for todelt som kun
                  gjennomføres på papir.
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Ikke innlogget</td>
                <td className="border px-4 py-2">-</td>
                <td className="border px-4 py-2">✔️</td>
                <td className="border px-4 py-2">
                  Det har vært flere klager på at statusen "Ikke innlogget" ikke
                  er presis da den kun vises inntil kandidaten har fått en annen
                  status. Eksempelvis: selv om kandidaten logger seg av, mens
                  hen venter på tilgang så vil ikke statusen gå tilbake til
                  "Ikke innlogget". Det oppfattes derfor som en default-status
                  eller null-status inntil det skjer en aktivitet som setter en
                  status. I betaversjonen beskriver vi den som "ingen status" i
                  filteret.
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Autorisert</td>
                <td className="border px-4 py-2">Autorisert</td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2"></td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Venter på tilgang</td>
                <td className="border px-4 py-2">Venter på tilgang</td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2"></td>
              </tr>
              <tr>
                <td className="border px-4 py-2">
                  Venter på eksamensstart (kl. tt:mm)
                </td>
                <td className="border px-4 py-2">Venter på eksamensstart</td>
                <td className="border px-4 py-2">✔️</td>
                <td className="border px-4 py-2">
                  Foreløpig har ikke betaversjonen støtte for å vise tidspunkt
                  for eksamensstart.
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Startet</td>
                <td className="border px-4 py-2">Startet</td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2"></td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Laster opp</td>
                <td className="border px-4 py-2">Laster opp</td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2"></td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Levert digitalt</td>
                <td className="border px-4 py-2">Levert digitalt</td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2"></td>
              </tr>
              <tr>
                <td className="border px-4 py-2">
                  Sendes i posten
                </td>
                <td className="border px-4 py-2">
                  Sendes i posten
                </td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2">
                  Kun forskjell ved at det i betaversjonen er lagt inn et
                  linjeskift for å spare horisontal plass.
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Ikke-dokumentert fravær</td>
                <td className="border px-4 py-2">Ikke-dokumentert fravær</td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2"></td>
              </tr>
              <tr>
                <td className="border px-4 py-2">Dokumentert fravær</td>
                <td className="border px-4 py-2">Dokumentert fravær</td>
                <td className="border px-4 py-2"></td>
                <td className="border px-4 py-2"></td>
              </tr>
            </tbody>
          </table>
          {/*   <p>
            I betaversjonen er det mulig å levere en fil på vegne av kandidaten.
            Dette gjør man ved å klikke knappen "Lever for kandidat". Merk at
            knappen ikke er tilgjengelig dersom kandidaten er registrert med
            fravær eller dersom kandidaten har status "Sendes i posten".
          </p>
          <p>
            Siden "Lever for kandidat" har mange likhetstrekk med
            <a
              className="ml-1"
              href="https://pgs-admin.udir.no/brukerveiledning/gruppeopplaster"
            >
              gruppeopplasteren
            </a>
            . Det er noen vesentlige forskjeller å merke seg:
          </p>
          <ul>
            <li>Siden gjelder for kun én spesifikk kandidat.</li>
            <li>
              I fanene "Opplastede filer" og "Leverte filer" vises alle filer
              som er lastet opp av kandidaten og av eksamensvaktene (merk at for
              todelte eksamener forutsetter det foreløpig at eksamensdel er
              angitt).
            </li>
            <li>
              Man må angi at filen er sjekket for å kunne levere filen (dette er
              tilsvarende som i den gamle løsningen).
            </li>
            <li>
              Informasjonen om filene som vises i tabellene i fanene er
              tilpasset punktene over.
            </li>
          </ul>
          <p>
            Merk at fanen "Filer med feil" kun vil vise filer som har feilet for
            deg. Den vil altså ikke vise filer som har feilet for kandidaten
            eller andre eksamensvakter.
          </p>
        </section>
        <section>
          <h2 id="kjente-mangler-i-betaversjonen">
            Kjente mangler i betaversjonen
          </h2>
          <p>
            Med kjente mangler mener vi mangler ved funksjonaliteten som er
            utviklet i betaversjonen – ikke funksjonalitet som ikke er utviklet
            ennå.
          </p>

          <div>
            <h3 id="lever-for-kandidat">Lever for kandidat</h3>
            <p>
              Dersom kandidaten har status Startet eller Laster opp, kan
              eksamensvakten likevel levere en fil på vegne av kandidaten.
              Eksamensvakten bør da velge "Lever markerte" for å unngå å levere
              filen som kandidaten har lastet opp. Status vil i dag gå over til
              Levert digitalt selv om man velger "Lever markerte". Dette vil
              føre til at kandidaten ved oppdatering i sin nettleser vil komme
              til kvitteringssiden, og derfor ikke få ferdigbehandlet sin fil.
              Her er planen at "Lever markerte" ikke skal sette status til
              "Levert digitalt" dersom kandidaten har status "Startet" eller
              "Laster opp". Hvis kandidaten ikke fikk levert sin fil under
              eksamen bør man åpne for ny levering i gamle PGS-monitor slik
              at kandidaten får ferdigbehandlet filen(e) som hen har lastet opp.
            </p>
          </div> */}
        </section>
        <section>
          <h2 id="videre-utvikling-av-ny-PGS-monitor">
            Videre utvikling av ny PGS-monitor
          </h2>
          <p>
            {" "}
            Utviklingen av PGS foregår etter prinsippene om smidig utvikling.
            Det vil si at idéene som presentes under er gjenstand for endring
            etter hvert som vi tester nyutviklet funksjonalitet i den nye
            PGS-monitor. Dersom eksempelvis brukertesting, uu-testing
            eller ytelsestesting viser at det er behov for endringer, vil vi
            gjøre dette fortløpende. Men når vi kommer til våreksamen 2025 skal
            vi ha en stabil løsning som ikke skal endres under eksamen. Idéene
            og planene under er ikke uttømmende, men det som vi anser som mest
            relevant for eksamensvakter på det nåværende tidspunkt.
          </p>{" "}
          <p>
            Vi planlegger en ny kolonne for Oppmøte/Gjennomføring som er tenkt å
            komme til venstre for "Del 1". Her vil eksamensvakten kunne
            registrere at kandidaten har møtt eller at hen har Ikke-dokumentert
            eller Dokumentert fravær. Vi ser også på muligheten for å angi at en
            kandidat skal gjennomføre på papir for én-del eksamen og del 2 for
            todelt. Dette vil medføre at digital tilgang blir stengt. Oppmøte er
            et attributt tilknyttet kandidatens eksamen ikke den enkelte
            eksamensdel (slik det kan se ut til for todelt eksamen i dag).
            Derfor er planen og ikke lenger vise fravær i kolonnene for del 1 og
            del 2 da dette vil dekkes av den nye kolonnen for oppmøte.
          </p>
          <p>
            Helt til venstre i PGS-monitor planlegger vi en kolonne for
            varsler. For eksempel kan det varsles når kandidaten ber om tilgang
            på autoriseringssiden eller dersom kandidaten har avvikende IP.
          </p>
          <p>
            Våre undersøkelser og sikkerhetsgjennomganger viser behov for å
            styrke sikkerheten knyttet til tilgangsstyring for kandidater for
            PGS. En kandidat kan ha mange innloggingssesjoner og det bør være
            mulig å se hvilken sesjon som man gir tilgang til. Det er ikke
            usannsynlig at enkelte kandidater kan få hjelp av utenforstående som
            også har en innloggingssesjon mot PGS. Det bør også være mulig å
            fjerne alle tilganger for alle sesjoner som en kandidat har mot PGS.
            Vi ønsker videre at dagspassord kun skal brukes som en
            backup-løsning da sikkerheten for denne vurderes som svakere enn
            tilgangsstyring via PGS-monitor.
          </p>
          <p>
            Vi ser at det blir trangt om plassen i ny PGS-monitor da vi har
            planlagt ytterligere kolonner. Derfor er planen å flytte info om IP
            og aktivitetsloggen bak en lenke som vises for kandidatens navn. Det
            blir tilsvarende som det er løst i gamle PGS-monitor. Dvs. at vi
            planlegger å fjerne kolonnen IP-adresse for å gjøre plass til andre
            kolonner.
          </p>
          <p>
            Vedr. avvikende IP så har vi ikke kommet så langt i utviklingen
            ennå. Vi vil utforske mulighetene i løpet av vinteren for å vise
            dette. Vi vil også utforske mulighetene for å hente ut IP på flere
            tidspunkter enn innloggingstidspunktet.
          </p>
          <p>
            Det er planlagt en aktivitetslogg tilsvarende som den gamle
            monitoren. Men det er uklart om en første versjon vil kunne tilby
            like mange detaljer som den gamle. Det er også mulig at vi i en
            begrenset periode henter aktivitetsloggen fra gamle
            PGS-monitor over til den nye PGS-monitor.
          </p>
        </section>
      </div>
    </>
  );
}
