import { getValueFromRedis, setValueInRedis } from "@/app/lib/redisHelper";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { ISession } from "@/interface/ISession";
import { getAppInsightsServer } from "@/lib/appInsightsServer";
import { generateAccessToken } from "@/lib/getAccessTokenForEksamen";
import { downloadJsonFromAzure } from "@/lib/downloadJsonFromAzure";

const telemetryClient = getAppInsightsServer();
const cacheKey = "PGS:Skoler";
const EksamenApiUrl = process.env.EKSAMENAPI_URL;

export const dynamic = "force-dynamic";

type ApiResponse = {
  status: number;
  text: () => Promise<string>;
  json: () => Promise<any>;
};

export async function GET() {
  let apiResponse: ApiResponse | null = null;
  let data: IPasxSchool[] | null = null;

  try {
    
    // Authentication check
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const cachedData = await getValueFromRedis(cacheKey);

    if (cachedData) {
      return NextResponse.json(JSON.parse(cachedData));
    } else {
      const accessToken = await generateAccessToken();

      try {
        const fetchResponse = await fetch(`${EksamenApiUrl}/skole`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          cache: "no-store",
        });

        apiResponse = {
          status: fetchResponse.status,
          text: () => fetchResponse.text(),
          json: () => fetchResponse.json(),
        };

        if (!fetchResponse.ok) {
          throw new Error(`HTTP error! status: ${fetchResponse.status}`);
        }

        data = await apiResponse.json();
      } catch (fetchError) {
        console.error("Fetch failed, falling back to blob storage", fetchError);

        // Fallback to blob storage
        data = await downloadJsonFromAzure("skole-PASX.json");
      }

      if (data) {
        const schoolDataToReturn = data
          .filter((school) => school.erAktiv)
          .map(
            ({
              navn,
              organisasjonsnummer,
              erGrunnskole,
              erVideregaendeskole,
              erPrivatSkole,
              besoksadresse,
              skoleansvarligOrganisasjonsnummer,
              ansvarligFylkesmannOrganisasjonsnummer,
              erAktiv,
            }) => ({
              navn,
              organisasjonsnummer,
              erGrunnskole,
              erVideregaendeskole,
              erPrivatSkole,
              besoksadresse,
              skoleansvarligOrganisasjonsnummer,
              ansvarligFylkesmannOrganisasjonsnummer,
              erAktiv,
            })
          );

        await setValueInRedis(
          cacheKey,
          JSON.stringify(schoolDataToReturn),
          3600
        );
        return NextResponse.json(schoolDataToReturn);
      } else {
        throw new Error(
          "Failed to retrieve data from both API and blob storage"
        );
      }
    }
  } catch (error: unknown) {
    console.error("Error:", error);

    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "GET-EnhetsService-Skoler",
        statuscode: apiResponse?.status ?? 0,
        response: apiResponse
          ? await apiResponse.text().catch(() => "Kunne ikke lese respons")
          : "Ingen respons",
      },
    });

    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
