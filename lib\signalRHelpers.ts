import { generateSignalRAccessToken } from "./generateSignalRAccessToken";

// Configuration for Azure SignalR with endpoint extraction from connection string
export const SIGNALR_CONFIG = {
  hubName: process.env.AZURE_SIGNALR_HUB_NAME || "pgshub",
  connectionString:
    process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "",
  get endpoint() {
    return this.connectionString.match(/Endpoint=(.*?);/)?.[1] ?? "";
  },
};

export function buildSignalRUrl(path: string): string {
  return `${SIGNALR_CONFIG.endpoint}/api/v1/hubs/${SIGNALR_CONFIG.hubName}/${path}`;
}

export async function sendSignalRMessage(url: string, message: any) {
  const accessToken = generateSignalRAccessToken({
    audience: url,
    lifetime: 60, // Token expires after 60 seconds
  });

  return fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(message),
  });
}
