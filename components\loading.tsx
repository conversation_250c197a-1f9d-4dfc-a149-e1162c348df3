// loading.tsx
import { Skeleton } from "@/components/ui/skeleton";

export default function Loading() {
  return (
    <div className="w-full space-y-4">
      <div className="flex items-center space-x-4">
        <Skeleton className="h-12 w-20" />
        <Skeleton className="h-4 w-[200px]" />
      </div>

      <div className="space-y-3">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[300px]" />
          <Skeleton className="h-4 w-[100px]" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[100px]" />
        </div>
        <div className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[100px]" />
        </div>
      </div>
    </div>
  );
}
