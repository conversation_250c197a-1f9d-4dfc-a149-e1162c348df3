"use client";

import React, {
  createContext,
  useContext,
  ReactNode,
} from "react";
import { TestPartsEnum } from "@/enums/TestPart";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { OperationEnum } from "@/enums/OperationEnum";
import { useBaseFileHandler, BaseFileContextType, FileHandlerConfig } from "./useBaseFileHandler";

// Define API endpoints specific to this hook
const API_ENDPOINTS = {
  validate: "/api/pgsaValidate/{fileGuid}/{testPartId}",
  delete: "/api/deleteFile/{fileGuid}",
  updateStatus: "/api/updateFileStatus",
  download: "/api/downloadSingleSubmission?documentCode={documentCode}",
};

// Configuration for regular file handler
const fileHandlerConfig: FileHandlerConfig = {
  componentName: "UseFileHandler",
  endpoints: API_ENDPOINTS,
  isGroupUpload: true,
  getUploadOperation: () => OperationEnum.StatusFileUploadedGroupUpload,
  getDeletionOperation: (file: IUploadedFile) => {
    switch (file.TestPartId) {
      case TestPartsEnum.EksamenDel1:
        return OperationEnum.StatusFileDeletedGroupUploadDel1;
      case TestPartsEnum.EksamenDel2:
        return OperationEnum.StatusFileDeletedGroupUploadDel2;
      default:
        return OperationEnum.StatusFileDeletedGroupUpload;
    }
  },
  getDeliveryOperation: (file: IUploadedFile) => {
    switch (file.TestPartId) {
      case TestPartsEnum.EksamenDel1:
        return OperationEnum.StatusFileDeliveredGroupUploadDel1;
      case TestPartsEnum.EksamenDel2:
        return OperationEnum.StatusFileDeliveredGroupUploadDel2;
      default:
        return OperationEnum.StatusFileDeliveredGroupUpload;
    }
  },
  getCandidateInfo: (file?: IUploadedFile) => {
    // Use candidate information from filevalidate response (stored in file object)
    const candidateFirstName = file?.CandidateName?.split(",")[1]?.trim() || "";
    const candidateLastName = file?.CandidateName?.split(",")[0]?.trim() || "";
    
    return {
      userId: file?.candidateRegistrationId || file?.Candididate || "",
      firstName: candidateFirstName,
      lastName: candidateLastName,
      registrationId: file?.candidateRegistrationId || "",
      candidateNumber: file?.Candididate || "",
    };
  },
};

type FileContextType = BaseFileContextType;

const FileContext = createContext<FileContextType | undefined>(undefined);

function useFileHandler(): FileContextType {
  return useBaseFileHandler(fileHandlerConfig);
}

export const FileProvider = ({ children }: { children: ReactNode }) => (
  <FileContext.Provider value={useFileHandler()}>
    {children}
  </FileContext.Provider>
);

export const useFileContext = () => {
  const context = useContext(FileContext);
  if (!context)
    throw new Error("useFileContext må brukes innenfor en FileProvider");
  return context;
};

export default useFileContext;
