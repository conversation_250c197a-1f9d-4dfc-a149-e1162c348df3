export const getUploadBlobSasToken =
  async (): Promise<IUploadFileSasTokenResponse> => {
    try {
      const response = await fetch(
        `${window.location.origin}/api/getuploadblobsastoken`,
        {
          cache: "no-store",
        }
      );

      if (!response.ok) {
        throw new Error(
          `Klarte ikke å hente sastoken for filen. Feilkode:${response.status}. Feilmelding: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Klarte ikke å hente sastoken for opplasting:", error);
      throw new Error("Klarte ikke å hente sastoken for opplasting");
    }
  };
