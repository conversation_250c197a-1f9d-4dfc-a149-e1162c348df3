// app/api/signalr/route.ts
import { ISession } from "@/interface/ISession";
import jwt from "jsonwebtoken";
import { getServerSession } from "next-auth";
import { NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";

const connectionString =
  process.env.CUSTOMCONNSTR_AZURE_SIGNALR_CONNECTION_STRING || "";
const hubName = process.env.AZURE_SIGNALR_HUB_NAME || "pgshub";
const endpoint = connectionString?.match(/Endpoint=(.*?);/)?.[1];
const accessKey = connectionString?.match(/AccessKey=(.*?);/)?.[1] ?? "";

function validateConfig() {
  if (!endpoint || !accessKey || !hubName) {
    throw new Error("SignalR configuration is missing");
  }
}

interface SignalRAccessTokenPayload {
  audience: string;
  lifetime?: number;
}

function generateSignalRAccessToken({
  audience,
  lifetime = 60,
}: SignalRAccessTokenPayload): string {
  try {
    const now = Math.floor(Date.now() / 1000);
    const expires = now + lifetime * 60;

    return jwt.sign(
      {
        aud: audience,
        exp: expires,
        iat: now,
      },
      accessKey,
      { algorithm: "HS256" }
    );
  } catch (error) {
    console.error("Error generating SignalR access token:", error);
    throw new Error("Failed to generate SignalR access token");
  }
}

async function manageUserGroup(
  groupName: string,
  userId: string,
  method: "PUT" | "DELETE"
) {
  if (!endpoint || !hubName) {
    throw new Error("SignalR configuration is missing");
  }

  const url = `${endpoint}/api/v1/hubs/${hubName}/groups/${groupName}/users/${userId}`;

  try {
    const response = await fetch(url, {
      method,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${generateSignalRAccessToken({
          audience: url,
          lifetime: 60,
        })}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to ${method === "PUT" ? "add" : "remove"} user ${
          method === "PUT" ? "to" : "from"
        } group: ${response.statusText}`
      );
    }
  } catch (error) {
    console.error(
      `Error ${method === "PUT" ? "adding" : "removing"} user ${
        method === "PUT" ? "to" : "from"
      } SignalR group: `,
      error
    );
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    validateConfig();

    const body = await request.json();
    const { schoolId } = body;

    if (!schoolId) {
      return NextResponse.json(
        { error: "SchoolId is required" },
        { status: 400 }
      );
    }

    const sessionData: ISession | null = await getServerSession(authOptions);
    if (!sessionData?.user.userInfo.userGuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const clientUrl = `${endpoint}/client/?hub=${hubName}`;

    const token = jwt.sign(
      {
        aud: clientUrl,
        exp: Math.floor(Date.now() / 1000) + 28800, // 8 timer
        iat: Math.floor(Date.now() / 1000),
        sub: sessionData.user.userInfo.userGuid,
        schoolId: schoolId,
      },
      accessKey
    );

    await manageUserGroup(schoolId, sessionData.user.userInfo.userGuid, "PUT");

    return NextResponse.json(
      {
        url: clientUrl,
        accessToken: token,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("SignalR negotiate error:", error);
    return NextResponse.json(
      { error: `Failed to negotiate: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    validateConfig();

    const body = await request.json();
    const { schoolId } = body;

    if (!schoolId) {
      return NextResponse.json(
        { error: "SchoolId is required" },
        { status: 400 }
      );
    }

    const sessionData: ISession | null = await getServerSession(authOptions);
    if (!sessionData?.user.userInfo.userGuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await manageUserGroup(
      schoolId,
      sessionData.user.userInfo.userGuid,
      "DELETE"
    );

    return NextResponse.json(
      { message: "Successfully removed from group" },
      { status: 200 }
    );
  } catch (error) {
    console.error("SignalR group removal error:", error);
    return NextResponse.json(
      { error: `Failed to remove from group: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}
