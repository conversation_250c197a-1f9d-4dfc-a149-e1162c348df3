## Hurtiguide

    - **Innlogging og autorisering**
      <br/>  <br/>
        1.Kandidaten skal gå til [kandidat.udir.no](kandidat.udir.no).
        2.Velg «Jeg har eksamen i dag»
        3.Bruk enten Feide eller kandidatnummer og kandidatpassord for å logge inn.
        4.For at kandidaten skal få digital tilgang til eksamensoppgaven, og kunne levere digitalt må kandidaten gis tilgang i PGS-monitor (se egen brukerveiledning for PGS-monitor). Alternativt kan eksamensvakten skrive inn dagspassordet i nettleseren til kandidaten.
        <br/>
        Merk at kandidaten med fordel kan få tilgang i PGS i god tid før eksamensstart. Kandidaten får uansett ikke tilgang til eksamensoppgaven i PGS før eksamen offisielt starter. Dette gjelder også for todelt eksamen.
        <br/>  <br/>
    - **Levere besvarelse**
          <br/>  <br/>
          1.<PERSON><PERSON><PERSON> «Gå til levering».
          2.Last opp en eller flere filer. Laster man opp feil fil, trykker man «Slett» (med ikon av papirkurv) til høyre for filnavnet for å fjerne filen.
          3.Kontroller besvarelsen ved å trykke «Last ned».
          4.Kandidaten skal åpne den nedlastede filen og verifisere at innholdet er riktig. Kandidaten vil få en bekreftelse i PGS på at filen er sjekket til venstre for filnavnet. Dersom kandidaten oppdager at han/hun har lastet opp feil fil, må opplastet fil slettes i PGS og deretter må riktig fil lastes opp og sjekkes. Om det skal gjøres endringer i filen etter å ha sjekket den, må filen endres lokalt på kandidatens PC. Deretter må den gamle versjonen slettes i PGS, og oppdatert fil må lastes opp og sjekkes.

5. Når kandidaten har sjekket alle filene kan hen levere ved å trykke på
   «Lever filene».
6. Bekreft levering. Etter dette kan ikke kandidaten levere på nytt, med mindre eksamensvakten åpner for dette i PGS-monitor. Se egen brukerveiledning for PGS-monitor.
7. Når kandidaten har levert vil kandidaten komme til kvitteringssiden. Kandidaten skal ikke logge ut er lukke nettleseren før en eksamensvakt har kontrollert kvitteringssiden.
8. Dagen etter eksamen kan kandidaten logge inn på kandidat.udir.no for å se sin egen besvarelse. Kandidaten kan da logge inn enten med Feide eller med ID-porten.

Kandidaten kan i enkelte tilfeller oppleve feil som skyldes forhold med nettleseren sin. Forsøk da å oppdatere siden i nettleseren til kandidaten. Hvis feilen vedvarer, kan kandidaten forsøke med en annen type nettleser.

## Innloggingssiden

> ## Infoboks
>
> Med **autentisering** mener vi her prosessen med å bekrefte en identitet. Dette skjer ved at kandidaten logger på med Feide eller kandidatnummer og passord. Autentiseringen foregår i Udir sin løsning for identitets- og tilgangskontroll: [UIDP](https://uidp-admin.udir.no/info/om-uidp).
>
> Med **autorisering** mener vi her prosessen med å avgjøre om en kandidat skal få tilgang til PGS. Kandidaten autoriseres ved at eksamensvakten gir tilgang i PGS-monitor eller at dagspassord benyttes.

Kandidaten går til [kandidat.udir.no](kandidat.udir.no), og velger «Jeg har eksamen i dag». Hen kommer så til innloggingssiden. Under vises et skjermbilde av innloggingssiden for kandidater. Alle persondata som vises i skjermbilder i denne veiledningen er fiktive data.

Kandidaten autentiserer seg med sin Feide-bruker, eller med kandidatnummer og kandidatpassord på innloggingssiden i UIDP. Skoleadministrator har tilgang på kandidatnummer og kandidatpassord i PAS-eksamen. Der finner man også dagspassordet for de forskjellige kandidatgruppene.

<img src="/images/signinnew.gif" />

## Autoriseringssiden

Når kandidaten har autentisert seg og logget inn i PGS vil hen komme til autoriseringssiden. Kandidatens navn vil vises i overskriften på siden.

For at kandidaten skal få tilgang til eksamensoppgaven, og kunne levere eksamen digitalt må kandidaten gis digital tilgang (autoriseres) av eksamensvakten i PGS-monitor (se egen brukerveiledning for PGS-monitor). Kandidaten må klikke på knappen «Jeg har fått tilgang» etter at digital tilgang er gitt i PGS-monitor. Eksamensvakten bør påse at kandidaten er til stede i eksamenslokalet og er logget på med sin egen bruker før det gis digital tilgang til PGS.

Et alternativ til å gi tilgang via PGS-monitor er at eksamensvakten skriver inn dagspassordet i nettleseren til kandidaten. Dagspassord er tilgjengelig for skoleadministrator i PAS-eksamen. Merk at dagspassordet skal være hemmelig og ikke deles med kandidaten.

Dersom kandidaten har fått digital tilgang av eksamensvakten før hen autentiserer seg, vil kandidaten sendes direkte til ventesiden etter autentisering på innloggingssiden.

![autoriseringsside](/images/autoriseringssideny.gif)

## Ventesiden

Når kandidaten har fått tilgang av eksamensvakten vil hen komme til ventesiden. Ventesiden vil vise nedtelling til eksamensstart. Når eksamen starter, vil kandidaten automatisk sendes videre til eksamensoppgaven (oppgavesiden).

Dersom kandidaten logger inn og autoriseres etter eksamensstart vil hen sendes direkte til oppgavesiden uten å komme til ventesiden.

Det er uproblematisk at kandidaten får digital tilgang til PGS før tidspunktet for eksamensstart. Kandidaten får uansett ikke tilgang til eksamensoppgaven i PGS før eksamen offisielt starter. Dette gjelder også for todelt eksamen – kandidaten vil ikke få tilgang eksamensoppgaven for del 2 før denne eksamensdelen offisielt starter. Starttidspunktet for del 2 er 10:00, 10:45 eller 11:00 avhengig av fagkoden. Da del 1 for todelt eksamen kun skal gjennomføres på papir, er ikke del 1 tilgjengelig i PGS for kandidatene.

![ventesiden](/images/ventesiden.png)

## Oppgavesiden

På oppgavesiden i PGS kan kandidaten laste ned eksamensoppgaven. Dersom eksamen har en forberedelsesdel, vil kandidaten også kunne laste ned forberedelsesmateriellet. For todelt eksamen vil ikke del 1 være tilgjengelig for nedlastning.

Kandidaten vil fra oppgavesiden også kunne gå videre til levering av eksamen (leveringssiden).

![oppgavesiden](/images/oppgavesiden.gif)

{" "}
<h2 id="leveringssiden-last-opp">Leveringssiden – Last opp</h2>

På leveringssiden skal kandidaten laste opp besvarelsesfilen(e) sin(e). Maksimal filstørrelse er 40MB. Maksgrensen vises på siden. For å laste opp filene kan kandidaten velge å dra filene over et angitt område eller hen kan benytte filutforskeren til å velge filene.

![leveringssideLastopp](/images/lastopp.gif)

Det er også begrensning på filtyper, og duplikat filnavn tillates ikke. Kandidaten vil få feilmelding ved opplastning dersom en fil ikke validerer. Feilede filer må slettes.

![leveringssideFeil](/images/leveringssideFeil.png)

Godkjente filtyper er:

7z, avi, bmp, c, css, doc, docm, docx, dot, dotm, dotx, emf, flv, ggb, gif, gz, h, hlp, htm, html, jpe, jpeg, jpg, js, m4a, mdb, mdi, mht, mm, mov, movie, mp3, mp4, mpeg, mpp, odb, odp, ods, odt, ott, pdf, png, ppsm, ppsx, ppt, pptm, pptx, qt, rap, rar, rm, rtf, sib, stw, svg, swf, sxc, sxw, tar, tex, tif, tiff, tii, txt, vsd, wav, wmf, wmv, wri, xls, xlsb, xlsm, xlsx, xml, xps, z, zip, zipx

<h2 id="leveringssiden-last-ned-og-sjekk-filene">
  Leveringssiden – Last ned og sjekk filene
</h2>

Før kandidaten kan levere må kandidaten laste ned og sjekke filene for å sikre at de er korrekte.
Dette gjøres ved å klikke «Last ned», for så å åpne og se gjennom den nedlastede filen. Dette må gjøres for alle filer som kandidaten har lastet opp til PGS.

Dersom kandidaten oppdager at hen har lastet opp feil fil, må opplastet fil slettes i PGS. Deretter må riktig fil lastes opp og sjekkes.

Dersom kandidaten ser det er behov for å gjøre endringer i filen etter å ha sjekket den, må filen endres lokalt på kandidatens PC. Deretter må den gamle versjonen slettes i PGS, og oppdatert fil må lastes opp og sjekkes.

Dersom kandidaten mot formodning skulle få en feilmelding ifb. nedlastning, er anbefalingen at kandidaten oppdaterer siden i nettleseren.

![LastNedOgSjekk](/images/LastNedOgSjekk.png)

<h2 id="leveringssiden-lever-eksamensbesvarelse">
  Leveringssiden – Lever eksamensbesvarelse{" "}
</h2>

Når kandidaten har sjekket alle filene kan hen levere sin eksamensbesvarelse ved å klikke «Lever filene».

![lever](/images/lever.png)

## Siste bekreftelse

For å endelig levere eksamensbesvarelsen må kandidaten gi en siste bekreftelse. Kandidaten må da klikke på knappen «Bekreft levering».

Etter at kandidaten har gitt siste bekreftelse vil ikke kandidaten kunne gjøre endringer på sine besvarelsesfiler eller angre leveringen. Eksamensvakten kan imidlertid ved behov åpne for at kandidaten skal få levere på nytt. Se brukerveiledningen for PGS-monitor for mer om dette.

![sisteBekreftelse](/images/sisteBekreftelse.png)

## Kvitteringssiden

Når kandidaten har bekreftet levering vil hen sendes automatisk til kvitteringssiden. På kvitteringssiden vil kandidaten se en liste over leverte filer, og hen vil ha muligheten til å laste ned en kvittering for levert eksamen. For todelt eksamen vil eksamensdelen spesifiseres i oversikten over leverte filer, og filer for del 1 vil vises dersom en eksamensvakt har levert digitalt for del 1.

Kandidaten skal ikke lukke siden eller logge ut, uten at hen har fått tillatelse fra en eksamensvakt. Dette er for å sikre at kandidaten får levert eksamen korrekt.

Dagen etter eksamen kan kandidaten logge inn på [kandidat.udir.no](kandidat.udir.no) for å se sin egen besvarelse. Kandidaten kan da logge inn enten med Feide eller med ID-porten.

Under vises et skjermbilde av kvitteringssiden når kandidaten har levert del 2 digitalt:

![kvittering](/images/kvittering.png)

Under vises et skjermbilde av kvitteringssiden når kandidaten har sendes i posten:

![kvittering på papir](/images/kvitteringPapir.png)

<h3 id="fravrssiden">Fraværssiden</h3>

En kandidat som er markert med «Dokumentert fravær» eller «Ikke-dokumentert fravær» i PGS, vil komme til fraværssiden dersom hen forsøker å logge på PGS. På fraværssiden får kandidaten beskjed om å kontakte eksamensvakten hvis dette er feil.
![fravær](/images/fravar.png)

## Tilbakemelding

Vi ønsker dine tilbakemeldinger på brukerveiledningen og hvordan du opplever PGS som system. Du kan sende e-<NAME_EMAIL>
