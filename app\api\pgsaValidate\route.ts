"use server";

import { getServerSession } from "next-auth/next";
import { getAccessToken } from "@/lib/getAccessToken";
import { authOptions } from "@/app/api/auth/authOptions";
import { ISession } from "@/interface/ISession";
import { NextRequest, NextResponse } from "next/server";
import { getClientIp } from "@/lib/getClientIp";
import { IPgsaValidateFilePayload } from "@/interface/IPgsaValidateFilePayload";
import { getAppInsightsServer } from "@/lib/appInsightsServer";

const PgsaApiUrl = process.env.PGSA_PGSE_API_URL;
const clientId: string = process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_ID || "";
const clientSecret: string =
  process.env.UIDP_PGS_ADMIN_RESOURCE_CLIENT_SECRET || "";
const scope: string =
  process.env.UDIR_PGS_ADMIN_RESOURCE_BESVARELSE_SCOPE || "";
const accesstokenKey: string = "PGSE:PGSA:AccessToken";

const telemetryClient = getAppInsightsServer();

export async function POST(request: NextRequest) {
  let response = null;
  let responseText = "";
  let body: IPgsaValidateFilePayload = await request.json();

  // Sjekk authentication først
  const session: ISession | null = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const userId = session.user.userInfo.userId;
    const ip = await getClientIp(request);

    body.UserId = userId;
    body.IpAddress = ip;

    // Get access token
    const accessToken = await getAccessToken(
      clientId,
      clientSecret,
      scope,
      accesstokenKey
    );

    if (!accessToken) {
      throw new Error("Failed to obtain access token");
    }

    const apiUrl = `${PgsaApiUrl}/api/BatchProcessing/upload`;
    response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
      cache: "no-store",
    });

    responseText = await response.text();

    if (!response.ok) {
      const error = new Error(`API request failed: ${responseText}`);
      (error as any).statusCode = response.status;
      throw error;
    }

    const data = responseText ? JSON.parse(responseText) : null;

    return NextResponse.json(data);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "pgsaValidate",
        payload: body,
        statusCode: response?.status,
        responseText,
      },
    });

    // Returner API-ets statuskode hvis tilgjengelig
    if ((error as any).statusCode) {
      return NextResponse.json(responseText, {
        status: (error as any).statusCode,
      });
    }

    // Fallback til 500 for andre feil
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
