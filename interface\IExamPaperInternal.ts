import { ICandidate } from "./ICandidate";

export interface IExamPaperInternal {
  groupCode: string;
  groupName: string;
  subjectCode: string;
  subjectName: string;
  variant: string;
  testPeriod: string;
  numberOfSubmissions: number;
  numberOfCandidatesInGroup: number;
  examStartDate?: string;
  partOneStartDateTime?: string;
  partTwoStartDateTime?: string | null;
  candidates: ICandidate[];
}
