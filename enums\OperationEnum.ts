export enum OperationEnum {
  StatusDokumentedAbsence = 100,
  StatusAccessGranted = 101,
  StatusAccessRefused = 102,
  StatusUnDocumentedAbsence = 103,
  StatusUndoAbsence = 104,
  StatusSendInPost = 105,
  StatusDeliveredOnPaperPart1 = 106,
  StatusDeliveredOnPaperPart2 = 107,
  StatusUndoDeliveredOnPaper = 108,
  StatusUndoDeliveredOnPaperPart1 = 109,
  StatusUndoDeliveredOnPaperPart2 = 110,
  StatusDeliveredExam = 111,
  StatusDeliveredPart2 = 112,
  StatusOpenedForNewDeliveryEksamen = 113,
  StatusOpenedForNewDelivery = 114,
  StatusBlocked = 115,
  StatusUnBlocked = 116,
  StatusOppdatering = 117,
  StatusFileDeleted = 118,
  StatusFileUploaded = 119,
  StatusFileDelivered = 120,
  StatusFileDeliveredPart1 = 121,
  StatusFileDeliveredPart2 = 122,
  StatusFileDownloaded = 123,
  StatusChangedTestPartId = 124,
  StatusFileChecked = 125,
  StatusRemovedAccess = 126,
  StatusFileUploadedGroupUpload = 127,
  StatusFileDeliveredGroupUpload = 128,
  StatusFileDeliveredGroupUploadDel1 = 129,
  StatusFileDeliveredGroupUploadDel2 = 130,
  StatusFileDeletedGroupUpload = 131,
  StatusFileDeletedGroupUploadDel1 = 132,
  StatusFileDeletedGroupUploadDel2 = 133,
  StatusChangedTestPartIdGoupUpload = 134,
}
