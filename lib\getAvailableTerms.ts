import dayjs from "dayjs";

interface Term {
  value: string; // f.eks. "H-2024"
  label: string; // f.eks. "Høst 2024"
}

export const getAvailableTerms = (numberOfTerms = 3): Term[] => {
  const terms: Term[] = [];
  const currentDate = dayjs();
  const currentYear = currentDate.year();
  const currentMonth = currentDate.month() + 1;

  // Vi er nå i vårsemesteret hvis vi er i månedene 1-6
  const isCurrentlySpring = currentMonth >= 1 && currentMonth < 7;

  let year = currentYear;

  if (isCurrentlySpring) {
    terms.push({ value: `V-${year}`, label: `Vår ${year}` });
    terms.push({ value: `H-${year - 1}`, label: `Høst ${year - 1}` });
    year--;
  } else {
    terms.push({ value: `H-${year}`, label: `Høst ${year}` });
    terms.push({ value: `V-${year}`, label: `Vår ${year}` });
    terms.push({ value: `H-${year - 1}`, label: `Høst ${year - 1}` });
    year--;
  }

  // Legg til flere tidligere terminer hvis nødvendig
  while (terms.length < numberOfTerms) {
    terms.push({ value: `V-${year}`, label: `Vår ${year}` });
    if (terms.length < numberOfTerms) {
      terms.push({ value: `H-${year - 1}`, label: `Høst ${year - 1}` });
      year--;
    }
  }

  return terms.slice(0, numberOfTerms);
};
