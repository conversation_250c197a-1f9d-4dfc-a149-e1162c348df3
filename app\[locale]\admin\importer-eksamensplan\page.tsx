"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, Download, Loader2 } from "lucide-react";
import { IEksamensplanImportResponse } from "@/interface/IEksamensplanImport";

type ImportResult = IEksamensplanImportResponse;

export default function ImportEksamensplanPage() {
  const [eksamensperiode, setEksamensperiode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [availableEksamensperioder, setAvailableEksamensperioder] = useState<
    string[]
  >([]);

  // Generer liste over tilgjengelige eksamensperioder på klientsiden
  useEffect(() => {
    const generateEksamensperioder = () => {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;
      // Finn "nåværende" periode
      const isAfterJune = currentMonth >= 6;
      // Finn start og sluttperiode
      let periods: string[] = [];
      let startPeriod, endPeriod;
      if (isAfterJune) {
        // Nåværende periode er Høst, så start på H-(currentYear-1) og slutt på H-(currentYear+1)
        startPeriod = { season: "H", year: currentYear - 1 };
        endPeriod = { season: "H", year: currentYear + 1 };
      } else {
        // Nåværende periode er Vår, så start på V-(currentYear-1) og slutt på V-(currentYear+1)
        startPeriod = { season: "V", year: currentYear - 1 };
        endPeriod = { season: "V", year: currentYear + 1 };
      }
      // Generer perioder mellom start og slutt
      let y = startPeriod.year;
      let s = startPeriod.season;
      while (true) {
        periods.push(`${s}-${y}`);
        if (s === endPeriod.season && y === endPeriod.year) break;
        if (s === "H") {
          s = "V";
          y = y + 1;
        } else {
          s = "H";
        }
      }
      // Sett nåværende periode
      const currentPeriod = isAfterJune
        ? `H-${currentYear}`
        : `V-${currentYear}`;
      return { periods, currentPeriod };
    };
    const { periods, currentPeriod } = generateEksamensperioder();
    setAvailableEksamensperioder(periods);
    setEksamensperiode(currentPeriod);
  }, []);

  const handleImport = async () => {
    if (!eksamensperiode) {
      setResult({
        success: false,
        message: "Vennligst velg en eksamensperiode",
        error: "Manglende eksamensperiode",
      });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch("/api/admin/import-eksamensplan", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ eksamensperiode }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setResult({
          success: false,
          message: "Import feilet",
          error: data.error || "Ukjent feil",
          details: data.details,
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: "Nettverksfeil",
        error: "Kunne ikke koble til serveren",
        details: error instanceof Error ? error.message : "Ukjent feil",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setResult(null);
    setEksamensperiode("");
  };

  return (
    <>
      <div className="py-6 bg-header">
        <div className="container-wrapper">
          <div>
            <h1 className="text-4xl">Importer eksamensplan</h1>
            <p className="mt-4">
              Last ned og importer eksamensplandata fra PAS eksamensapi til PGS databasen.
            </p>
          </div>
        </div>
      </div>
      <div className="container-wrapper py-8">
        <div className="max-w-2xl mx-auto space-y-6">

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              Import Eksamensdata
            </CardTitle>
            <CardDescription>
              Importer fagkoder og eksamensdeler for en gitt eksamensperiode.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="eksamensperiode">Eksamensperiode</Label>
              <Select
                value={eksamensperiode}
                onValueChange={setEksamensperiode}
                disabled={isLoading}
              >
                <SelectTrigger className="font-mono">
                  <SelectValue placeholder="Velg eksamensperiode" />
                </SelectTrigger>
                <SelectContent>
                  {availableEksamensperioder.length > 0 ? (
                    availableEksamensperioder.map((periode: string) => (
                      <SelectItem
                        key={periode}
                        value={periode}
                        className="font-mono"
                      >
                        {periode} ({periode.startsWith("H") ? "Høst" : "Vår"}{" "}
                        {periode.split("-")[1]})
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      Laster perioder...
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Velg eksamensperiode fra listen.
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleImport}
                disabled={
                  isLoading ||
                  !eksamensperiode ||
                  availableEksamensperioder.length === 0
                }
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Importerer...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4" />
                    Start Import
                  </>
                )}
              </Button>

              {result && (
                <Button variant="outline" onClick={handleReset}>
                  Tilbakestill
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {result && (
          <Alert
            className={
              result.success
                ? result.data && result.data.importedCount === 0
                  ? "border-yellow-200 bg-yellow-50"
                  : "border-green-200 bg-green-50"
                : "border-red-200 bg-red-50"
            }
          >
            <div className="flex items-center gap-2">
              {result.success ? (
                result.data && result.data.importedCount === 0 ? (
                  <CheckCircle className="w-4 h-4 text-yellow-600" />
                ) : (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                )
              ) : (
                <AlertCircle className="w-4 h-4 text-red-600" />
              )}
              <AlertTitle
                className={
                  result.success
                    ? result.data && result.data.importedCount === 0
                      ? "text-yellow-800"
                      : "text-green-800"
                    : "text-red-800"
                }
              >
                {result.success
                  ? result.data && result.data.importedCount === 0
                    ? "Ingen data funnet for valgt periode"
                    : "Import fullført!"
                  : "Import feilet"}
              </AlertTitle>
            </div>
            <AlertDescription
              className={`mt-2 ${
                result.success
                  ? result.data && result.data.importedCount === 0
                    ? "text-yellow-700"
                    : "text-green-700"
                  : "text-red-700"
              }`}
            >
              <div className="space-y-2">
                <p>{result.message}</p>
                {result.success && result.data && (
                  <div
                    className={`bg-white p-3 rounded border ${
                      result.data.importedCount === 0
                        ? "border-yellow-200"
                        : "border-green-200"
                    } text-sm`}
                  >
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <strong>Eksamensperiode:</strong>{" "}
                        {result.data.eksamensperiode}
                      </div>
                      <div>
                        <strong>Importert:</strong> {result.data.importedCount}{" "}
                        fagkoder
                      </div>
                      <div>
                        <strong>Feil:</strong> {result.data.errorCount}
                      </div>
                      <div>
                        <strong>Utført av:</strong> {result.data.user}
                      </div>
                    </div>
                  </div>
                )}
                {!result.success && result.error && (
                  <div className="bg-white p-3 rounded border border-red-200 text-sm">
                    <div>
                      <strong>Feilmelding:</strong> {result.error}
                    </div>
                    {result.details && (
                      <div className="mt-1">
                        <strong>Detaljer:</strong> {result.details}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Informasjon</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-muted-foreground space-y-2">
            <p>
              <strong>API-endepunkt:</strong>{" "}
              https://eksamenapi-tst2.udir.no/pgsd/eksamensplan
            </p>
            <p>
              <strong>Tabeller som oppdateres:</strong> Fagkodeeksamen og
              Eksamensdel
            </p>
            <p>
              <strong>Import-logikk:</strong> Eksisterende data oppdateres, nye
              data legges til.
            </p>
            <p>
              <strong>Tilgang:</strong> Kun systemadministratorer kan utføre
              denne operasjonen.
            </p>
          </CardContent>
        </Card>
        </div>
      </div>
    </>
  );
}
