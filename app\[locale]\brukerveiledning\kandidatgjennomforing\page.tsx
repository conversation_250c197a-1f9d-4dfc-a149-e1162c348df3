
import MarkdownComponent from "./markdownComponent";
import TableOfContents from "../../../../components/toc";
import React from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Brukerveiledning PGS – kandidatgjennomføring",
};

export default async function Home() {
  return (
    <>
      <div className="p-6 bg-header">
        <div className="container-wrapper">
          <h1 className="text-4xl">
          Brukerveiledning PGS – kandidatgjennomføring
          </h1>

          <p className="mt-4">
            {" "}
            Veiledning for våren 2025 – sist oppdatert 08.05.2025
          </p>
        </div>
      </div>
      <div className="container-wrapper flex justify-between md:gap-8 p-10">
        <div className="">
          <MarkdownComponent />
        </div>
        <div className="hidden md:block md:w-[300px] lg:w-[350px] xl:w-[400px] ml-20 ">
          <div className="sticky top-40 bottom-4 w-[280px] lg:w-[330px] xl:w-[380px] overflow-y-auto">
            <TableOfContents />
          </div>
        </div>
      </div>
    </>
  );
}
