import React from "react";
import Countdown from "react-countdown";

interface CountdownTimerProps {
  onComplete?: () => void;
  interval: number;
}

interface RendererProps {
  seconds: number;
  completed: boolean;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  onComplete,
  interval,
}) => {
  const renderer = ({ seconds, completed }: RendererProps) => {
    if (completed) {
      return <span>Refresh...</span>;
    }
    return <span>{seconds}s</span>;
  };

  return (
    <div className="text-sm text-gray-500">
      <span className="mr-1">Neste oppdatering om:</span>
      <Countdown
        date={Date.now() + interval}
        renderer={renderer}
        onComplete={() => {
          onComplete?.();
          return { shouldRepeat: true };
        }}
        autoStart={true}
        key={Date.now()}
      />
    </div>
  );
};

export default CountdownTimer;
