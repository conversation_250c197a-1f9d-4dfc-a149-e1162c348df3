"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from "@/components/ui/command";
import { ChevronsUpDown, Check } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

const items = [
  { id: "papir", label: "Papir" },
  { id: "digital", label: "Digital" },
] as const;

export function ExamPerformanceCheck() {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [openStatus, setOpenStatus] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const statusFilterLabel = "examPerformance";

  const updateQueryString = (statuses: string[]) => {
    const params = new URLSearchParams(window.location.search);

    if (statuses.length > 0) {
      params.set(statusFilterLabel, statuses.join(","));
    } else {
      params.delete(statusFilterLabel);
    }

    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleCheckboxChange = (id: string, checked: boolean) => {
    setSelectedItems((prev) => {
      const updatedItems = checked ? [...prev, id] : prev.filter((item) => item !== id);
      return updatedItems;
    });
  };

  const getSelectedStatuses = (): string[] => {
    const statuses = searchParams.get(statusFilterLabel)?.split(",") || [];
    return statuses.filter((status) => items.some((item) => item.id === status));
  };

  useEffect(() => {
    const initialStatuses = getSelectedStatuses();
    setSelectedItems(initialStatuses);
  }, [searchParams]);

  useEffect(() => {
    updateQueryString(selectedItems);
  }, [selectedItems]);

  const filteredItems = items.filter((item) =>
    item.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Popover open={openStatus} onOpenChange={setOpenStatus}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-between text-left font-medium w-[250px]"
        >
          <div className="flex items-center truncate">
            {selectedItems.length > 0 ? (
              <div className="flex items-center gap-2">
                <span className="text-sm">Gjennomføringsmodus</span>
                <Badge variant="secondary" className="bg-mint">
                  {selectedItems.length}
                </Badge>
              </div>
            ) : (
              "Gjennomføringsmodus"
            )}
          </div>
          <ChevronsUpDown
            className="ml-2 h-4 w-4 shrink-0 opacity-50 transition-transform duration-200"
            role="img"
            aria-label="FilterIkon"
            style={{
              transform: openStatus ? "rotate(180deg)" : "rotate(0deg)",
            }}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <div className="w-[250px]">
          <Command>
           
            <CommandList>
              {filteredItems.length > 0 ? (
                <CommandGroup className="p-1">
                  {filteredItems.map((item) => (
                    <CommandItem
                      key={item.id}
                      value={item.label}
                      onSelect={() => {
                        const isSelected = selectedItems.includes(item.id);
                        handleCheckboxChange(item.id, !isSelected);
                      }}
                      className="flex items-center gap-2 px-2 py-1.5 hover:bg-gray-100 rounded-md cursor-pointer transition-colors duration-150"
                    >
                      <div className="flex items-center flex-1 gap-2">
                        <Checkbox
                          checked={selectedItems.includes(item.id)}
                          onCheckedChange={(checked) => {
                            handleCheckboxChange(item.id, checked as boolean);
                          }}
                          className="border-gray-300"
                          onClick={(e) => e.stopPropagation()}
                        />
                        <span className="text-sm">{item.label}</span>
                      </div>
                      {selectedItems.includes(item.id) && (
                        <Check className="h-4 w-4 text-mint" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              ) : (
                <CommandEmpty>Ingen resultater funnet</CommandEmpty>
              )}
            </CommandList>
          </Command>
        </div>
      </PopoverContent>
    </Popover>
  );
}
