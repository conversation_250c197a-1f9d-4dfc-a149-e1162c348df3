import React from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
    title: "Informasjonskapsler - PGS-admin",
};

const cookies = [
    {
        name: "ai_user",
        purpose: "Brukes av Application Insights for å samle telemetry data om bruk av applikasjonen.",
        admin: true,
        candidate: true,
    },
    {
        name: "ai_session",
        purpose: "Brukes av Application Insights til å spore brukerøkter.",
        admin: false,
        candidate: true,
    },
    {
        name: "_Host-next-auth.csrf-token",
        purpose: "Sikrer at forespørsler som endrer tilstand (f.eks. innlogging) kommer fra legitime kilder.",
        admin: true,
        candidate: true,
    },
    {
        name: "_Secure-next-auth.callback-url",
        purpose: "Hjelper med å håndtere redirects under OAuth- eller lignende autentiseringsflyter.",
        admin: true,
        candidate: true,
    },
    {
        name: "_Secure-next-auth.session-token",
        purpose: "<PERSON>ruk<PERSON> til å identifisere og validere brukerens sesjon på tvers av forespørsler.",
        admin: true,
        candidate: true,
    },
    {
        name: "_Secure-next-auth.pkce.code_verifier",
        purpose: "Brukes for sikker autentisering via PKCE i OAuth2.",
        admin: false,
        candidate: true,
    },
    {
        name: "_Secure-next-auth.state",
        purpose: "Brukes for å beskytte mot CSRF-angrep i OAuth-autentisering.",
        admin: false,
        candidate: true,
    },
    {
        name: "NEXT_LOCALE",
        purpose: "Brukes for å vise riktig målform i sidene.",
        admin: false,
        candidate: true,
    },
    {
        name: "userRole",
        purpose: "Brukes for tilgangskontroll og visning av rollespesifikt innhold.",
        admin: true,
        candidate: false,
    },
    {
        name: "pgs.user",
        purpose: "Inneholder en signert JWT som identifiserer brukeren i kandidat-applikasjonen.",
        admin: false,
        candidate: true,
    },
];

export default async function Home() {
    return (
        <>
            <div className="p-6 bg-header">
                <div className="container-wrapper">
                    <h1 className="text-4xl">Bruk av informasjonskapsler</h1>
                </div>
            </div>
            <div className="container-wrapper flex justify-between md:gap-8 p-10">
                <div>
                    <p>
                        En informasjonskapsel, også kjent som cookie, er en liten tekstfil som
                        blir lagret på datamaskinen, lesebrettet eller mobilen din av nettstedet
                        du besøker. Slik kan nettsiden huske handlingene eller preferansene dine
                        over tid.
                    </p>
                    <p>Dette er informasjonskapsler som PGS bruker:</p>

                    <div className="overflow-x-auto py-6">
                        <table className="min-w-full border border-gray-300">
                            <thead className="bg-gray-100">
                                <tr>
                                    <th className="px-4 py-2 border">Navn</th>
                                    <th className="px-4 py-2 border">Formål</th>
                                    <th className="px-4 py-2 border">Admin</th>
                                    <th className="px-4 py-2 border">Kandidat</th>
                                </tr>
                            </thead>
                            <tbody>
                                {cookies.map((cookie) => (
                                    <tr key={cookie.name} className="hover:bg-gray-50">
                                        <td className="px-4 py-2 border font-mono">{cookie.name}</td>
                                        <td className="px-4 py-2 border">{cookie.purpose}</td>
                                        <td className="px-4 py-2 border text-center">
                                            {cookie.admin ? "✓" : ""}
                                        </td>
                                        <td className="px-4 py-2 border text-center">
                                            {cookie.candidate ? "✓" : ""}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </>
    );
}
