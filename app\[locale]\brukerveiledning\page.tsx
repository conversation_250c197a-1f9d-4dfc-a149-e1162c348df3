import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import React from "react";

export const metadata: Metadata = {
  title: "Bukerveiledning - PGS-admin",
 
};
export default async function Home() {
  return (
    <>
      <div className="py-6 bg-header">
        <div className="container-wrapper">
          <h1 className="text-4xl">Brukerveiledning</h1>
          <p className="mt-4">Her finner du veiledninger for bruk av PGS.</p>
          <p>
            De fleste av veiledningene er også tilgjengelig på{" "}
            <Link
              href="https://www.udir.no/eksamen-og-prover/eksamen/administrere-eksamen/#a110486"
              target="_blank"
              className="underline underline-offset-2"
            >
              udir.no
            </Link>
            .
          </p>
        </div>
      </div>
      <div className="flex flex-col md:flex-row items-start  md:gap-12 justify-between">
        <div className="flex flex-col gap-6 md:gap-8 container-wrapper py-20 ">
          <Link
            href="/brukerveiledning/kandidatgjennomforing"
            className="underline underline-offset-2 hover:text-blue-600"
          >
            Kandidatgjennomføring
          </Link>
          <Link
            href="/brukerveiledning/pgs-monitor"
            className="underline underline-offset-2 hover:text-blue-600"
          >
            PGS-monitor
          </Link>
          <Link
            href="/brukerveiledning/pgs-monitor/leverFor"
            className="underline underline-offset-2 hover:text-blue-600"
          >
            Lever for kandidat
          </Link>
          <Link
            href="/brukerveiledning/gruppeopplaster"
            className="underline underline-offset-2 hover:text-blue-600"
          >
            Gruppeopplasting
          </Link>
          <Link
            href="/brukerveiledning/nedlasting"
            className="underline underline-offset-2 hover:text-blue-600"
          >
            Nedlasting
          </Link>
          
        </div>
        <div className="w-full md:w-1/2 ">
          <Image
            src="/images/illustrasjonVoksen.png"
            alt="Illustrasjon"
            width={400}
            height={400}
          />
        </div>
      </div>
    </>
  );
}
