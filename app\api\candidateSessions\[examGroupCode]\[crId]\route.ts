import { getSessionDataWithPipeline } from "@/app/lib/redisHelper";
import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ examGroupCode: string; crId: string }> }
) {
  const { examGroupCode, crId } = await params;

  if (!examGroupCode || !crId) {
    return NextResponse.json(
      { error: "Missing examGroupCode or crId" },
      { status: 400 }
    );
  }

  try {
    const sessionData = await getSessionDataWithPipeline(examGroupCode, crId);
    return NextResponse.json(sessionData, { status: 200 });
  } catch (error) {
    console.error("Error fetching session data:", error);
    return NextResponse.json(
      { error: "Failed to fetch session data" },
      { status: 500 }
    );
  }
}
