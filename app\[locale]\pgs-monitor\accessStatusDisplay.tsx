import React from "react";
import { useCandidate } from "@/context/CandidateMonitorContext";
import { ICandidateMonitor } from "@/interface/ICandidateMonitor";
import { useAccessRequests } from "@/hooks/accessRequestProvider";
import { getCandidateAccessStatus } from "@/lib/candidateStatusUtils";

interface AccessStatusDisplayProps {
  status1: number;
  status2: number;
  candidate: ICandidateMonitor;
}

export const AccessStatusDisplay = ({
  status1,
  status2,
  candidate,
}: AccessStatusDisplayProps) => {
  const { 
    authorizedCandidates, 
    candidatesWaitingForExamStart,
    blockedUsers, 
    hasAbsence, 
    candidatesWithLevertStatus 
  } = useCandidate();
  const { accessRequests } = useAccessRequests();

  // Use the centralized status calculation
  const statusResult = getCandidateAccessStatus({
    candidate,
    authorizedCandidates,
    accessRequests,
    blockedUsers,
    hasAbsence,
    candidatesWithLevertStatus,
    candidatesWaitingForExamStart,
  });

  return (
    <div className="flex items-center gap-2 ">
      <div
        className="h-3 w-3 min-w-[0.75rem] min-h-[0.75rem] rounded-full"
        style={{ backgroundColor: statusResult.color }}
      />
      <span className="text-gray-600 text-wrap w-[120px]">{statusResult.text}</span>
    </div>
  );
};

export default AccessStatusDisplay;
