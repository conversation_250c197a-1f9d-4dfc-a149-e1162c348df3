// Unified interface for activity logging
export interface IActivityLogV2 {
  // Input fields (from client)
  userId: string;
  FileName?: string;
  TestPartId?: string;
  Role: string;
  OperationId: number;
  Parameters?: Record<string, any>;

  // Context fields (from validateupload or candidate context) - Required
  candidateRegistrationId: string;
  candidateNumber: string;
  candidateFirstName: string;
  candidateLastName: string;

  // Server-side fields (populated internally)
  ip?: string;
}
