import { ISession } from "@/interface/ISession";
import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { generateSasTokenUrl } from "@/lib/blobHelper";
import { getAppInsightsServer } from "@/lib/appInsightsServer";

const container = "pgsx-documents";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET(request: NextRequest) {
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const searchParams = request.nextUrl.searchParams;
    const guid = searchParams.get("guid");

    if (!guid) {
      return NextResponse.json(
        { error: "Missing guid parameter" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      sastoken: await generateSasTokenUrl(container, guid, "wt"),
    });
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "getUploadBlobSasToken",
        response: error instanceof Error ? error.message : "Unknown error",
      },
    });
    // Logger feilen og sender en feilmelding til klienten
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
