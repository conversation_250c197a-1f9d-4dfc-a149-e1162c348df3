import "reflect-metadata";
import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index, BeforeInsert } from "typeorm";

@Entity('Fagkodeeksamen')
@Index(['Fagkode', 'Variantkode', 'Eksamensperiode'], { unique: true })
export class Fagkodeeksamen {
    @PrimaryColumn({ type: 'nvarchar', length: 100 })
    FagkodeeksamensID!: string;

    @Column({ type: 'nvarchar', length: 50 })
    Fagkode!: string;

    @Column({ type: 'nvarchar', length: 50, nullable: true })
    Variantkode?: string;

    @Column({ type: 'nvarchar', length: 50 })
    Eksamensperiode!: string;

    @Column({ type: 'nvarchar', length: 255 })
    Fagnavn!: string;

    @Column({ type: 'int', nullable: true })
    Varighet?: number | null;

    @Column({ type: 'date', nullable: true })
    Eksamensdato?: Date | null;

    @Column({ type: 'nvarchar', length: 10, nullable: true })
    Eksamenstid?: string | null;

    @Column({ type: 'bit', default: false })
    ErTestFagkode!: boolean;

    @CreateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    CreatedDate!: Date;

    @UpdateDateColumn({ type: 'datetime2', default: () => 'GETDATE()' })
    ModifiedDate!: Date;

    // Lazy import to avoid circular dependency
    @OneToMany("Eksamensdel", (eksamensdel: any) => eksamensdel.fagkodeeksamen)
    eksamensdeler!: any[];

    @BeforeInsert()
    generateId() {
        // Generate composite ID: ExamPeriod + Fagkode + Variant (if variant exists)
        // Examples:
        // - MAT0015 without variant -> "H-2025_MAT0015"
        // - MAT0015-SMA without variant -> "H-2025_MAT0015-SMA"  
        // - REA3049 with variant DIV -> "H-2025_REA3049_DIV"
        const variantPart = this.Variantkode ? `_${this.Variantkode}` : '';
        this.FagkodeeksamensID = `${this.Eksamensperiode}_${this.Fagkode}${variantPart}`;
    }
}
