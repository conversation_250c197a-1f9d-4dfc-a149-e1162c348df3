import { getServerSession } from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "../auth/authOptions";
import { ISession } from "@/interface/ISession";
import { getAccessibleMenuItems } from "@/app/lib/accessControl";
import { getAppInsightsServer } from "@/lib/appInsightsServer";

export const dynamic = "force-dynamic";

const telemetryClient = getAppInsightsServer();

export async function GET(request: NextRequest) {
  //TODO: Hva skal vi gjøre hvis det trynner her?
  //return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  try {
    const session: ISession | null = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const encodedRole = searchParams.get("role");

    if (!encodedRole) {
      return NextResponse.json(
        { error: "Role not specified" },
        { status: 400 }
      );
    }

    const rolename = decodeURIComponent(encodedRole);

    const userSessionData = (await getServerSession(authOptions)) as ISession;

    if (!userSessionData || !userSessionData.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has the specified role
    const userRoles = Array.isArray(userSessionData.user.role)
      ? userSessionData.user.role
      : [userSessionData.user.role];

    /*if (!userRoles.includes(rolename)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }*/

    const menuItems = await getAccessibleMenuItems(rolename);

    return NextResponse.json(menuItems);
  } catch (error) {
    telemetryClient?.trackException({
      exception: error as Error,
      properties: {
        action: "getMenuItems",
      },
    });

    console.error("Error fetching menu items:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
