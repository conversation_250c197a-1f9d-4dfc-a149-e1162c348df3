import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import React from "react";
import dayjs from "dayjs";
import { IActivityLog } from "@/interface/IActivitylog";

export const columns: ColumnDef<IActivityLog>[] = [
  {
    accessorKey: "rowNumber",
    header: ({ column }: { column: any }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="capitalize">{row.index + 1}</div>;
    },
    enableSorting: true,
    sortingFn: (a, b) => a.index - b.index,
  },
  {
    accessorKey: "timestamp",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Tid
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="">{dayjs(row.original.timestamp).format("HH:mm")}</div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "activity",
    header: ({ column }) => {
      return (
        <div className="">
          <Button
            variant="ghost"
            className="p-0 hover:bg-transparent"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Aktivitet
            <ArrowUpDown
              className="ml-2 h-4 w-4"
              role="img"
              aria-label="opp-ned pil ikon"
            />
          </Button>
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <>
          <div className="text-wrap">{row.original.activity}</div>
        </>
      );
    },
    enableSorting: true,
  },

  {
    accessorKey: "ipAddress",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          IP-adresse
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <>
          <div>{row.original.ipAddress}</div>
        </>
      );
    },
    enableSorting: true,
  },

  {
    accessorKey: "userType",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0 hover:bg-transparent"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Person{" "}
          <ArrowUpDown
            className="ml-2 h-4 w-4"
            role="img"
            aria-label="opp-ned pil ikon"
          />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <>
          <div>{row.original.userType}</div>
        </>
      );
    },
    enableSorting: true,
  },
];

export default columns;
