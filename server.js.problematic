const { createServer } = require("http");
const { parse } = require("url");
const next = require("next");

const port = process.env.PORT || 4000;
const dev = process.env.NODE_ENV !== "production";

// Use standalone server in production
if (!dev) {
  // In production with standalone build, use the generated server
  const path = require("path");
  const standaloneServer = path.join(__dirname, ".next/standalone/server.js");
  
  if (require("fs").existsSync(standaloneServer)) {
    console.log("Starting with Next.js standalone server");
    require(standaloneServer);
    return;
  }
}

// Fallback to regular Next.js server for development or if standalone not found
const app = next({ dev });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://localhost:${port}`);
  });
});
